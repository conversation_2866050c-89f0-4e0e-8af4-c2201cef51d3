import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_hybrid_app/services/cache_service.dart';
import 'package:flutter_hybrid_app/services/api_service.dart';

void main() {
  group('工作台缓存测试', () {
    setUp(() async {
      // 清除所有缓存
      SharedPreferences.setMockInitialValues({});
      await CacheService.clearAllCache();
    });

    test('应该能够保存和获取工作台缓存数据', () async {
      // 准备测试数据
      final testData = {
        'frequentApps': [
          {
            'id': 1,
            'name': '测试应用1',
            'icon': 'test_icon',
            'color': '0xFF3366CC',
          },
          {
            'id': 2,
            'name': '测试应用2',
            'icon': 'test_icon2',
            'color': '0xFFFF9900',
          },
        ],
      };

      // 保存数据到缓存
      await CacheService.saveWorkspaceData(testData);

      // 从缓存获取数据
      final cachedData = await CacheService.getWorkspaceData();

      // 验证数据
      expect(cachedData, isNotNull);
      expect(cachedData!['frequentApps'], isA<List>());
      expect((cachedData['frequentApps'] as List).length, equals(2));
      expect((cachedData['frequentApps'] as List)[0]['name'], equals('测试应用1'));
      expect((cachedData['frequentApps'] as List)[1]['name'], equals('测试应用2'));
    });

    test('应该能够检查工作台缓存是否存在', () async {
      // 初始状态应该没有缓存
      expect(await CacheService.hasWorkspaceCache(), isFalse);

      // 保存数据后应该有缓存
      await CacheService.saveWorkspaceData({
        'frequentApps': [
          {'id': 1, 'name': '测试应用', 'icon': 'test', 'color': '0xFF000000'}
        ],
      });

      expect(await CacheService.hasWorkspaceCache(), isTrue);
    });

    test('应该能够清除工作台缓存', () async {
      // 先保存数据
      await CacheService.saveWorkspaceData({
        'frequentApps': [
          {'id': 1, 'name': '测试应用', 'icon': 'test', 'color': '0xFF000000'}
        ],
      });

      // 确认有缓存
      expect(await CacheService.hasWorkspaceCache(), isTrue);

      // 清除缓存
      await CacheService.clearWorkspaceCache();

      // 确认缓存已清除
      expect(await CacheService.hasWorkspaceCache(), isFalse);
      expect(await CacheService.getWorkspaceData(), isNull);
    });

    test('工作台API应该保存数据到缓存', () async {
      // 初始化API服务
      await ApiService.initialize();

      // 调用工作台API（使用模拟数据）
      final result = await ApiService.getWorkspaceApps(useMockData: true);

      // 验证API调用成功
      expect(result['success'], isTrue);
      expect(result['data'], isNotNull);

      // 验证数据已保存到缓存
      expect(await CacheService.hasWorkspaceCache(), isTrue);

      final cachedData = await CacheService.getWorkspaceData();
      expect(cachedData, isNotNull);
      expect(cachedData!['favoriteApps'], isA<List>());
      expect((cachedData['favoriteApps'] as List).isNotEmpty, isTrue);
    });

    test('缓存过期后应该返回null', () async {
      // 保存数据到缓存
      await CacheService.saveWorkspaceData({
        'frequentApps': [
          {'id': 1, 'name': '测试应用', 'icon': 'test', 'color': '0xFF000000'}
        ],
      });

      // 验证缓存存在
      expect(await CacheService.getWorkspaceData(), isNotNull);

      // 模拟缓存过期（通过直接修改时间戳）
      final prefs = await SharedPreferences.getInstance();
      final expiredTimestamp = DateTime.now().millisecondsSinceEpoch - (25 * 60 * 60 * 1000); // 25小时前
      await prefs.setInt('workspace_data_timestamp', expiredTimestamp);

      // 验证过期缓存返回null
      expect(await CacheService.getWorkspaceData(), isNull);
      expect(await CacheService.hasWorkspaceCache(), isFalse);
    });

    test('forceRefresh参数应该跳过缓存直接调用API', () async {
      // 先保存一些缓存数据
      await CacheService.saveWorkspaceData({
        'frequentApps': [
          {'id': 1, 'name': '缓存应用', 'icon': 'cached', 'color': '0xFF000000'}
        ],
      });

      // 验证缓存存在
      expect(await CacheService.hasWorkspaceCache(), isTrue);

      // 初始化API服务
      await ApiService.initialize();

      // 不使用forceRefresh，应该返回缓存数据
      final cachedResult = await ApiService.getWorkspaceApps(useMockData: true);
      expect(cachedResult['success'], isTrue);
      expect(cachedResult['fromCache'], isTrue);
      expect(cachedResult['data']['frequentApps'][0]['name'], equals('缓存应用'));

      // 使用forceRefresh，应该调用API并返回新数据
      final freshResult = await ApiService.getWorkspaceApps(
        forceRefresh: true,
        useMockData: true,
      );
      expect(freshResult['success'], isTrue);
      expect(freshResult['fromCache'], isNull); // 不是从缓存获取的
      expect(freshResult['data']['favoriteApps'][0]['name'], equals('日程管理')); // API返回的数据
    });
  });
}
