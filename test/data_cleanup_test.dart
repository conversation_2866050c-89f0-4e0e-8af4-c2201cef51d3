import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../lib/services/data_cleanup_service.dart';
import '../lib/services/auth_service.dart';
import '../lib/services/cache_service.dart';
import '../lib/services/api_service.dart';
import '../lib/constants/app_constants.dart';

void main() {
  group('数据清理功能测试', () {
    late SharedPreferences prefs;
    late DataCleanupService cleanupService;
    late AuthService authService;

    setUp(() async {
      // 设置测试环境
      SharedPreferences.setMockInitialValues({});
      prefs = await SharedPreferences.getInstance();
      cleanupService = DataCleanupService();
      authService = AuthService();
    });

    tearDown(() async {
      // 清理测试环境
      await prefs.clear();
    });

    test('应该能够创建测试数据', () async {
      // 创建各种类型的测试数据
      await prefs.setString('user_token', 'test_token_123');
      await prefs.setString('user_info', '{"id": 1, "name": "test_user"}');
      await prefs.setString('login_time', DateTime.now().toIso8601String());
      await prefs.setBool('session_valid', true);
      
      // 创建用户个人数据
      await prefs.setString('test_user_data', 'test_value');
      await prefs.setString('custom_setting', 'custom_value');
      
      // 创建缓存数据
      await prefs.setString('home_data', '{"test": "home"}');
      await prefs.setInt('home_data_timestamp', DateTime.now().millisecondsSinceEpoch);
      await prefs.setString('apps_data', '{"test": "apps"}');
      await prefs.setInt('apps_data_timestamp', DateTime.now().millisecondsSinceEpoch);
      
      // 创建日志数据
      await prefs.setString('debug_logs', '[]');
      await prefs.setBool('debug_log_enabled', true);
      
      // 创建API设置
      await prefs.setBool('use_mock_data', true);
      
      // 创建应该保留的数据
      await prefs.setString(AppConstants.keyUsername, 'test_username');
      await prefs.setString(AppConstants.keyPassword, 'test_password');
      await prefs.setBool(AppConstants.keyRememberPassword, true);
      await prefs.setString('selected_theme', 'dark');
      await prefs.setString('selected_language', 'en');
      await prefs.setString(AppConstants.keyServerAddress, 'localhost');
      await prefs.setString(AppConstants.keyServerPort, '8080');

      // 验证数据已创建
      expect(prefs.getString('user_token'), equals('test_token_123'));
      expect(prefs.getString('user_info'), isNotNull);
      expect(prefs.getBool('session_valid'), isTrue);
      expect(prefs.getString('test_user_data'), equals('test_value'));
      expect(prefs.getString(AppConstants.keyUsername), equals('test_username'));
    });

    test('应该能够清除所有用户数据但保留登录页面信息', () async {
      // 先创建测试数据
      await prefs.setString('user_token', 'test_token_123');
      await prefs.setString('user_info', '{"id": 1, "name": "test_user"}');
      await prefs.setString('login_time', DateTime.now().toIso8601String());
      await prefs.setBool('session_valid', true);
      await prefs.setString('test_user_data', 'test_value');
      await prefs.setString('home_data', '{"test": "home"}');
      await prefs.setInt('home_data_timestamp', DateTime.now().millisecondsSinceEpoch);
      await prefs.setString('debug_logs', '[]');
      await prefs.setBool('use_mock_data', true);
      
      // 创建应该保留的数据
      await prefs.setString(AppConstants.keyUsername, 'test_username');
      await prefs.setString(AppConstants.keyPassword, 'test_password');
      await prefs.setBool(AppConstants.keyRememberPassword, true);
      await prefs.setString('selected_theme', 'dark');
      await prefs.setString('selected_language', 'en');
      await prefs.setString(AppConstants.keyServerAddress, 'localhost');

      // 执行数据清理
      await cleanupService.clearAllUserData();

      // 验证用户数据已被清除
      expect(prefs.getString('user_token'), isNull);
      expect(prefs.getString('user_info'), isNull);
      expect(prefs.getString('login_time'), isNull);
      expect(prefs.getBool('session_valid'), isFalse);
      expect(prefs.getString('test_user_data'), isNull);
      expect(prefs.getString('home_data'), isNull);
      expect(prefs.getInt('home_data_timestamp'), isNull);
      expect(prefs.getString('debug_logs'), isNull);
      expect(prefs.getBool('use_mock_data'), isNull);

      // 验证登录页面信息被保留
      expect(prefs.getString(AppConstants.keyUsername), equals('test_username'));
      expect(prefs.getString(AppConstants.keyPassword), equals('test_password'));
      expect(prefs.getBool(AppConstants.keyRememberPassword), isTrue);
      expect(prefs.getString('selected_theme'), equals('dark'));
      expect(prefs.getString('selected_language'), equals('en'));
      expect(prefs.getString(AppConstants.keyServerAddress), equals('localhost'));
    });

    test('应该能够通过AuthService清除所有用户数据', () async {
      // 创建测试数据
      await prefs.setString('user_token', 'test_token_456');
      await prefs.setString('user_info', '{"id": 2, "name": "test_user2"}');
      await prefs.setBool('session_valid', true);
      await prefs.setString(AppConstants.keyUsername, 'saved_username');
      await prefs.setBool(AppConstants.keyRememberPassword, true);

      // 通过AuthService执行清理
      await authService.clearAllUserData();

      // 验证认证数据已清除
      expect(prefs.getString('user_token'), isNull);
      expect(prefs.getString('user_info'), isNull);
      expect(prefs.getBool('session_valid'), isFalse);

      // 验证登录信息被保留
      expect(prefs.getString(AppConstants.keyUsername), equals('saved_username'));
      expect(prefs.getBool(AppConstants.keyRememberPassword), isTrue);
    });

    test('应该能够获取保留数据信息', () async {
      // 创建测试数据
      await prefs.setString(AppConstants.keyUsername, 'test_user');
      await prefs.setBool(AppConstants.keyRememberPassword, true);
      await prefs.setString(AppConstants.keyServerAddress, 'test.server.com');
      await prefs.setString(AppConstants.keyServerPort, '9090');
      await prefs.setString('selected_theme', 'light');
      await prefs.setString('selected_language', 'zh');

      // 获取保留数据信息
      final retainedData = await cleanupService.getRetainedDataInfo();

      // 验证返回的信息
      expect(retainedData['username'], equals('test_user'));
      expect(retainedData['rememberPassword'], isTrue);
      expect(retainedData['serverAddress'], equals('test.server.com'));
      expect(retainedData['serverPort'], equals('9090'));
      expect(retainedData['theme'], equals('light'));
      expect(retainedData['language'], equals('zh'));
    });

    test('应该能够获取数据统计信息', () async {
      // 创建各种类型的测试数据
      await prefs.setString('user_token', 'token');
      await prefs.setString('user_info', 'info');
      await prefs.setString('home_data', 'data');
      await prefs.setInt('home_data_timestamp', 123456);
      await prefs.setString('test_data_expiry', '789012');
      await prefs.setString('debug_logs', 'logs');
      await prefs.setString('other_key', 'value');

      // 获取统计信息
      final stats = await cleanupService.getClearedDataStats();

      // 验证统计信息
      expect(stats['totalKeys'], greaterThan(0));
      expect(stats.containsKey('cacheKeys'), isTrue);
      expect(stats.containsKey('authKeys'), isTrue);
      expect(stats.containsKey('logKeys'), isTrue);
      expect(stats.containsKey('otherKeys'), isTrue);
    });

    test('清理过程应该能够处理异常情况', () async {
      // 测试在没有数据的情况下清理
      expect(() => cleanupService.clearAllUserData(), returnsNormally);
      
      // 测试在部分数据缺失的情况下清理
      await prefs.setString('user_token', 'token');
      expect(() => cleanupService.clearAllUserData(), returnsNormally);
    });

    test('应该能够清理缓存相关的键值', () async {
      // 创建各种缓存键值
      await prefs.setString('test_data', 'value');
      await prefs.setString('test_data_expiry', '123456');
      await prefs.setString('another_cache', 'cache_value');
      await prefs.setInt('another_cache_timestamp', 789012);
      await prefs.setString('normal_key', 'normal_value');

      // 执行清理
      await cleanupService.clearAllUserData();

      // 验证缓存相关键值被清除
      expect(prefs.getString('test_data'), isNull);
      expect(prefs.getString('test_data_expiry'), isNull);
      expect(prefs.getString('another_cache'), isNull);
      expect(prefs.getInt('another_cache_timestamp'), isNull);
    });
  });
}
