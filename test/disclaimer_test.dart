import 'package:flutter_test/flutter_test.dart';

void main() {
  group('免责声明翻译测试', () {
    test('免责声明中文翻译应该存在', () {
      // 直接测试翻译映射
      const chineseTranslations = {
        'disclaimer_title': '免责声明',
        'disclaimer_content': '本软件仅为应用程序外壳，不包含任何实际业务内容。所有显示的数据均来自API接口，本软件不对数据的准确性、完整性或可靠性承担任何责任。使用本软件即表示您同意自行承担使用风险。',
      };

      // 验证中文翻译
      expect(chineseTranslations['disclaimer_title'], equals('免责声明'));
      expect(chineseTranslations['disclaimer_content'], contains('本软件仅为应用程序外壳'));
      expect(chineseTranslations['disclaimer_content'], contains('不包含任何实际业务内容'));
      expect(chineseTranslations['disclaimer_content'], contains('所有显示的数据均来自API接口'));
    });

    test('免责声明英文翻译应该存在', () {
      // 直接测试翻译映射
      const englishTranslations = {
        'disclaimer_title': 'Disclaimer',
        'disclaimer_content': 'This software is only an application shell and does not contain any actual business content. All displayed data comes from API interfaces. This software does not assume any responsibility for the accuracy, completeness, or reliability of the data. By using this software, you agree to assume the risks of use at your own discretion.',
      };

      // 验证英文翻译
      expect(englishTranslations['disclaimer_title'], equals('Disclaimer'));
      expect(englishTranslations['disclaimer_content'], contains('This software is only an application shell'));
      expect(englishTranslations['disclaimer_content'], contains('does not contain any actual business content'));
      expect(englishTranslations['disclaimer_content'], contains('All displayed data comes from API interfaces'));
    });

    test('免责声明翻译应该不同', () {
      const chineseTitle = '免责声明';
      const englishTitle = 'Disclaimer';

      const chineseContent = '本软件仅为应用程序外壳，不包含任何实际业务内容。所有显示的数据均来自API接口，本软件不对数据的准确性、完整性或可靠性承担任何责任。使用本软件即表示您同意自行承担使用风险。';
      const englishContent = 'This software is only an application shell and does not contain any actual business content. All displayed data comes from API interfaces. This software does not assume any responsibility for the accuracy, completeness, or reliability of the data. By using this software, you agree to assume the risks of use at your own discretion.';

      // 验证翻译不同
      expect(chineseTitle, isNot(equals(englishTitle)));
      expect(chineseContent, isNot(equals(englishContent)));
    });
  });
}