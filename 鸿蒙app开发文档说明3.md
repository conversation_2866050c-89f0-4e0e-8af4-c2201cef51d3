# 鸿蒙企业移动办公系统开发文档 v2.0 (续3)

## 9. 安全架构设计 (续)

### 9.2 网络安全

#### 9.2.1 HTTPS通信
**SSL/TLS配置**:
- 强制HTTPS通信
- 证书验证机制
- 证书固定(Certificate Pinning)
- 双向认证支持

**网络安全实现**:
```typescript
class SecureHttpClient {
  private static readonly TIMEOUT = 30000;
  private static readonly MAX_RETRIES = 3;

  static async request(config: RequestConfig): Promise<any> {
    // 添加安全头
    const secureHeaders = {
      ...config.headers,
      'X-Requested-With': 'XMLHttpRequest',
      'X-App-Version': AppConfig.version,
      'X-Device-ID': DeviceUtil.getDeviceId(),
    };

    // 添加Token认证
    const token = await TokenManager.getToken();
    if (token) {
      secureHeaders['Authorization'] = `Bearer ${token}`;
    }

    // 请求签名
    const signature = this.generateSignature(config);
    secureHeaders['X-Signature'] = signature;

    return this.executeRequest({
      ...config,
      headers: secureHeaders,
      timeout: this.TIMEOUT,
    });
  }

  private static generateSignature(config: RequestConfig): string {
    // 生成请求签名，防止请求篡改
    const timestamp = Date.now().toString();
    const nonce = this.generateNonce();
    const data = `${config.method}${config.url}${timestamp}${nonce}`;
    return CryptoUtil.hmacSha256(data, AppConfig.secretKey);
  }

  private static generateNonce(): string {
    return Math.random().toString(36).substring(2, 15);
  }
}
```

#### 9.2.2 API安全防护
**请求防护机制**:
- 请求频率限制
- 请求签名验证
- 防重放攻击
- SQL注入防护

**安全中间件**:
```typescript
class SecurityMiddleware {
  // 请求频率限制
  static rateLimiter = new Map<string, number[]>();

  static checkRateLimit(endpoint: string, limit: number = 100): boolean {
    const now = Date.now();
    const windowMs = 60000; // 1分钟窗口

    if (!this.rateLimiter.has(endpoint)) {
      this.rateLimiter.set(endpoint, []);
    }

    const requests = this.rateLimiter.get(endpoint)!;
    // 清理过期请求
    const validRequests = requests.filter(time => now - time < windowMs);

    if (validRequests.length >= limit) {
      return false; // 超过限制
    }

    validRequests.push(now);
    this.rateLimiter.set(endpoint, validRequests);
    return true;
  }

  // 输入验证
  static validateInput(data: any, schema: ValidationSchema): boolean {
    // 实现输入验证逻辑
    return InputValidator.validate(data, schema);
  }

  // XSS防护
  static sanitizeInput(input: string): string {
    return input
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#x27;')
      .replace(/\//g, '&#x2F;');
  }
}
```

### 9.3 权限管理

#### 9.3.1 权限模型设计
**权限体系**:
- 基于角色的访问控制(RBAC)
- 功能权限控制
- 数据权限控制
- 操作权限控制

**权限管理实现**:
```typescript
interface Permission {
  id: string;
  name: string;
  resource: string;
  action: string;
  conditions?: Record<string, any>;
}

interface Role {
  id: string;
  name: string;
  permissions: Permission[];
}

interface User {
  id: string;
  roles: Role[];
  permissions: Permission[];
}

class PermissionManager {
  private static userPermissions: Set<string> = new Set();

  static async loadUserPermissions(userId: string): Promise<void> {
    try {
      const response = await UserService.getUserPermissions(userId);
      this.userPermissions = new Set(response.permissions);
    } catch (error) {
      console.error('Failed to load user permissions:', error);
    }
  }

  static hasPermission(permission: string): boolean {
    return this.userPermissions.has(permission);
  }

  static hasAnyPermission(permissions: string[]): boolean {
    return permissions.some(permission => this.hasPermission(permission));
  }

  static hasAllPermissions(permissions: string[]): boolean {
    return permissions.every(permission => this.hasPermission(permission));
  }

  static checkResourceAccess(resource: string, action: string): boolean {
    const permission = `${resource}:${action}`;
    return this.hasPermission(permission);
  }
}
```

#### 9.3.2 页面权限控制
**路由权限守卫**:
```typescript
class RouteGuard {
  static canActivate(route: RouteConfig): boolean {
    // 检查登录状态
    if (route.requiresAuth && !AuthService.isLoggedIn()) {
      Router.navigateTo('/login');
      return false;
    }

    // 检查权限
    if (route.permissions && route.permissions.length > 0) {
      if (!PermissionManager.hasAnyPermission(route.permissions)) {
        Router.navigateTo('/unauthorized');
        return false;
      }
    }

    // 检查角色
    if (route.roles && route.roles.length > 0) {
      const userRoles = AuthService.getCurrentUser()?.roles || [];
      const hasRole = route.roles.some(role =>
        userRoles.some(userRole => userRole.name === role)
      );

      if (!hasRole) {
        Router.navigateTo('/unauthorized');
        return false;
      }
    }

    return true;
  }
}
```

### 9.4 代码安全

#### 9.4.1 代码混淆
**混淆策略**:
- 变量名混淆
- 函数名混淆
- 字符串加密
- 控制流混淆

#### 9.4.2 反调试保护
**保护机制**:
- 调试器检测
- 模拟器检测
- Root/越狱检测
- Hook检测

```typescript
class SecurityChecker {
  static isDebuggerAttached(): boolean {
    // 检测调试器
    return false; // 实际实现需要使用原生能力
  }

  static isEmulator(): boolean {
    // 检测模拟器
    return false; // 实际实现需要使用原生能力
  }

  static isRooted(): boolean {
    // 检测Root状态
    return false; // 实际实现需要使用原生能力
  }

  static performSecurityCheck(): boolean {
    if (this.isDebuggerAttached()) {
      console.warn('Debugger detected');
      return false;
    }

    if (this.isEmulator()) {
      console.warn('Emulator detected');
      return false;
    }

    if (this.isRooted()) {
      console.warn('Rooted device detected');
      return false;
    }

    return true;
  }
}
```

---

## 10. 测试策略与质量保证

### 10.1 测试金字塔

#### 10.1.1 单元测试
**测试范围**:
- 工具类函数测试
- 数据模型测试
- 业务逻辑测试
- 状态管理测试

**测试实现示例**:
```typescript
// 工具类测试
describe('EncryptionUtil', () => {
  test('should encrypt and decrypt data correctly', () => {
    const originalData = 'test data';
    const key = 'test key';

    const encrypted = EncryptionUtil.encrypt(originalData, key);
    const decrypted = EncryptionUtil.decrypt(encrypted, key);

    expect(decrypted).toBe(originalData);
  });

  test('should generate different encrypted data for same input', () => {
    const data = 'test data';
    const key = 'test key';

    const encrypted1 = EncryptionUtil.encrypt(data, key);
    const encrypted2 = EncryptionUtil.encrypt(data, key);

    expect(encrypted1).not.toBe(encrypted2);
  });
});

// 状态管理测试
describe('UserStateManager', () => {
  let userStateManager: UserStateManager;

  beforeEach(() => {
    userStateManager = new UserStateManager();
  });

  test('should initialize with logged out state', () => {
    const state = userStateManager.getState();
    expect(state.isLoggedIn).toBe(false);
    expect(state.userInfo).toBeNull();
  });

  test('should update state after successful login', async () => {
    const mockUser = { id: '1', name: 'Test User' };
    const mockResponse = {
      success: true,
      data: { user: mockUser, token: 'test-token' }
    };

    jest.spyOn(AuthService, 'login').mockResolvedValue(mockResponse);

    await userStateManager.login({ username: 'test', password: 'test' });

    const state = userStateManager.getState();
    expect(state.isLoggedIn).toBe(true);
    expect(state.userInfo).toEqual(mockUser);
  });
});
```

#### 10.1.2 集成测试
**测试场景**:
- API接口集成测试
- 数据库操作测试
- 第三方服务集成测试
- 模块间交互测试

**集成测试示例**:
```typescript
describe('AuthService Integration', () => {
  test('should complete login flow successfully', async () => {
    // 模拟网络请求
    const mockResponse = {
      success: true,
      data: {
        token: 'mock-token',
        user: { id: '1', name: 'Test User' }
      }
    };

    jest.spyOn(HttpClient, 'post').mockResolvedValue(mockResponse);
    jest.spyOn(TokenManager, 'saveToken').mockResolvedValue();

    const result = await AuthService.login({
      username: 'test',
      password: 'test'
    });

    expect(result.success).toBe(true);
    expect(TokenManager.saveToken).toHaveBeenCalledWith('mock-token');
  });
});
```

#### 10.1.3 UI测试
**测试工具**:
- 鸿蒙官方测试框架
- 自动化UI测试
- 截图对比测试
- 性能测试

**UI测试示例**:
```typescript
describe('LoginPage UI Tests', () => {
  test('should display login form correctly', async () => {
    const driver = await startApp();

    // 检查登录页面元素
    const usernameInput = await driver.findComponent(By.key('username-input'));
    const passwordInput = await driver.findComponent(By.key('password-input'));
    const loginButton = await driver.findComponent(By.key('login-button'));

    expect(usernameInput).toBeDefined();
    expect(passwordInput).toBeDefined();
    expect(loginButton).toBeDefined();
  });

  test('should show error message for invalid credentials', async () => {
    const driver = await startApp();

    // 输入无效凭据
    await driver.inputText(By.key('username-input'), 'invalid');
    await driver.inputText(By.key('password-input'), 'invalid');
    await driver.click(By.key('login-button'));

    // 检查错误消息
    const errorMessage = await driver.findComponent(By.key('error-message'));
    expect(errorMessage).toBeDefined();
  });
});
```

### 10.2 自动化测试

#### 10.2.1 持续集成测试
**CI/CD流程**:
- 代码提交触发测试
- 自动化测试执行
- 测试报告生成
- 质量门禁检查

**测试配置**:
```yaml
# .github/workflows/test.yml
name: Test

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v2

    - name: Setup Node.js
      uses: actions/setup-node@v2
      with:
        node-version: '16'

    - name: Install dependencies
      run: npm install

    - name: Run unit tests
      run: npm run test:unit

    - name: Run integration tests
      run: npm run test:integration

    - name: Generate test report
      run: npm run test:report

    - name: Upload coverage
      uses: codecov/codecov-action@v1
```

#### 10.2.2 性能测试
**性能指标**:
- 启动时间测试
- 页面渲染性能测试
- 内存使用测试
- 网络请求性能测试

**性能测试实现**:
```typescript
describe('Performance Tests', () => {
  test('app startup time should be less than 3 seconds', async () => {
    const startTime = Date.now();

    await startApp();
    await waitForElement(By.key('main-screen'));

    const endTime = Date.now();
    const startupTime = endTime - startTime;

    expect(startupTime).toBeLessThan(3000);
  });

  test('page navigation should be smooth', async () => {
    const driver = await startApp();

    const startTime = Date.now();
    await driver.click(By.key('apps-tab'));
    await waitForElement(By.key('apps-screen'));
    const endTime = Date.now();

    const navigationTime = endTime - startTime;
    expect(navigationTime).toBeLessThan(300);
  });
});
```

### 10.3 质量保证流程

#### 10.3.1 代码质量检查
**静态代码分析**:
- ESLint代码规范检查
- TypeScript类型检查
- 代码复杂度分析
- 安全漏洞扫描

**代码审查流程**:
- Pull Request必须经过审查
- 自动化检查必须通过
- 测试覆盖率要求达标
- 性能指标符合要求

#### 10.3.2 测试覆盖率要求
**覆盖率目标**:
- 单元测试覆盖率：≥80%
- 集成测试覆盖率：≥60%
- 关键路径覆盖率：100%
- 分支覆盖率：≥70%

**覆盖率监控**:
```typescript
// jest.config.js
module.exports = {
  collectCoverage: true,
  coverageDirectory: 'coverage',
  coverageReporters: ['text', 'lcov', 'html'],
  coverageThreshold: {
    global: {
      branches: 70,
      functions: 80,
      lines: 80,
      statements: 80
    }
  }
};
```

---

## 11. 国际化与本地化

### 11.1 多语言支持

#### 11.1.1 语言资源管理
**支持语言**:
- 简体中文 (zh-CN)
- 英语 (en-US)
- 繁体中文 (zh-TW) - 可选
- 日语 (ja-JP) - 可选

**资源文件结构**:
```
resources/
├── base/
│   ├── element/
│   │   ├── string.json
│   │   ├── color.json
│   │   └── float.json
│   └── media/
├── zh_CN/
│   ├── element/
│   │   └── string.json
│   └── media/
├── en_US/
│   ├── element/
│   │   └── string.json
│   └── media/
└── zh_TW/
    ├── element/
    │   └── string.json
    └── media/
```

#### 11.1.2 国际化实现
**资源管理器**:
```typescript
class I18nManager {
  private static currentLocale: string = 'zh-CN';
  private static resources: Map<string, Record<string, string>> = new Map();

  static async init(): Promise<void> {
    // 获取系统语言
    const systemLocale = await this.getSystemLocale();
    this.currentLocale = this.getSupportedLocale(systemLocale);

    // 加载语言资源
    await this.loadResources(this.currentLocale);
  }

  static async setLocale(locale: string): Promise<void> {
    if (this.currentLocale === locale) return;

    this.currentLocale = locale;
    await this.loadResources(locale);

    // 保存用户选择
    await PreferencesManager.setString('user_locale', locale);

    // 通知应用更新
    EventBus.getInstance().emit('locale_changed', locale);
  }

  static getString(key: string, params?: Record<string, string>): string {
    const resources = this.resources.get(this.currentLocale);
    let text = resources?.[key] || key;

    // 参数替换
    if (params) {
      Object.keys(params).forEach(param => {
        text = text.replace(`{${param}}`, params[param]);
      });
    }

    return text;
  }

  static getFormattedString(key: string, ...args: any[]): string {
    const template = this.getString(key);
    return template.replace(/{(\d+)}/g, (match, index) => {
      return args[index] !== undefined ? args[index] : match;
    });
  }

  private static async loadResources(locale: string): Promise<void> {
    try {
      const resourcePath = `resources/${locale}/element/string.json`;
      const resources = await ResourceManager.loadJson(resourcePath);
      this.resources.set(locale, resources);
    } catch (error) {
      console.error(`Failed to load resources for locale: ${locale}`, error);
      // 回退到默认语言
      if (locale !== 'zh-CN') {
        await this.loadResources('zh-CN');
      }
    }
  }

  private static getSupportedLocale(locale: string): string {
    const supportedLocales = ['zh-CN', 'en-US', 'zh-TW', 'ja-JP'];
    return supportedLocales.includes(locale) ? locale : 'zh-CN';
  }

  private static async getSystemLocale(): Promise<string> {
    // 获取系统语言设置
    return 'zh-CN'; // 实际实现需要调用系统API
  }
}
```