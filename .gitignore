# Flutter/Dart 相关文件
# 构建输出目录
build/
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.packages
.pub-cache/
.pub/

# Flutter 构建文件
**/build/
**/android/build/
**/ios/build/
**/web/build/
**/linux/build/
**/macos/build/
**/windows/build/

# Flutter 生成的文件
**/.flutter-plugins
**/.flutter-plugins-dependencies
**/.packages

# pubspec.lock 文件处理说明：
# - 对于应用项目：建议提交 pubspec.lock 以确保依赖版本一致性
# - 对于库项目：不提交 pubspec.lock 以允许更灵活的依赖版本
# 当前项目是应用项目，所以保留 pubspec.lock
# 如果需要忽略，取消注释下面的行：
# pubspec.lock

# IDE 相关文件
.vscode/
.idea/
*.iml
*.ipr
*.iws
.settings/
.project
.classpath

# 操作系统生成的文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 日志文件
*.log

# 临时文件
*.tmp
*.temp
*~

# 编译产物
*.apk
*.aab
*.ipa
*.app
*.dmg
*.exe
*.msi

# 密钥和证书文件
*.keystore
*.jks
*.p12
*.key
*.pem
*.crt
*.cer

# 环境配置文件（包含敏感信息）
.env
.env.local
.env.*.local
config/secrets.dart
lib/config/api_keys.dart

# 测试覆盖率报告
coverage/
test/.test_coverage.dart
lcov.info

# 文档生成文件
doc/api/

# Firebase 配置文件（如果包含敏感信息）
# 注意：通常 google-services.json 和 GoogleService-Info.plist 需要提交
# 但如果包含敏感信息，可以取消注释下面的行
# android/app/google-services.json
# ios/Runner/GoogleService-Info.plist

# 本地数据库文件
*.db
*.sqlite
*.sqlite3

# 备份文件
*.bak
*.backup

# 其他不需要的文件
.fvm/
.metadata
analysis_options.yaml.bak
