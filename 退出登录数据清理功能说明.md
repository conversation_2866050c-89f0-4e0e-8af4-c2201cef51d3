# 退出登录数据清理功能实现说明

## 功能概述

根据用户需求，实现了退出登录时清空所有用户数据信息、缓存和日志的功能，但保留登录页面的基本信息（用户名、密码、记住密码设置等）。

## 实现方案

### 1. 新增数据清理服务 (`lib/services/data_cleanup_service.dart`)

创建了专门的数据清理服务类 `DataCleanupService`，负责系统性地清理所有用户相关数据。

#### 主要功能：
- **完全清除用户数据**：`clearAllUserData()` 方法
- **分类清理**：按数据类型分别清理认证数据、缓存数据、日志数据等
- **智能保留**：自动保留登录页面需要的基本信息
- **统计功能**：提供数据清理前后的统计信息

#### 清理的数据类型：

1. **认证相关数据**
   - 用户Token (`user_token`)
   - 用户信息 (`user_info`)
   - 登录时间 (`login_time`)
   - 会话状态 (`session_valid`)

2. **缓存数据**
   - 首页数据缓存 (`home_data`, `home_data_timestamp`)
   - 应用数据缓存 (`apps_data`, `apps_data_timestamp`)
   - 工作台数据缓存 (`workspace_data`, `workspace_data_timestamp`)
   - 消息数据缓存 (`messages_data`, `messages_data_timestamp`)
   - 所有通用缓存数据（以 `_expiry` 或 `_timestamp` 结尾的键值）

3. **日志数据**
   - 调试日志 (`debug_logs`)
   - 日志启用状态 (`debug_log_enabled`)

4. **API设置**
   - 模拟数据设置 (`use_mock_data`)

5. **其他用户个人数据**
   - 除保留列表外的所有用户相关数据

#### 保留的数据：

为了保持用户体验，以下数据在退出登录时**不会被清除**：

1. **登录页面信息**
   - 用户名 (`username`)
   - 密码 (`password`)
   - 记住密码设置 (`remember_password`)

2. **应用基本设置**
   - 主题设置 (`selected_theme`)
   - 语言设置 (`selected_language`)

3. **服务器配置**
   - 服务器地址 (`server_address`)
   - 服务器端口 (`server_port`)
   - 保存的服务器列表 (`saved_servers`)

### 2. 修改认证服务 (`lib/services/auth_service.dart`)

#### 新增方法：
- `clearAllUserData()`：调用 `DataCleanupService` 进行完整的数据清理
- 保留原有的 `clearLoginInfo()`：仅清理认证相关数据

#### 改进：
- 添加了对 `DataCleanupService` 的依赖
- 优化了内存缓存的清理逻辑
- 增强了错误处理机制

### 3. 修改设置页面 (`lib/screens/settings_screen.dart`)

#### 改进退出登录流程：
1. **用户确认**：显示确认对话框
2. **数据清理提示**：显示"正在清除用户数据..."的提示
3. **执行清理**：调用 `AuthService.clearAllUserData()`
4. **跳转登录页**：清理完成后跳转到登录页面
5. **结果反馈**：显示清理完成或错误提示

#### 新增翻译键：
- `clearing_user_data`：正在清除用户数据...
- `user_data_cleared`：用户数据已清除完成
- `logout_error`：退出登录时发生错误

### 4. 测试功能 (`lib/test_data_cleanup.dart`)

创建了专门的测试页面，用于验证数据清理功能：

#### 测试功能：
- **创建测试数据**：生成各种类型的测试数据
- **执行数据清理**：测试完整的清理流程
- **检查数据状态**：验证哪些数据被清除，哪些被保留
- **统计信息**：显示清理前后的数据统计

## 使用方法

### 正常使用流程：
1. 用户在设置页面点击"退出登录"按钮
2. 系统显示确认对话框
3. 用户确认后，系统开始清理数据
4. 显示清理进度提示
5. 清理完成后跳转到登录页面
6. 登录页面保留用户名、密码等基本信息

### 开发测试：
1. 导入测试页面：`import 'test_data_cleanup.dart';`
2. 在需要的地方添加测试页面入口
3. 使用测试页面验证清理功能

## 技术特点

### 1. 安全性
- 分类清理，避免误删重要数据
- 异常处理，确保清理过程的稳定性
- 保留关键配置，维持用户体验

### 2. 完整性
- 覆盖所有用户相关数据类型
- 包括内存缓存和持久化存储
- 支持动态发现和清理未知数据

### 3. 可维护性
- 独立的服务类，职责清晰
- 详细的日志记录
- 完善的错误处理机制

### 4. 用户体验
- 保留登录页面基本信息
- 清理过程有明确的用户反馈
- 支持多语言提示信息

## 注意事项

1. **数据不可恢复**：清理的数据无法恢复，请确保用户了解这一点
2. **网络状态**：清理过程不依赖网络，可在离线状态下执行
3. **性能影响**：清理过程可能需要几秒钟时间，已添加进度提示
4. **平台兼容**：在Android平台增加了额外的等待时间确保数据写入完成

## 扩展性

该实现具有良好的扩展性：

1. **新增数据类型**：在 `DataCleanupService` 中添加新的清理方法
2. **自定义保留规则**：修改 `keysToKeep` 集合
3. **清理策略**：可以根据需要实现不同级别的清理策略
4. **统计功能**：可以扩展统计信息的详细程度

## 总结

该实现完全满足了用户的需求：
- ✅ 退出登录时清空所有用户数据
- ✅ 清空缓存数据
- ✅ 清空日志数据
- ✅ 保留登录页面信息
- ✅ 提供用户友好的操作体验
- ✅ 具有良好的错误处理和扩展性
