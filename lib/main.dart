import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'constants/app_theme.dart';
import 'screens/login_screen.dart';
import 'services/localization_service.dart';
import 'services/theme_service.dart';
import 'services/app_initializer.dart';
import 'services/error_log_service.dart';
import 'services/debug_log_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // 初始化错误日志服务
  await ErrorLogService.initialize();

  // 初始化调试日志服务
  await DebugLogService().initialize();

  // 设置全局错误捕获
  _setupErrorHandling();

  // 初始化基础服务
  await LocalizationService.loadLanguage();
  await ThemeService.loadTheme();

  // 初始化网络相关服务
  await AppInitializer.initialize();

  runApp(const MyApp());
}

/// 设置全局错误处理
void _setupErrorHandling() {
  // 捕获Flutter框架错误
  FlutterError.onError = (FlutterErrorDetails details) {
    // 在调试模式下仍然打印到控制台
    if (kDebugMode) {
      FlutterError.presentError(details);
    }

    // 记录到错误日志
    ErrorLogService.logFlutterError(details);
  };

  // 捕获异步错误和其他未处理的错误
  PlatformDispatcher.instance.onError = (error, stack) {
    // 在调试模式下打印错误
    if (kDebugMode) {
      debugPrint('Unhandled error: $error');
      debugPrint('Stack trace: $stack');
    }

    // 记录到错误日志
    ErrorLogService.logDartError(error, stack);

    return true; // 表示错误已被处理
  };
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();

  // Static method to rebuild the app
  static void rebuildApp() {
    _MyAppState.rebuildApp();
  }
}

class _MyAppState extends State<MyApp> {
  // Add a key to force rebuild when language changes
  static final GlobalKey<NavigatorState> navigatorKey =
      GlobalKey<NavigatorState>();

  // Static instance to allow external access
  static _MyAppState? _instance;

  // Method to rebuild the app when language changes
  static void rebuildApp() {
    _instance?.setState(() {});
  }

  @override
  void initState() {
    super.initState();
    // Set static instance for external access
    _instance = this;

    // Set up theme change callback
    ThemeService.setThemeChangeCallback(() {
      setState(() {});
    });
  }

  @override
  void dispose() {
    _instance = null;
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      navigatorKey: navigatorKey,
      title: LocalizationService.t('app_name'),
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: ThemeService.currentThemeMode,
      locale: LocalizationService.currentLocale,
      localizationsDelegates: const [
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: LocalizationService.supportedLocales,
      debugShowCheckedModeBanner: false,
      routes: {'/': (context) => const LoginScreen()},
    );
  }
}
