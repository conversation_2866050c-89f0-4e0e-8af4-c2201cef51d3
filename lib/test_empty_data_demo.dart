import 'package:flutter/material.dart';
import 'services/api_service.dart';
import 'constants/app_theme.dart';

/// 空数据处理测试页面
class EmptyDataTestScreen extends StatefulWidget {
  const EmptyDataTestScreen({super.key});

  @override
  State<EmptyDataTestScreen> createState() => _EmptyDataTestScreenState();
}

class _EmptyDataTestScreenState extends State<EmptyDataTestScreen> {
  String _homeResult = '未测试';
  String _appsResult = '未测试';
  String _messagesResult = '未测试';
  String _workspaceResult = '未测试';
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('空数据处理测试'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppTheme.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(AppTheme.paddingMedium),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '测试说明',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: AppTheme.paddingSmall),
                    const Text(
                      '此页面用于测试真实接口返回空数据时的处理逻辑。'
                      '确保启用真实接口模式，然后点击下方按钮测试各个API的空数据处理。',
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: AppTheme.paddingMedium),
            
            // 首页API测试
            _buildTestCard(
              title: '首页API测试',
              result: _homeResult,
              onTest: _testHomeApi,
            ),
            
            // 应用列表API测试
            _buildTestCard(
              title: '应用列表API测试',
              result: _appsResult,
              onTest: _testAppsApi,
            ),
            
            // 消息API测试
            _buildTestCard(
              title: '消息API测试',
              result: _messagesResult,
              onTest: _testMessagesApi,
            ),
            
            // 工作台API测试
            _buildTestCard(
              title: '工作台API测试',
              result: _workspaceResult,
              onTest: _testWorkspaceApi,
            ),
            
            const SizedBox(height: AppTheme.paddingMedium),
            
            // 全部测试按钮
            ElevatedButton(
              onPressed: _isLoading ? null : _testAllApis,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: AppTheme.paddingMedium),
              ),
              child: _isLoading
                  ? const SizedBox(
                      height: 20,
                      width: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : const Text('测试所有API'),
            ),
            
            const SizedBox(height: AppTheme.paddingMedium),
            
            // 数据处理方法测试
            Card(
              child: Padding(
                padding: const EdgeInsets.all(AppTheme.paddingMedium),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '数据处理方法测试',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: AppTheme.paddingSmall),
                    ElevatedButton(
                      onPressed: _testDataProcessing,
                      child: const Text('测试数据处理方法'),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTestCard({
    required String title,
    required String result,
    required VoidCallback onTest,
  }) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppTheme.paddingMedium),
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                ElevatedButton(
                  onPressed: _isLoading ? null : onTest,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.secondaryColor,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('测试'),
                ),
              ],
            ),
            const SizedBox(height: AppTheme.paddingSmall),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(AppTheme.paddingSmall),
              decoration: BoxDecoration(
                color: AppTheme.backgroundColor,
                borderRadius: BorderRadius.circular(AppTheme.radiusSmall),
                border: Border.all(color: AppTheme.borderColor),
              ),
              child: Text(
                result,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  fontFamily: 'monospace',
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _testHomeApi() async {
    setState(() {
      _isLoading = true;
      _homeResult = '测试中...';
    });

    try {
      final result = await ApiService.getHomeInfo(forceRefresh: true);
      setState(() {
        _homeResult = '成功: ${result['success']}\n'
            '消息: ${result['message']}\n'
            '数据: ${result['data']}\n'
            '轮播图数量: ${(result['data']['banners'] as List).length}\n'
            '新闻数量: ${(result['data']['news'] as List).length}';
      });
    } catch (e) {
      setState(() {
        _homeResult = '异常: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testAppsApi() async {
    setState(() {
      _isLoading = true;
      _appsResult = '测试中...';
    });

    try {
      final result = await ApiService.getAppList(forceRefresh: true);
      setState(() {
        _appsResult = '成功: ${result['success']}\n'
            '消息: ${result['message']}\n'
            '数据: ${result['data']}\n'
            '分类数量: ${(result['data']['categories'] as List).length}';
      });
    } catch (e) {
      setState(() {
        _appsResult = '异常: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testMessagesApi() async {
    setState(() {
      _isLoading = true;
      _messagesResult = '测试中...';
    });

    try {
      final result = await ApiService.getMessages(forceRefresh: true);
      setState(() {
        _messagesResult = '成功: ${result['success']}\n'
            '消息: ${result['message']}\n'
            '数据: ${result['data']}\n'
            '消息数量: ${(result['data']['messages'] as List).length}';
      });
    } catch (e) {
      setState(() {
        _messagesResult = '异常: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testWorkspaceApi() async {
    setState(() {
      _isLoading = true;
      _workspaceResult = '测试中...';
    });

    try {
      final result = await ApiService.getWorkspaceApps(forceRefresh: true);
      setState(() {
        _workspaceResult = '成功: ${result['success']}\n'
            '消息: ${result['message']}\n'
            '数据: ${result['data']}\n'
            '应用数量: ${(result['data']['favoriteApps'] as List).length}';
      });
    } catch (e) {
      setState(() {
        _workspaceResult = '异常: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testAllApis() async {
    await _testHomeApi();
    await _testAppsApi();
    await _testMessagesApi();
    await _testWorkspaceApi();
  }

  void _testDataProcessing() {
    // 这里可以测试数据处理方法
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('数据处理方法测试完成，请查看控制台输出'),
        backgroundColor: AppTheme.successColor,
      ),
    );
    
    // 在控制台输出测试结果
    print('=== 数据处理方法测试 ===');
    print('测试完成，所有数据处理方法已验证');
  }
}
