import 'package:flutter/foundation.dart';
import '../services/auth_service.dart';

/// 用于测试Android登录持久化的辅助工具
class AuthTestHelper {
  static final AuthService _authService = AuthService();
  
  /// 测试登录信息保存和恢复
  static Future<Map<String, dynamic>> testLoginPersistence() async {
    final results = <String, dynamic>{};
    
    try {
      // 1. 清除现有登录信息
      await _authService.clearLoginInfo();
      results['clear_success'] = true;
      
      // 2. 验证清除成功
      final isLoggedInAfterClear = await _authService.isLoggedIn();
      results['clear_verified'] = !isLoggedInAfterClear;
      
      // 3. 保存测试登录信息
      final saveSuccess = await _authService.saveLoginInfo(
        token: 'test_token_${DateTime.now().millisecondsSinceEpoch}',
        userInfo: '{"id": 1, "name": "Test User", "email": "<EMAIL>"}',
        username: 'testuser',
        password: 'testpass',
        rememberPassword: true,
      );
      results['save_success'] = saveSuccess;
      
      // 4. 立即验证保存结果
      final isLoggedInAfterSave = await _authService.isLoggedIn();
      results['save_verified'] = isLoggedInAfterSave;
      
      // 5. 获取保存的Token
      final savedToken = await _authService.getUserToken();
      results['token_retrieved'] = savedToken != null;
      results['token_value'] = savedToken;
      
      // 6. 获取保存的用户信息
      final savedUserInfo = await _authService.getUserInfo();
      results['userinfo_retrieved'] = savedUserInfo != null;
      
      // 7. 获取保存的凭据
      final credentials = await _authService.getSavedCredentials();
      results['credentials_retrieved'] = credentials['username'] == 'testuser';
      results['password_saved'] = credentials['rememberPassword'] && 
                                  credentials['password'] == 'testpass';
      
      // 8. 模拟应用重启后的状态检查
      await Future.delayed(const Duration(milliseconds: 100));
      final isStillLoggedIn = await _authService.isLoggedIn();
      results['persistence_verified'] = isStillLoggedIn;
      
      // 9. Android特定测试
      if (defaultTargetPlatform == TargetPlatform.android) {
        results['platform'] = 'android';
        
        // 多次快速读写测试
        for (int i = 0; i < 5; i++) {
          await _authService.updateSessionValidity(true);
          final isValid = await _authService.isLoggedIn();
          if (!isValid) {
            results['android_rapid_test_failed'] = i;
            break;
          }
        }
        
        // 刷新登录时间测试
        await _authService.refreshLoginTime();
        final stillValid = await _authService.isLoggedIn();
        results['android_refresh_test'] = stillValid;
      } else {
        results['platform'] = defaultTargetPlatform.toString();
      }
      
      results['overall_success'] = results['save_success'] && 
                                   results['save_verified'] && 
                                   results['persistence_verified'];
      
    } catch (e) {
      results['error'] = e.toString();
      results['overall_success'] = false;
    }
    
    return results;
  }
  
  /// 打印测试结果
  static void printTestResults(Map<String, dynamic> results) {
    debugPrint('=== 登录持久化测试结果 ===');
    debugPrint('平台: ${results['platform']}');
    debugPrint('清除成功: ${results['clear_success']}');
    debugPrint('清除验证: ${results['clear_verified']}');
    debugPrint('保存成功: ${results['save_success']}');
    debugPrint('保存验证: ${results['save_verified']}');
    debugPrint('Token获取: ${results['token_retrieved']}');
    debugPrint('用户信息获取: ${results['userinfo_retrieved']}');
    debugPrint('凭据获取: ${results['credentials_retrieved']}');
    debugPrint('密码保存: ${results['password_saved']}');
    debugPrint('持久化验证: ${results['persistence_verified']}');
    
    if (results['platform'] == 'android') {
      debugPrint('Android快速测试: ${results['android_rapid_test_failed'] == null ? '通过' : '失败于第${results['android_rapid_test_failed']}次'}');
      debugPrint('Android刷新测试: ${results['android_refresh_test']}');
    }
    
    debugPrint('总体结果: ${results['overall_success'] ? '成功' : '失败'}');
    
    if (results['error'] != null) {
      debugPrint('错误信息: ${results['error']}');
    }
    
    debugPrint('========================');
  }
  
  /// 运行完整测试
  static Future<void> runFullTest() async {
    debugPrint('开始运行登录持久化测试...');
    final results = await testLoginPersistence();
    printTestResults(results);
  }
  
  /// 清理测试数据
  static Future<void> cleanupTestData() async {
    try {
      await _authService.clearLoginInfo();
      debugPrint('测试数据清理完成');
    } catch (e) {
      debugPrint('清理测试数据失败: $e');
    }
  }
}
