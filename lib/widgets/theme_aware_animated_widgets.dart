import 'package:flutter/material.dart';
import '../constants/app_theme.dart';
import 'animated_widgets.dart';

/// 主题感知的动画按钮组件
class ThemeAwareAnimatedButton extends StatelessWidget {
  final Widget child;
  final VoidCallback? onPressed;
  final double scaleValue;

  const ThemeAwareAnimatedButton({
    super.key,
    required this.child,
    this.onPressed,
    this.scaleValue = 0.95,
  });

  @override
  Widget build(BuildContext context) {
    final duration = AppTheme.getAnimationDuration(context, defaultDuration: const Duration(milliseconds: 150));
    
    return AnimatedButton(
      onPressed: onPressed,
      duration: duration,
      scaleValue: scaleValue,
      child: child,
    );
  }
}

/// 主题感知的淡入动画组件
class ThemeAwareFadeInAnimation extends StatelessWidget {
  final Widget child;
  final Duration delay;
  final Curve curve;

  const ThemeAwareFadeInAnimation({
    super.key,
    required this.child,
    this.delay = Duration.zero,
    this.curve = Curves.easeInOut,
  });

  @override
  Widget build(BuildContext context) {
    final duration = AppTheme.getAnimationDuration(context, defaultDuration: const Duration(milliseconds: 500));
    
    return FadeInAnimation(
      duration: duration,
      delay: delay,
      curve: curve,
      child: child,
    );
  }
}

/// 主题感知的滑入动画组件
class ThemeAwareSlideInAnimation extends StatelessWidget {
  final Widget child;
  final Duration delay;
  final Offset begin;
  final Offset end;
  final Curve curve;

  const ThemeAwareSlideInAnimation({
    super.key,
    required this.child,
    this.delay = Duration.zero,
    this.begin = const Offset(0.0, 1.0),
    this.end = Offset.zero,
    this.curve = Curves.easeOutCubic,
  });

  @override
  Widget build(BuildContext context) {
    final duration = AppTheme.getAnimationDuration(context, defaultDuration: const Duration(milliseconds: 500));
    
    return SlideInAnimation(
      duration: duration,
      delay: delay,
      begin: begin,
      end: end,
      curve: curve,
      child: child,
    );
  }
}

/// 主题感知的列表项动画组件
class ThemeAwareAnimatedListItem extends StatelessWidget {
  final Widget child;
  final int index;
  final Duration delayBase;

  const ThemeAwareAnimatedListItem({
    super.key,
    required this.child,
    required this.index,
    this.delayBase = const Duration(milliseconds: 100),
  });

  @override
  Widget build(BuildContext context) {
    final duration = AppTheme.getAnimationDuration(context, defaultDuration: const Duration(milliseconds: 400));
    
    return AnimatedListItem(
      index: index,
      duration: duration,
      delayBase: delayBase,
      child: child,
    );
  }
}

/// 主题感知的脉冲动画组件
class ThemeAwarePulseAnimation extends StatelessWidget {
  final Widget child;
  final double minScale;
  final double maxScale;

  const ThemeAwarePulseAnimation({
    super.key,
    required this.child,
    this.minScale = 1.0,
    this.maxScale = 1.05,
  });

  @override
  Widget build(BuildContext context) {
    final duration = AppTheme.getAnimationDuration(context, defaultDuration: const Duration(milliseconds: 1000));
    
    return PulseAnimation(
      duration: duration,
      minScale: minScale,
      maxScale: maxScale,
      child: child,
    );
  }
}

/// 响应式动画容器
class ResponsiveAnimatedContainer extends StatelessWidget {
  final Widget child;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final Color? backgroundColor;
  final BorderRadius? borderRadius;
  final bool enableAnimation;

  const ResponsiveAnimatedContainer({
    super.key,
    required this.child,
    this.padding,
    this.margin,
    this.backgroundColor,
    this.borderRadius,
    this.enableAnimation = true,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    final mediaQuery = MediaQuery.of(context);
    final disableAnimations = mediaQuery.disableAnimations;
    
    final effectiveBackgroundColor = backgroundColor ?? AppTheme.getSurfaceColor(context);
    final effectiveBorderRadius = borderRadius ?? BorderRadius.circular(AppTheme.radiusMedium);
    
    Widget container = Container(
      padding: padding,
      margin: margin,
      decoration: BoxDecoration(
        color: effectiveBackgroundColor,
        borderRadius: effectiveBorderRadius,
        boxShadow: isDark ? null : [AppTheme.cardShadow],
        border: isDark ? Border.all(
          color: AppTheme.getBorderColor(context),
          width: 1,
        ) : null,
      ),
      child: child,
    );

    if (enableAnimation && !disableAnimations) {
      return ThemeAwareFadeInAnimation(child: container);
    }

    return container;
  }
}

/// 无障碍性感知的动画组件
class AccessibilityAwareAnimation extends StatelessWidget {
  final Widget child;
  final Widget? fallbackChild;
  final Duration duration;
  final Curve curve;

  const AccessibilityAwareAnimation({
    super.key,
    required this.child,
    this.fallbackChild,
    this.duration = const Duration(milliseconds: 300),
    this.curve = Curves.easeInOut,
  });

  @override
  Widget build(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    final disableAnimations = mediaQuery.disableAnimations;
    final reduceMotion = mediaQuery.accessibleNavigation;

    if (disableAnimations || reduceMotion) {
      return fallbackChild ?? child;
    }

    return ThemeAwareFadeInAnimation(
      child: child,
    );
  }
}

/// 高对比度感知的组件
class HighContrastAwareWidget extends StatelessWidget {
  final Widget child;
  final Widget? highContrastChild;
  final Color? borderColor;
  final double borderWidth;

  const HighContrastAwareWidget({
    super.key,
    required this.child,
    this.highContrastChild,
    this.borderColor,
    this.borderWidth = 2.0,
  });

  @override
  Widget build(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    final isHighContrast = mediaQuery.highContrast;

    if (isHighContrast) {
      Widget result = highContrastChild ?? child;
      
      if (borderColor != null) {
        result = Container(
          decoration: BoxDecoration(
            border: Border.all(
              color: borderColor!,
              width: borderWidth,
            ),
          ),
          child: result,
        );
      }
      
      return result;
    }

    return child;
  }
}
