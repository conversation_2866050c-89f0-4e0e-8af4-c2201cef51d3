import 'package:flutter/material.dart';
import '../services/localization_service.dart';
import '../constants/app_theme.dart';
import 'accessibility_widgets.dart';

/// 支持多语言的无障碍性按钮组件
class LocalizedAccessibleButton extends StatelessWidget {
  final Widget child;
  final VoidCallback? onPressed;
  final String? semanticLabelKey;
  final String? tooltipKey;
  final bool excludeSemantics;

  const LocalizedAccessibleButton({
    super.key,
    required this.child,
    this.onPressed,
    this.semanticLabelKey,
    this.tooltipKey,
    this.excludeSemantics = false,
  });

  @override
  Widget build(BuildContext context) {
    return AccessibleButton(
      onPressed: onPressed,
      semanticLabel: semanticLabelKey != null ? LocalizationService.t(semanticLabelKey!) : null,
      tooltip: tooltipKey != null ? LocalizationService.t(tooltipKey!) : null,
      excludeSemantics: excludeSemantics,
      child: child,
    );
  }
}

/// 支持多语言的无障碍性文本组件
class LocalizedAccessibleText extends StatelessWidget {
  final String textKey;
  final TextStyle? style;
  final String? semanticLabelKey;
  final bool isHeading;
  final int? headingLevel;
  final TextAlign? textAlign;
  final int? maxLines;
  final TextOverflow? overflow;

  const LocalizedAccessibleText(
    this.textKey, {
    super.key,
    this.style,
    this.semanticLabelKey,
    this.isHeading = false,
    this.headingLevel,
    this.textAlign,
    this.maxLines,
    this.overflow,
  });

  @override
  Widget build(BuildContext context) {
    return AccessibleText(
      LocalizationService.t(textKey),
      style: style,
      semanticLabel: semanticLabelKey != null ? LocalizationService.t(semanticLabelKey!) : null,
      isHeading: isHeading,
      headingLevel: headingLevel,
      textAlign: textAlign,
      maxLines: maxLines,
      overflow: overflow,
    );
  }
}

/// 支持多语言的无障碍性图片组件
class LocalizedAccessibleImage extends StatelessWidget {
  final String? imagePath;
  final String semanticLabelKey;
  final double? width;
  final double? height;
  final BoxFit? fit;
  final Widget? placeholder;
  final IconData? fallbackIcon;

  const LocalizedAccessibleImage({
    super.key,
    this.imagePath,
    required this.semanticLabelKey,
    this.width,
    this.height,
    this.fit,
    this.placeholder,
    this.fallbackIcon,
  });

  @override
  Widget build(BuildContext context) {
    return AccessibleImage(
      imagePath: imagePath,
      semanticLabel: LocalizationService.t(semanticLabelKey),
      width: width,
      height: height,
      fit: fit,
      placeholder: placeholder,
      fallbackIcon: fallbackIcon,
    );
  }
}

/// 支持多语言的无障碍性列表项组件
class LocalizedAccessibleListTile extends StatelessWidget {
  final Widget? leading;
  final Widget? title;
  final Widget? subtitle;
  final Widget? trailing;
  final VoidCallback? onTap;
  final String? semanticLabelKey;
  final bool isThreeLine;
  final EdgeInsetsGeometry? contentPadding;

  const LocalizedAccessibleListTile({
    super.key,
    this.leading,
    this.title,
    this.subtitle,
    this.trailing,
    this.onTap,
    this.semanticLabelKey,
    this.isThreeLine = false,
    this.contentPadding,
  });

  @override
  Widget build(BuildContext context) {
    return AccessibleListTile(
      leading: leading,
      title: title,
      subtitle: subtitle,
      trailing: trailing,
      onTap: onTap,
      semanticLabel: semanticLabelKey != null ? LocalizationService.t(semanticLabelKey!) : null,
      isThreeLine: isThreeLine,
      contentPadding: contentPadding,
    );
  }
}

/// 支持多语言的无障碍性输入框组件
class LocalizedAccessibleTextField extends StatelessWidget {
  final TextEditingController? controller;
  final String? labelTextKey;
  final String? hintTextKey;
  final String? semanticLabelKey;
  final String? errorTextKey;
  final bool obscureText;
  final TextInputType? keyboardType;
  final ValueChanged<String>? onChanged;
  final VoidCallback? onTap;
  final bool readOnly;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final int? maxLines;
  final FocusNode? focusNode;

  const LocalizedAccessibleTextField({
    super.key,
    this.controller,
    this.labelTextKey,
    this.hintTextKey,
    this.semanticLabelKey,
    this.errorTextKey,
    this.obscureText = false,
    this.keyboardType,
    this.onChanged,
    this.onTap,
    this.readOnly = false,
    this.prefixIcon,
    this.suffixIcon,
    this.maxLines = 1,
    this.focusNode,
  });

  @override
  Widget build(BuildContext context) {
    return AccessibleTextField(
      controller: controller,
      labelText: labelTextKey != null ? LocalizationService.t(labelTextKey!) : null,
      hintText: hintTextKey != null ? LocalizationService.t(hintTextKey!) : null,
      semanticLabel: semanticLabelKey != null ? LocalizationService.t(semanticLabelKey!) : null,
      errorText: errorTextKey != null ? LocalizationService.t(errorTextKey!) : null,
      obscureText: obscureText,
      keyboardType: keyboardType,
      onChanged: onChanged,
      onTap: onTap,
      readOnly: readOnly,
      prefixIcon: prefixIcon,
      suffixIcon: suffixIcon,
      maxLines: maxLines,
      focusNode: focusNode,
    );
  }
}

/// 支持多语言的无障碍性卡片组件
class LocalizedAccessibleCard extends StatelessWidget {
  final Widget child;
  final VoidCallback? onTap;
  final String? semanticLabelKey;
  final EdgeInsetsGeometry? margin;
  final EdgeInsetsGeometry? padding;
  final Color? color;
  final double? elevation;

  const LocalizedAccessibleCard({
    super.key,
    required this.child,
    this.onTap,
    this.semanticLabelKey,
    this.margin,
    this.padding,
    this.color,
    this.elevation,
  });

  @override
  Widget build(BuildContext context) {
    return AccessibleCard(
      onTap: onTap,
      semanticLabel: semanticLabelKey != null ? LocalizationService.t(semanticLabelKey!) : null,
      margin: margin,
      padding: padding,
      color: color,
      elevation: elevation,
      child: child,
    );
  }
}

/// 支持多语言的屏幕阅读器公告组件
class LocalizedScreenReaderAnnouncement extends StatelessWidget {
  final String messageKey;
  final Widget child;

  const LocalizedScreenReaderAnnouncement({
    super.key,
    required this.messageKey,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return ScreenReaderAnnouncement(
      message: LocalizationService.t(messageKey),
      child: child,
    );
  }

  /// 立即发送多语言屏幕阅读器公告
  static void announce(String messageKey) {
    ScreenReaderAnnouncement.announce(LocalizationService.t(messageKey));
  }
}

/// 主题感知的无障碍性组件
class ThemeAwareAccessibleContainer extends StatelessWidget {
  final Widget child;
  final String? semanticLabelKey;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final bool useCardStyle;

  const ThemeAwareAccessibleContainer({
    super.key,
    required this.child,
    this.semanticLabelKey,
    this.padding,
    this.margin,
    this.useCardStyle = false,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    
    Widget container = Container(
      padding: padding,
      margin: margin,
      decoration: useCardStyle ? BoxDecoration(
        color: AppTheme.getSurfaceColor(context),
        borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
        boxShadow: isDark ? null : [AppTheme.cardShadow],
        border: isDark ? Border.all(
          color: AppTheme.getBorderColor(context),
          width: 1,
        ) : null,
      ) : null,
      child: child,
    );

    if (semanticLabelKey != null) {
      container = Semantics(
        label: LocalizationService.t(semanticLabelKey!),
        child: container,
      );
    }

    return container;
  }
}
