import 'package:flutter/material.dart';
import 'package:flutter/semantics.dart';
import '../constants/app_theme.dart';

/// 无障碍性增强的按钮组件
class AccessibleButton extends StatelessWidget {
  final Widget child;
  final VoidCallback? onPressed;
  final String? semanticLabel;
  final String? tooltip;
  final bool excludeSemantics;

  const AccessibleButton({
    super.key,
    required this.child,
    this.onPressed,
    this.semanticLabel,
    this.tooltip,
    this.excludeSemantics = false,
  });

  @override
  Widget build(BuildContext context) {
    Widget button = InkWell(
      onTap: onPressed,
      borderRadius: BorderRadius.circular(AppTheme.radiusSmall),
      child: Container(
        constraints: const BoxConstraints(
          minWidth: 48.0,
          minHeight: 48.0,
        ),
        child: child,
      ),
    );

    if (tooltip != null) {
      button = Tooltip(
        message: tooltip!,
        child: button,
      );
    }

    return Semantics(
      button: true,
      enabled: onPressed != null,
      label: semanticLabel,
      excludeSemantics: excludeSemantics,
      onTap: onPressed,
      child: button,
    );
  }
}

/// 无障碍性增强的文本组件
class AccessibleText extends StatelessWidget {
  final String text;
  final TextStyle? style;
  final String? semanticLabel;
  final bool isHeading;
  final int? headingLevel;
  final TextAlign? textAlign;
  final int? maxLines;
  final TextOverflow? overflow;

  const AccessibleText(
    this.text, {
    super.key,
    this.style,
    this.semanticLabel,
    this.isHeading = false,
    this.headingLevel,
    this.textAlign,
    this.maxLines,
    this.overflow,
  });

  @override
  Widget build(BuildContext context) {
    return Semantics(
      label: semanticLabel ?? text,
      header: isHeading,
      child: Text(
        text,
        style: style,
        textAlign: textAlign,
        maxLines: maxLines,
        overflow: overflow,
      ),
    );
  }
}

/// 无障碍性增强的图片组件
class AccessibleImage extends StatelessWidget {
  final String? imagePath;
  final String? semanticLabel;
  final double? width;
  final double? height;
  final BoxFit? fit;
  final Widget? placeholder;
  final IconData? fallbackIcon;

  const AccessibleImage({
    super.key,
    this.imagePath,
    required this.semanticLabel,
    this.width,
    this.height,
    this.fit,
    this.placeholder,
    this.fallbackIcon,
  });

  @override
  Widget build(BuildContext context) {
    Widget imageWidget;

    if (imagePath != null) {
      imageWidget = Image.asset(
        imagePath!,
        width: width,
        height: height,
        fit: fit,
        errorBuilder: (context, error, stackTrace) {
          return _buildFallback();
        },
      );
    } else {
      imageWidget = _buildFallback();
    }

    return Semantics(
      image: true,
      label: semanticLabel,
      child: ExcludeSemantics(child: imageWidget),
    );
  }

  Widget _buildFallback() {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: Colors.grey[300],
        borderRadius: BorderRadius.circular(AppTheme.radiusSmall),
      ),
      child: Icon(
        fallbackIcon ?? Icons.image,
        size: (width != null && height != null) 
            ? (width! < height! ? width! * 0.5 : height! * 0.5)
            : 24,
        color: Colors.grey[600],
      ),
    );
  }
}

/// 无障碍性增强的列表项组件
class AccessibleListTile extends StatelessWidget {
  final Widget? leading;
  final Widget? title;
  final Widget? subtitle;
  final Widget? trailing;
  final VoidCallback? onTap;
  final String? semanticLabel;
  final bool isThreeLine;
  final EdgeInsetsGeometry? contentPadding;

  const AccessibleListTile({
    super.key,
    this.leading,
    this.title,
    this.subtitle,
    this.trailing,
    this.onTap,
    this.semanticLabel,
    this.isThreeLine = false,
    this.contentPadding,
  });

  @override
  Widget build(BuildContext context) {
    return Semantics(
      button: onTap != null,
      enabled: onTap != null,
      label: semanticLabel,
      onTap: onTap,
      child: ListTile(
        leading: leading,
        title: title,
        subtitle: subtitle,
        trailing: trailing,
        onTap: onTap,
        isThreeLine: isThreeLine,
        contentPadding: contentPadding,
        minVerticalPadding: 12.0, // 确保足够的触摸目标大小
      ),
    );
  }
}

/// 无障碍性增强的输入框组件
class AccessibleTextField extends StatelessWidget {
  final TextEditingController? controller;
  final String? labelText;
  final String? hintText;
  final String? semanticLabel;
  final String? errorText;
  final bool obscureText;
  final TextInputType? keyboardType;
  final ValueChanged<String>? onChanged;
  final VoidCallback? onTap;
  final bool readOnly;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final int? maxLines;
  final FocusNode? focusNode;

  const AccessibleTextField({
    super.key,
    this.controller,
    this.labelText,
    this.hintText,
    this.semanticLabel,
    this.errorText,
    this.obscureText = false,
    this.keyboardType,
    this.onChanged,
    this.onTap,
    this.readOnly = false,
    this.prefixIcon,
    this.suffixIcon,
    this.maxLines = 1,
    this.focusNode,
  });

  @override
  Widget build(BuildContext context) {
    return Semantics(
      textField: true,
      label: semanticLabel ?? labelText,
      hint: hintText,
      child: TextField(
        controller: controller,
        focusNode: focusNode,
        decoration: InputDecoration(
          labelText: labelText,
          hintText: hintText,
          errorText: errorText,
          prefixIcon: prefixIcon,
          suffixIcon: suffixIcon,
          border: const OutlineInputBorder(),
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16.0,
            vertical: 12.0,
          ),
        ),
        obscureText: obscureText,
        keyboardType: keyboardType,
        onChanged: onChanged,
        onTap: onTap,
        readOnly: readOnly,
        maxLines: maxLines,
      ),
    );
  }
}

/// 无障碍性增强的卡片组件
class AccessibleCard extends StatelessWidget {
  final Widget child;
  final VoidCallback? onTap;
  final String? semanticLabel;
  final EdgeInsetsGeometry? margin;
  final EdgeInsetsGeometry? padding;
  final Color? color;
  final double? elevation;

  const AccessibleCard({
    super.key,
    required this.child,
    this.onTap,
    this.semanticLabel,
    this.margin,
    this.padding,
    this.color,
    this.elevation,
  });

  @override
  Widget build(BuildContext context) {
    Widget card = Card(
      margin: margin,
      color: color,
      elevation: elevation ?? 2.0,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
        child: Padding(
          padding: padding ?? const EdgeInsets.all(AppTheme.paddingMedium),
          child: child,
        ),
      ),
    );

    return Semantics(
      button: onTap != null,
      enabled: onTap != null,
      label: semanticLabel,
      onTap: onTap,
      child: card,
    );
  }
}

/// 高对比度主题检测器
class HighContrastDetector extends StatelessWidget {
  final Widget child;
  final Widget? highContrastChild;

  const HighContrastDetector({
    super.key,
    required this.child,
    this.highContrastChild,
  });

  @override
  Widget build(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    final isHighContrast = mediaQuery.highContrast;

    if (isHighContrast && highContrastChild != null) {
      return highContrastChild!;
    }

    return child;
  }
}

/// 屏幕阅读器公告组件
class ScreenReaderAnnouncement extends StatelessWidget {
  final String message;
  final Widget child;

  const ScreenReaderAnnouncement({
    super.key,
    required this.message,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return Semantics(
      liveRegion: true,
      label: message,
      child: child,
    );
  }

  /// 立即发送屏幕阅读器公告
  static void announce(String message) {
    SemanticsService.announce(message, TextDirection.ltr);
  }
}
