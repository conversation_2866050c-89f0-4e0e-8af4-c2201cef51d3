import 'package:flutter/material.dart';
import '../services/network_status_service.dart';
import '../constants/app_theme.dart';
import '../services/localization_service.dart';

/// 网络状态指示器组件
class NetworkStatusIndicator extends StatefulWidget {
  final bool showWhenConnected;
  final Duration autoHideDuration;
  
  const NetworkStatusIndicator({
    super.key,
    this.showWhenConnected = false,
    this.autoHideDuration = const Duration(seconds: 3),
  });

  @override
  State<NetworkStatusIndicator> createState() => _NetworkStatusIndicatorState();
}

class _NetworkStatusIndicatorState extends State<NetworkStatusIndicator> {
  final NetworkStatusService _networkStatus = NetworkStatusService();
  NetworkStatus? _currentStatus;
  bool _isVisible = false;

  @override
  void initState() {
    super.initState();
    _initializeNetworkStatus();
  }

  void _initializeNetworkStatus() {
    // 获取当前状态
    _currentStatus = _networkStatus.currentStatus;
    _updateVisibility();

    // 监听状态变化
    _networkStatus.statusStream.listen((status) {
      if (mounted) {
        setState(() {
          _currentStatus = status;
          _updateVisibility();
        });
      }
    });
  }

  void _updateVisibility() {
    final shouldShow = _shouldShowIndicator();
    
    if (shouldShow != _isVisible) {
      setState(() {
        _isVisible = shouldShow;
      });

      // 如果显示且网络已连接，自动隐藏
      if (_isVisible && _currentStatus?.isConnected == true && widget.showWhenConnected) {
        Future.delayed(widget.autoHideDuration, () {
          if (mounted && _currentStatus?.isConnected == true) {
            setState(() {
              _isVisible = false;
            });
          }
        });
      }
    }
  }

  bool _shouldShowIndicator() {
    if (_currentStatus == null) return false;
    
    // 如果网络断开，总是显示
    if (!_currentStatus!.isConnected) return true;
    
    // 如果网络连接且设置为显示连接状态，则显示
    if (_currentStatus!.isConnected && widget.showWhenConnected) return true;
    
    return false;
  }

  @override
  Widget build(BuildContext context) {
    if (!_isVisible || _currentStatus == null) {
      return const SizedBox.shrink();
    }

    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: _getBackgroundColor(),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              _getStatusIcon(),
              color: Colors.white,
              size: 16,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                _getStatusText(),
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            if (_currentStatus!.isConnected && _currentStatus!.isMetered)
              Container(
                margin: const EdgeInsets.only(left: 8),
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Text(
                  '计费',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 10,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Color _getBackgroundColor() {
    if (_currentStatus == null) return AppTheme.errorColor;
    
    if (!_currentStatus!.isConnected) {
      return AppTheme.errorColor;
    }
    
    switch (_currentStatus!.type) {
      case NetworkType.wifi:
        return AppTheme.successColor;
      case NetworkType.mobile:
        return AppTheme.warningColor;
      case NetworkType.ethernet:
        return AppTheme.primaryColor;
      default:
        return AppTheme.infoColor;
    }
  }

  IconData _getStatusIcon() {
    if (_currentStatus == null || !_currentStatus!.isConnected) {
      return Icons.wifi_off;
    }
    
    switch (_currentStatus!.type) {
      case NetworkType.wifi:
        return Icons.wifi;
      case NetworkType.mobile:
        return Icons.signal_cellular_4_bar;
      case NetworkType.ethernet:
        return Icons.lan;
      default:
        return Icons.device_unknown;
    }
  }

  String _getStatusText() {
    if (_currentStatus == null) {
      return '网络状态未知';
    }
    
    if (!_currentStatus!.isConnected) {
      return '网络连接已断开';
    }
    
    switch (_currentStatus!.type) {
      case NetworkType.wifi:
        return 'WiFi已连接';
      case NetworkType.mobile:
        return '移动网络已连接';
      case NetworkType.ethernet:
        return '以太网已连接';
      default:
        return '网络已连接';
    }
  }

  @override
  void dispose() {
    super.dispose();
  }
}

/// 网络状态横幅组件
class NetworkStatusBanner extends StatelessWidget {
  final Widget child;
  final bool showWhenConnected;
  
  const NetworkStatusBanner({
    super.key,
    required this.child,
    this.showWhenConnected = false,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        NetworkStatusIndicator(
          showWhenConnected: showWhenConnected,
        ),
        Expanded(child: child),
      ],
    );
  }
}
