import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';
import '../constants/app_theme.dart';

/// 通用骨架屏组件
class SkeletonWidget extends StatelessWidget {
  final double width;
  final double height;
  final BorderRadius? borderRadius;

  const SkeletonWidget({
    super.key,
    required this.width,
    required this.height,
    this.borderRadius,
  });

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: AppTheme.getSkeletonBaseColor(context),
      highlightColor: AppTheme.getSkeletonHighlightColor(context),
      child: Container(
        width: width,
        height: height,
        decoration: BoxDecoration(
          color: AppTheme.getSkeletonBaseColor(context),
          borderRadius: borderRadius ?? BorderRadius.circular(AppTheme.radiusSmall),
        ),
      ),
    );
  }
}

/// 首页轮播图骨架屏
class CarouselSkeleton extends StatelessWidget {
  const CarouselSkeleton({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(AppTheme.paddingMedium),
      child: Column(
        children: [
          // 轮播图骨架
          const SkeletonWidget(
            width: double.infinity,
            height: 180,
            borderRadius: BorderRadius.all(Radius.circular(AppTheme.radiusMedium)),
          ),
          const SizedBox(height: AppTheme.paddingSmall),
          // 指示器骨架
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: List.generate(
              3,
              (index) => Container(
                margin: const EdgeInsets.symmetric(horizontal: 4),
                child: const SkeletonWidget(
                  width: 8,
                  height: 8,
                  borderRadius: BorderRadius.all(Radius.circular(4)),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// 资讯列表骨架屏
class NewsListSkeleton extends StatelessWidget {
  final int itemCount;

  const NewsListSkeleton({
    super.key,
    this.itemCount = 5,
  });

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      padding: const EdgeInsets.all(AppTheme.paddingMedium),
      itemCount: itemCount,
      itemBuilder: (context, index) => const NewsItemSkeleton(),
    );
  }
}

/// 单个资讯项骨架屏
class NewsItemSkeleton extends StatelessWidget {
  const NewsItemSkeleton({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppTheme.paddingMedium),
      padding: const EdgeInsets.all(AppTheme.paddingMedium),
      decoration: BoxDecoration(
        color: AppTheme.getSurfaceColor(context),
        borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
        boxShadow: [AppTheme.cardShadow],
      ),
      child: const Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题骨架
          SkeletonWidget(
            width: double.infinity,
            height: 20,
          ),
          SizedBox(height: AppTheme.paddingSmall),
          // 副标题骨架
          SkeletonWidget(
            width: 200,
            height: 16,
          ),
          SizedBox(height: AppTheme.paddingSmall),
          // 时间骨架
          SkeletonWidget(
            width: 100,
            height: 14,
          ),
        ],
      ),
    );
  }
}

/// 应用网格骨架屏
class AppGridSkeleton extends StatelessWidget {
  final int itemCount;

  const AppGridSkeleton({
    super.key,
    this.itemCount = 12,
  });

  @override
  Widget build(BuildContext context) {
    return GridView.builder(
      padding: const EdgeInsets.all(AppTheme.paddingMedium),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        crossAxisSpacing: AppTheme.paddingMedium,
        mainAxisSpacing: AppTheme.paddingMedium,
        childAspectRatio: 1.0,
      ),
      itemCount: itemCount,
      itemBuilder: (context, index) => const AppItemSkeleton(),
    );
  }
}

/// 单个应用项骨架屏
class AppItemSkeleton extends StatelessWidget {
  const AppItemSkeleton({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppTheme.paddingSmall),
      decoration: BoxDecoration(
        color: AppTheme.getSurfaceColor(context),
        borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
        boxShadow: [AppTheme.cardShadow],
      ),
      child: const Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // 应用图标骨架
          SkeletonWidget(
            width: 48,
            height: 48,
            borderRadius: BorderRadius.all(Radius.circular(12)),
          ),
          SizedBox(height: AppTheme.paddingSmall),
          // 应用名称骨架
          SkeletonWidget(
            width: 60,
            height: 14,
          ),
        ],
      ),
    );
  }
}

/// 工作台应用列表骨架屏
class WorkspaceAppsSkeleton extends StatelessWidget {
  final int itemCount;

  const WorkspaceAppsSkeleton({
    super.key,
    this.itemCount = 6,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppTheme.paddingMedium),
      padding: const EdgeInsets.all(AppTheme.paddingMedium),
      decoration: BoxDecoration(
        color: AppTheme.getSurfaceColor(context),
        borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
        boxShadow: [AppTheme.cardShadow],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题骨架
          const SkeletonWidget(
            width: 120,
            height: 20,
          ),
          const SizedBox(height: AppTheme.paddingMedium),
          // 应用列表骨架
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 4,
              crossAxisSpacing: AppTheme.paddingSmall,
              mainAxisSpacing: AppTheme.paddingSmall,
              childAspectRatio: 1.0,
            ),
            itemCount: itemCount,
            itemBuilder: (context, index) => const Column(
              children: [
                SkeletonWidget(
                  width: 40,
                  height: 40,
                  borderRadius: BorderRadius.all(Radius.circular(8)),
                ),
                SizedBox(height: 4),
                SkeletonWidget(
                  width: 50,
                  height: 12,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

/// 消息列表骨架屏
class MessageListSkeleton extends StatelessWidget {
  final int itemCount;

  const MessageListSkeleton({
    super.key,
    this.itemCount = 8,
  });

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      padding: const EdgeInsets.all(AppTheme.paddingMedium),
      itemCount: itemCount,
      itemBuilder: (context, index) => const MessageItemSkeleton(),
    );
  }
}

/// 单个消息项骨架屏
class MessageItemSkeleton extends StatelessWidget {
  const MessageItemSkeleton({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppTheme.paddingSmall),
      padding: const EdgeInsets.all(AppTheme.paddingMedium),
      decoration: BoxDecoration(
        color: AppTheme.getSurfaceColor(context),
        borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
        border: Border.all(color: AppTheme.getBorderColor(context)),
      ),
      child: const Row(
        children: [
          // 头像骨架
          SkeletonWidget(
            width: 40,
            height: 40,
            borderRadius: BorderRadius.all(Radius.circular(20)),
          ),
          SizedBox(width: AppTheme.paddingMedium),
          // 内容骨架
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SkeletonWidget(
                  width: double.infinity,
                  height: 16,
                ),
                SizedBox(height: AppTheme.paddingSmall),
                SkeletonWidget(
                  width: 150,
                  height: 14,
                ),
              ],
            ),
          ),
          // 时间骨架
          SkeletonWidget(
            width: 60,
            height: 12,
          ),
        ],
      ),
    );
  }
}
