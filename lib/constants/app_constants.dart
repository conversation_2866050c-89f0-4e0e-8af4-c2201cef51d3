import '../services/localization_service.dart';

class AppConstants {
  // 应用信息
  static const String appName = '企业移动办公系统';
  static const String appVersion = '1.0.0';
  static const String copyright = '版权所有 © 2023 企业移动办公系统';

  // 网络配置
  static const int connectTimeout = 30000;
  static const int receiveTimeout = 30000;

  // 本地存储键
  static const String keyUsername = 'username';
  static const String keyPassword = 'password';
  static const String keyRememberPassword = 'remember_password';
  static const String keyServerAddress = 'server_address';
  static const String keyServerPort = 'server_port';
  static const String keySavedServers = 'saved_servers';
  static const String keyUserInfo = 'user_info';
  static const String keyThemeMode = 'theme_mode';

  // 默认配置
  static const String defaultServerAddress = 'company.example.com';
  static const String defaultServerPort = '8080';

  // 轮播图数据
  static const List<Map<String, String>> carouselData = [
    {
      'title': '企业战略规划会议',
      'subtitle': '2023年度第三季度企业战略规划会议将于下周三举行',
      'color': '0xFF3366CC',
    },
    {
      'title': '新版应用上线',
      'subtitle': '企业数据分析平台V2.0现已上线，欢迎体验',
      'color': '0xFFFF9900',
    },
    {
      'title': '年度优秀员工评选',
      'subtitle': '2023年度优秀员工评选活动开始，请各部门提交推荐名单',
      'color': '0xFF52C41A',
    },
  ];

  // 应用分类数据
  static List<Map<String, dynamic>> get appCategories => [
    {
      'title': LocalizationService.t('office_applications'),
      'apps': [
        {
          'name': LocalizationService.t('schedule_management'),
          'icon': 'calendar_today',
          'color': '0xFF3366CC',
        },
        {
          'name': LocalizationService.t('task_collaboration'),
          'icon': 'assignment',
          'color': '0xFFFF9900',
        },
        {
          'name': LocalizationService.t('document_center'),
          'icon': 'description',
          'color': '0xFF52C41A',
        },
        {
          'name': LocalizationService.t('email_system'),
          'icon': 'email',
          'color': '0xFF1890FF',
        },
        {
          'name': LocalizationService.t('video_conference'),
          'icon': 'video_call',
          'color': '0xFF722ED1',
        },
      ],
    },
    {
      'title': LocalizationService.t('financial_applications'),
      'apps': [
        {
          'name': LocalizationService.t('expense_reimbursement'),
          'icon': 'receipt',
          'color': '0xFFF5222D',
        },
        {
          'name': LocalizationService.t('financial_reports'),
          'icon': 'pie_chart',
          'color': '0xFFFAAD14',
        },
        {
          'name': LocalizationService.t('budget_management'),
          'icon': 'account_balance',
          'color': '0xFF13C2C2',
        },
        {
          'name': LocalizationService.t('invoice_system'),
          'icon': 'receipt_long',
          'color': '0xFF52C41A',
        },
      ],
    },
    {
      'title': LocalizationService.t('hr_applications'),
      'apps': [
        {
          'name': LocalizationService.t('employee_management'),
          'icon': 'people',
          'color': '0xFF9C27B0',
        },
        {
          'name': LocalizationService.t('attendance_system'),
          'icon': 'access_time',
          'color': '0xFF795548',
        },
        {
          'name': LocalizationService.t('performance_review'),
          'icon': 'assessment',
          'color': '0xFFE91E63',
        },
        {
          'name': LocalizationService.t('training_center'),
          'icon': 'school',
          'color': '0xFF607D8B',
        },
      ],
    },
    {
      'title': LocalizationService.t('business_applications'),
      'apps': [
        {
          'name': LocalizationService.t('customer_management'),
          'icon': 'business',
          'color': '0xFF00BCD4',
        },
        {
          'name': LocalizationService.t('sales_tracking'),
          'icon': 'trending_up',
          'color': '0xFF4CAF50',
        },
        {
          'name': LocalizationService.t('inventory_management'),
          'icon': 'inventory',
          'color': '0xFFFF5722',
        },
        {
          'name': LocalizationService.t('project_management'),
          'icon': 'folder_open',
          'color': '0xFF3F51B5',
        },
      ],
    },
    {
      'title': LocalizationService.t('communication_applications'),
      'apps': [
        {
          'name': LocalizationService.t('instant_messaging'),
          'icon': 'chat',
          'color': '0xFF2196F3',
        },
        {
          'name': LocalizationService.t('announcement_board'),
          'icon': 'campaign',
          'color': '0xFFFF9800',
        },
        {
          'name': LocalizationService.t('forum_discussion'),
          'icon': 'forum',
          'color': '0xFF8BC34A',
        },
      ],
    },
    {
      'title': LocalizationService.t('tool_applications'),
      'apps': [
        {
          'name': LocalizationService.t('file_manager'),
          'icon': 'folder',
          'color': '0xFF607D8B',
        },
        {
          'name': LocalizationService.t('calculator'),
          'icon': 'calculate',
          'color': '0xFF795548',
        },
        {
          'name': LocalizationService.t('qr_scanner'),
          'icon': 'qr_code_scanner',
          'color': '0xFF9E9E9E',
        },
        {
          'name': LocalizationService.t('note_taking'),
          'icon': 'note_add',
          'color': '0xFFFFC107',
        },
      ],
    },
  ];

  // 消息类型（固定的英文键值，用于数据过滤）
  static const List<String> messageTypes = [
    'all',
    'unread',
    'system',
    'work',
  ];

  // 消息类型的翻译显示名称
  static List<String> get messageTypeNames => [
    LocalizationService.t('all'),
    LocalizationService.t('unread'),
    LocalizationService.t('system'),
    LocalizationService.t('work'),
  ];

  // 示例消息数据
  static List<Map<String, dynamic>> get sampleMessages => [
    {
      'title': LocalizationService.t('system_notification'),
      'content': LocalizationService.t('system_update_content'),
      'time': '09:15',
      'icon': 'notifications',
      'color': '0xFF3366CC',
      'unreadCount': 0,
      'type': LocalizationService.t('system'),
    },
    {
      'title': LocalizationService.t('approval_reminder'),
      'content': LocalizationService.t('approval_content'),
      'time': LocalizationService.t('yesterday'),
      'icon': 'assignment_turned_in',
      'color': '0xFFFF9900',
      'unreadCount': 2,
      'type': LocalizationService.t('work'),
    },
    {
      'title': LocalizationService.t('team_message'),
      'content': LocalizationService.t('team_content'),
      'time': LocalizationService.t('yesterday'),
      'icon': 'group',
      'color': '0xFF13C2C2',
      'unreadCount': 0,
      'type': LocalizationService.t('work'),
    },
    {
      'title': LocalizationService.t('schedule_reminder'),
      'content': LocalizationService.t('schedule_content'),
      'time': LocalizationService.t('days_ago_3'),
      'icon': 'event',
      'color': '0xFF722ED1',
      'unreadCount': 1,
      'type': LocalizationService.t('work'),
    },
    {
      'title': LocalizationService.t('urgent_notice'),
      'content': LocalizationService.t('urgent_content'),
      'time': '10-15',
      'icon': 'warning',
      'color': '0xFFF5222D',
      'unreadCount': 0,
      'type': LocalizationService.t('system'),
    },
  ];

  // 设置菜单项 - 这个数据结构已经在settings_screen.dart中使用翻译，这里保留原始结构作为参考
  static const List<Map<String, dynamic>> settingsMenus = [
    {
      'category': '账号设置',
      'items': [
        {'title': '账号安全', 'icon': 'security', 'color': '0xFF3366CC'},
        {'title': '消息通知', 'icon': 'notifications', 'color': '0xFFFF9900'},
        {'title': '隐私设置', 'icon': 'privacy_tip', 'color': '0xFF13C2C2'},
      ],
    },
    {
      'category': '企业设置',
      'items': [
        {'title': '企业信息', 'icon': 'business', 'color': '0xFF1890FF'},
        {'title': '部门管理', 'icon': 'people', 'color': '0xFF722ED1'},
      ],
    },
    {
      'category': '应用偏好',
      'items': [
        {'title': '应用排序', 'icon': 'apps', 'color': '0xFF52C41A'},
        {
          'title': '外观设置',
          'icon': 'palette',
          'color': '0xFFFF9900',
          'subtitle': '浅色',
        },
        {
          'title': '语言设置',
          'icon': 'language',
          'color': '0xFF722ED1',
          'subtitle': '中文',
        },
        {'title': '服务器配置', 'icon': 'dns', 'color': '0xFF13C2C2'},
      ],
    },
    {
      'category': '关于与帮助',
      'items': [
        {'title': '关于我们', 'icon': 'info', 'color': '0xFFF5222D'},
        {'title': '帮助中心', 'icon': 'help', 'color': '0xFFFAAD14'},
        {'title': '意见反馈', 'icon': 'feedback', 'color': '0xFF1890FF'},
      ],
    },
  ];

  // 企业资讯示例数据
  static const List<Map<String, String>> newsData = [
    {
      'title': '我司成功签约国际大型合作项目',
      'date': '2023-10-15',
      'content': '近日，我公司与国际知名企业成功签署战略合作协议，双方将在多个领域展开深入合作...',
    },
    {
      'title': '技术部门完成年度系统升级',
      'date': '2023-10-10',
      'content': '经过技术团队三个月的努力，公司核心系统成功完成升级，性能提升30%，并新增多项功能...',
    },
    {
      'title': '企业社会责任：公益捐赠活动圆满完成',
      'date': '2023-10-05',
      'content': '我公司组织的年度公益捐赠活动已圆满结束，共为山区学校捐赠教学设备和图书...',
    },
  ];
}
