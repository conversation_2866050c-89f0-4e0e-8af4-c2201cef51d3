import 'package:flutter/material.dart';
import '../constants/app_theme.dart';
import '../constants/app_constants.dart';
import '../services/app_state_manager.dart';
import '../services/cache_service.dart';
import '../services/localization_service.dart';
import 'webview_screen.dart';
import '../services/api_service.dart';
import '../widgets/skeleton_widgets.dart';

class MessagesScreen extends StatefulWidget {
  const MessagesScreen({super.key});

  @override
  State<MessagesScreen> createState() => _MessagesScreenState();
}

class _MessagesScreenState extends State<MessagesScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _searchFocusNode = FocusNode();
  int _selectedTabIndex = 0;
  List<Map<String, dynamic>> _messages = [];
  List<Map<String, dynamic>> _filteredMessages = [];
  bool _isLoading = true;
  bool _isRefreshing = false;
  bool _hasCache = false;
  DateTime? _lastRefreshTime;

  // 获取翻译后的消息类型（用于显示）
  List<String> get _messageTypes => AppConstants.messageTypeNames;

  // 获取原始消息类型（用于数据过滤）
  List<String> get _originalMessageTypes => AppConstants.messageTypes;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _messageTypes.length, vsync: this);
    _tabController.addListener(() {
      setState(() {
        _selectedTabIndex = _tabController.index;
      });
    });
    _initializeMessages();
  }

  Future<void> _initializeMessages() async {
    // 使用AppStateManager检查是否需要强制刷新
    final appStateManager = AppStateManager();
    final shouldForceRefresh = await appStateManager.shouldForceRefresh();

    if (shouldForceRefresh) {
      // 需要强制刷新，显示骨架屏并强制加载数据
      debugPrint('消息页面检测到强制刷新标记，强制加载数据');
      setState(() {
        _hasCache = false;
        _isLoading = true;
      });
      _loadMessages();
      return;
    }

    // 正常流程：检查是否有缓存数据
    final cachedData = await CacheService.getMessagesData();
    if (cachedData != null) {
      // 有缓存数据，直接显示
      setState(() {
        _hasCache = true;
        _isLoading = false;
        _messages = List<Map<String, dynamic>>.from(cachedData['messages'] ?? []);
        _filteredMessages = List.from(_messages);
      });
    } else {
      // 没有缓存数据，加载数据
      _loadMessages();
    }
  }

  Future<void> _loadMessages() async {
    try {
      final result = await ApiService.getMessages(context: context);
      if (result['success'] && mounted) {
        final messages = List<Map<String, dynamic>>.from(result['data']['messages']);

        setState(() {
          _messages = messages;
          _filteredMessages = List.from(_messages);
          _isLoading = false;
          _hasCache = true;
        });
      } else if (mounted) {
        // 检查是否为token过期错误
        if (result['isTokenExpired'] == true) {
          // Token过期时，TokenExpiryHandler已经处理了对话框和跳转
          // 这里只需要更新UI状态，不显示额外的错误信息
          setState(() {
            _isLoading = false;
          });
          return;
        }

        // API调用失败，显示错误信息
        setState(() {
          _isLoading = false;
        });

        // 如果有错误信息，显示给用户
        if (result['message'] != null) {
          Color backgroundColor;
          if (result['fromCache'] == true) {
            // 从缓存获取数据
            backgroundColor = result['isOffline'] == true
                ? AppTheme.warningColor  // 离线缓存
                : AppTheme.infoColor;    // 在线缓存
          } else {
            backgroundColor = AppTheme.errorColor; // 网络错误
          }

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(result['message']),
              backgroundColor: backgroundColor,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${LocalizationService.t('load_failed')}: ${e.toString()}'),
            backgroundColor: AppTheme.errorColor,
          ),
        );
      }
    }
  }

  Future<void> _refreshMessages() async {
    // 检查是否在刷新中或距离上次刷新不足3秒
    final now = DateTime.now();
    if (_isRefreshing ||
        (_lastRefreshTime != null &&
            now.difference(_lastRefreshTime!).inSeconds < 3)) {
      return;
    }

    setState(() {
      _isRefreshing = true;
    });

    _lastRefreshTime = now;

    try {
      // 强制刷新，不使用缓存
      final result = await ApiService.getMessages(forceRefresh: true, context: context);
      if (result['success'] && mounted) {
        setState(() {
          _messages = List<Map<String, dynamic>>.from(
            result['data']['messages'],
          );
          _filteredMessages = List.from(_messages);
          _filterMessages(_searchController.text);
        });

        // 显示刷新成功提示
        String message;
        Color backgroundColor;

        if (result['fromCache'] == true) {
          // 从缓存获取数据
          if (result['isOffline'] == true) {
            message = LocalizationService.t('fetch_success_offline_cache');
            backgroundColor = AppTheme.warningColor;
          } else {
            message = LocalizationService.t('fetch_success_cache');
            backgroundColor = AppTheme.infoColor;
          }
        } else {
          // 从网络获取新数据
          message = LocalizationService.t('refresh_success');
          backgroundColor = AppTheme.successColor;
        }

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(message),
            duration: const Duration(seconds: 1),
            backgroundColor: backgroundColor,
          ),
        );
      } else if (mounted) {
        // 检查是否为token过期错误
        if (result['isTokenExpired'] == true) {
          // Token过期时，TokenExpiryHandler已经处理了对话框和跳转
          // 这里不显示额外的错误信息
          return;
        }

        // 刷新失败，显示错误信息
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(result['message'] ?? LocalizationService.t('refresh_failed')),
            backgroundColor: AppTheme.errorColor,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        // 显示刷新失败提示
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${LocalizationService.t('refresh_failed')}: ${e.toString()}'),
            duration: const Duration(seconds: 2),
            backgroundColor: AppTheme.errorColor,
          ),
        );
      }
    } finally {
      // 3秒后重新启用刷新按钮
      Future.delayed(const Duration(seconds: 3), () {
        if (mounted) {
          setState(() {
            _isRefreshing = false;
          });
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        // 点击其他区域时失去搜索栏焦点
        _searchFocusNode.unfocus();
      },
      child: Scaffold(
        backgroundColor: AppTheme.getBackgroundColor(context),
        appBar: AppBar(
          title: Text(LocalizationService.t('messages')),
          actions: [
            IconButton(
              onPressed: _isRefreshing ? null : _refreshMessages,
              icon: _isRefreshing
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
                      ),
                    )
                  : const Icon(Icons.refresh),
              style: IconButton.styleFrom(
                disabledForegroundColor: Theme.of(context).brightness == Brightness.dark
                    ? AppTheme.darkTextSecondary
                    : AppTheme.textSecondary,
              ),
            ),
          ],
        ),
        body: _isLoading && !_hasCache
            ? const Column(
                children: [
                  // 搜索栏骨架
                  SizedBox(height: 80),
                  // 标签栏骨架
                  SizedBox(height: 50),
                  // 消息列表骨架屏
                  Expanded(child: MessageListSkeleton()),
                ],
              )
            : Column(
                children: [
                  // 搜索栏
                  _buildSearchBar(),
                  // 消息类型标签栏
                  _buildTabBar(),
                  // 消息列表
                  Expanded(
                    child: TabBarView(
                      controller: _tabController,
                      children: _originalMessageTypes.map((type) {
                        return _buildMessageList(type);
                      }).toList(),
                    ),
                  ),
                ],
              ),
      ),
    );
  }

  Widget _buildSearchBar() {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Container(
      padding: const EdgeInsets.all(AppTheme.paddingMedium),
      color: AppTheme.getSurfaceColor(context),
      child: TextField(
        controller: _searchController,
        focusNode: _searchFocusNode,
        onChanged: _filterMessages,
        style: TextStyle(
          color: isDark ? AppTheme.darkTextPrimary : AppTheme.textPrimary,
          fontSize: AppTheme.fontSizeMedium,
        ),
        decoration: InputDecoration(
          hintText: LocalizationService.t('search_messages'),
          hintStyle: TextStyle(
            color: isDark ? AppTheme.darkTextTertiary : AppTheme.textTertiary,
            fontSize: AppTheme.fontSizeMedium,
          ),
          prefixIcon: Icon(
            Icons.search,
            color: isDark ? AppTheme.darkTextTertiary : AppTheme.textTertiary,
          ),
          suffixIcon:
              _searchController.text.isNotEmpty
                  ? IconButton(
                    onPressed: _clearSearch,
                    icon: Icon(
                      Icons.clear,
                      color:
                          isDark
                              ? AppTheme.darkTextTertiary
                              : AppTheme.textTertiary,
                    ),
                  )
                  : null,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
            borderSide: BorderSide(
              color: isDark ? AppTheme.darkBorder : AppTheme.borderColor,
              width: 1,
            ),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
            borderSide: BorderSide(
              color: isDark ? AppTheme.darkBorder : AppTheme.borderColor,
              width: 1,
            ),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
            borderSide: BorderSide(color: AppTheme.primaryColor, width: 2),
          ),
          filled: true,
          fillColor: isDark ? AppTheme.darkCard : AppTheme.white,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: AppTheme.paddingMedium,
            vertical: AppTheme.paddingSmall,
          ),
        ),
      ),
    );
  }

  void _filterMessages(String query) {
    setState(() {
      if (query.isEmpty) {
        _filteredMessages = List.from(_messages);
      } else {
        _filteredMessages =
            _messages.where((message) {
              final title = message['title']?.toString() ?? '';
              final content = message['content']?.toString() ?? '';
              return title.toLowerCase().contains(
                    query.toLowerCase(),
                  ) ||
                  content.toLowerCase().contains(
                    query.toLowerCase(),
                  );
            }).toList();
      }
    });
  }

  void _clearSearch() {
    setState(() {
      _searchController.clear();
      _filteredMessages = List.from(_messages);
    });
  }

  Widget _buildTabBar() {
    return Container(
      color: AppTheme.getSurfaceColor(context),
      child: TabBar(
        controller: _tabController,
        isScrollable: false,
        labelColor: AppTheme.primaryColor,
        unselectedLabelColor: AppTheme.textSecondary,
        indicatorColor: AppTheme.primaryColor,
        indicatorWeight: 2,
        labelStyle: const TextStyle(
          fontSize: AppTheme.fontSizeMedium,
          fontWeight: FontWeight.w600,
        ),
        unselectedLabelStyle: const TextStyle(
          fontSize: AppTheme.fontSizeMedium,
          fontWeight: FontWeight.normal,
        ),
        tabs:
            _messageTypes.map((type) {
              return Tab(text: type);
            }).toList(),
      ),
    );
  }

  Widget _buildMessageList(String messageType) {
    List<Map<String, dynamic>> filteredMessages = _getFilteredMessages(
      messageType,
    );

    if (filteredMessages.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.message, size: 64, color: AppTheme.textTertiary),
            const SizedBox(height: AppTheme.paddingMedium),
            Text(
              LocalizationService.t(
                'no_messages_of_type',
              ).replaceAll('{type}', _getMessageTypeDisplayName(messageType)),
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(color: AppTheme.textTertiary),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(AppTheme.paddingMedium),
      itemCount: filteredMessages.length,
      itemBuilder: (context, index) {
        final message = filteredMessages[index];
        return _buildMessageItem(message);
      },
    );
  }

  Widget _buildMessageItem(Map<String, dynamic> message) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppTheme.paddingSmall),
      decoration: BoxDecoration(
        color: AppTheme.getCardColor(context),
        borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
        boxShadow: [AppTheme.getCardShadow(context)],
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.all(AppTheme.paddingMedium),
        leading: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: Color(int.parse(message['color'] ?? '0xFF3366CC')),
            borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
          ),
          child: Icon(
            _getIconData(message['icon'] ?? 'notifications'),
            color: AppTheme.white,
            size: 20,
          ),
        ),
        title: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: Text(
                message['title']?.toString() ?? LocalizationService.t('unknown_message'),
                style: Theme.of(
                  context,
                ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            Text(
              message['time']?.toString() ?? '',
              style: Theme.of(
                context,
              ).textTheme.bodySmall?.copyWith(color: AppTheme.textTertiary),
            ),
          ],
        ),
        subtitle: Padding(
          padding: const EdgeInsets.only(top: 4),
          child: Text(
            message['content']?.toString() ?? '',
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(color: AppTheme.textSecondary),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ),
        trailing:
            (message['unreadCount'] ?? 0) > 0
                ? Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 2,
                  ),
                  decoration: BoxDecoration(
                    color: AppTheme.errorColor,
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Text(
                    '${message['unreadCount'] ?? 0}',
                    style: const TextStyle(
                      color: AppTheme.white,
                      fontSize: AppTheme.fontSizeSmall,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                )
                : null,
        onTap: () => _openMessage(message),
      ),
    );
  }

  List<Map<String, dynamic>> _getFilteredMessages(String messageType) {
    if (messageType == 'all') {
      return _filteredMessages;
    } else if (messageType == 'unread') {
      return _filteredMessages
          .where((message) => (message['unreadCount'] ?? 0) > 0)
          .toList();
    } else if (messageType == 'system') {
      return _filteredMessages
          .where(
            (message) => (message['type']?.toString() ?? '') == 'system',
          )
          .toList();
    } else if (messageType == 'work') {
      return _filteredMessages
          .where((message) => (message['type']?.toString() ?? '') == 'work')
          .toList();
    } else {
      return [];
    }
  }

  String _getMessageTypeDisplayName(String messageType) {
    switch (messageType) {
      case 'all':
        return LocalizationService.t('all');
      case 'unread':
        return LocalizationService.t('unread');
      case 'system':
        return LocalizationService.t('system');
      case 'work':
        return LocalizationService.t('work');
      default:
        return messageType;
    }
  }

  IconData _getIconData(String iconName) {
    switch (iconName) {
      case 'notifications':
        return Icons.notifications;
      case 'assignment_turned_in':
        return Icons.assignment_turned_in;
      case 'group':
        return Icons.group;
      case 'event':
        return Icons.event;
      case 'warning':
        return Icons.warning;
      default:
        return Icons.message;
    }
  }

  void _openMessage(Map<String, dynamic> message) {
    // 检查消息数据是否有效
    final messageTitle = message['title']?.toString() ?? '';
    final messageUrl = message['url'] as String? ?? '';

    if (messageTitle.isEmpty) {
      // 消息标题为空，显示提示信息
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(LocalizationService.t('message_data_invalid')),
          backgroundColor: AppTheme.warningColor,
        ),
      );
      return;
    }

    // 标记消息为已读
    _markMessageAsRead(message);

    // 如果消息URL为空，使用默认的详情页面
    final targetUrl = messageUrl.isNotEmpty
        ? messageUrl
        : 'assets/html/message_detail.html?id=${_getMessageId(messageTitle)}';

    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => WebViewScreen(
          title: messageTitle,
          url: targetUrl,
        ),
      ),
    );
  }

  void _markMessageAsRead(Map<String, dynamic> message) {
    setState(() {
      // 找到对应的消息并将未读计数设为0
      final messageIndex = _messages.indexWhere(
        (m) => m['id'] == message['id'],
      );
      if (messageIndex != -1) {
        _messages[messageIndex]['unreadCount'] = 0;
      }
    });

    // TODO: 这里可以调用API将已读状态同步到服务器
    // ApiService.markMessageAsRead(message['id']);
  }

  String _getMessageId(String messageTitle) {
    if (messageTitle == LocalizationService.t('system_maintenance_notice')) {
      return '1';
    } else if (messageTitle == LocalizationService.t('new_version_release')) {
      return '2';
    } else if (messageTitle == LocalizationService.t('meeting_reminder')) {
      return '3';
    } else if (messageTitle == LocalizationService.t('task_assignment')) {
      return '4';
    } else if (messageTitle == LocalizationService.t('approval_notification')) {
      return '5';
    } else {
      return '1';
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    _searchFocusNode.dispose();
    super.dispose();
  }
}
