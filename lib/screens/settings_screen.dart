import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../constants/app_theme.dart';
import '../screens/login_screen.dart';
import '../screens/server_config_screen.dart';
import '../screens/network_test_screen.dart';
import '../screens/error_log_screen.dart';
import '../screens/workspace_cache_demo_screen.dart';
import '../test_api_calls.dart';
import '../test_empty_data_demo.dart';
import '../test_data_cleanup.dart';
import '../services/localization_service.dart';
import 'test_404_screen.dart';
import 'test_webview_404_screen.dart';
import '../services/theme_service.dart';
import '../services/auth_service.dart';
import '../main.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  String _currentLanguage = LocalizationService.currentLanguage;
  String _currentTheme = ThemeService.currentTheme;
  Map<String, dynamic>? _userInfo;
  bool _isLoadingUserInfo = true;

  @override
  void initState() {
    super.initState();
    _loadCurrentSettings();
    _loadUserInfo();
  }

  void _loadCurrentSettings() {
    setState(() {
      _currentLanguage = LocalizationService.currentLanguage;
      _currentTheme = ThemeService.currentTheme;
    });
  }

  Future<void> _loadUserInfo() async {
    try {
      final authService = AuthService();
      final userInfoString = await authService.getUserInfo();

      if (userInfoString != null && userInfoString.isNotEmpty) {
        // 尝试解析用户信息JSON
        try {
          final userInfoJson = jsonDecode(userInfoString);
          setState(() {
            _userInfo = userInfoJson;
            _isLoadingUserInfo = false;
          });
        } catch (e) {
          // JSON解析失败，使用默认信息
          setState(() {
            _userInfo = null;
            _isLoadingUserInfo = false;
          });
        }
      } else {
        // 没有用户信息，使用默认信息
        setState(() {
          _userInfo = null;
          _isLoadingUserInfo = false;
        });
      }
    } catch (e) {
      // 获取用户信息失败，使用默认信息
      setState(() {
        _userInfo = null;
        _isLoadingUserInfo = false;
      });
    }
  }

  String _getUserDisplayName() {
    if (_userInfo != null) {
      // 尝试从用户信息中获取显示名称
      final name = _userInfo!['name'] ?? _userInfo!['username'] ?? _userInfo!['displayName'];
      if (name != null && name.toString().isNotEmpty) {
        return name.toString();
      }
    }
    // 如果没有用户信息或名称为空，使用默认值
    return LocalizationService.t('manager_zhang');
  }

  String _getUserDepartmentAndPosition() {
    if (_userInfo != null) {
      final department = _userInfo!['department']?.toString() ?? '';
      final position = _userInfo!['position']?.toString() ?? '';

      if (department.isNotEmpty && position.isNotEmpty) {
        return '$department · $position';
      } else if (department.isNotEmpty) {
        return department;
      } else if (position.isNotEmpty) {
        return position;
      }
    }
    // 如果没有用户信息或部门职位为空，使用默认值
    return LocalizationService.t('product_manager');
  }

  // 获取翻译后的设置菜单
  List<Map<String, dynamic>> get _translatedSettingsMenus => [
    {
      'category': LocalizationService.t('account_settings'),
      'items': [
        {
          'title': LocalizationService.t('account_security'),
          'icon': 'security',
          'color': '0xFF3366CC',
        },
        {
          'title': LocalizationService.t('message_notifications'),
          'icon': 'notifications',
          'color': '0xFFFF9900',
        },
        {
          'title': LocalizationService.t('privacy_settings'),
          'icon': 'privacy_tip',
          'color': '0xFF13C2C2',
        },
      ],
    },
    {
      'category': LocalizationService.t('enterprise_settings'),
      'items': [
        {
          'title': LocalizationService.t('enterprise_info'),
          'icon': 'business',
          'color': '0xFF1890FF',
        },
        {
          'title': LocalizationService.t('department_management'),
          'icon': 'people',
          'color': '0xFF722ED1',
        },
      ],
    },
    {
      'category': LocalizationService.t('app_preferences'),
      'items': [
        {
          'title': LocalizationService.t('app_sorting'),
          'icon': 'apps',
          'color': '0xFF52C41A',
        },
        {
          'title': LocalizationService.t('appearance_settings'),
          'icon': 'palette',
          'color': '0xFFFF9900',
          'subtitle': ThemeService.getThemeDisplayName(_currentTheme),
        },
        {
          'title': LocalizationService.t('language_settings'),
          'icon': 'language',
          'color': '0xFF722ED1',
          'subtitle': LocalizationService.getLanguageDisplayName(
            _currentLanguage,
          ),
        },
        {
          'title': LocalizationService.t('server_configuration'),
          'icon': 'dns',
          'color': '0xFF13C2C2',
        },
        {
          'title': LocalizationService.t('network_test_title'),
          'icon': 'network_check',
          'color': '0xFF52C41A',
          'subtitle': LocalizationService.t('network_test_subtitle'),
        },
        {
          'title': LocalizationService.t('error_log_title'),
          'icon': 'bug_report',
          'color': '0xFFFF5722',
          'subtitle': LocalizationService.t('error_log_subtitle'),
        },
        {
          'title': 'API调用测试',
          'icon': 'api',
          'color': '0xFF9C27B0',
          'subtitle': '测试应用和工作台API调用',
        },
        {
          'title': '空数据处理测试',
          'icon': 'data_usage',
          'color': '0xFFE91E63',
          'subtitle': '测试真实接口空数据处理逻辑',
        },
        {
          'title': '工作台缓存演示',
          'icon': 'cached',
          'color': '0xFF00BCD4',
          'subtitle': '演示工作台页面的缓存机制',
        },
        {
          'title': '数据清理测试',
          'icon': 'delete_sweep',
          'color': '0xFFFF5722',
          'subtitle': '测试退出登录数据清理功能',
        },
        {
          'title': '404页面测试',
          'icon': 'error_outline',
          'color': '0xFFFF9800',
          'subtitle': '测试404错误页面显示',
        },
        {
          'title': 'WebView 404测试',
          'icon': 'web',
          'color': '0xFF9C27B0',
          'subtitle': '测试WebView加载错误和超时处理',
        },
      ],
    },
    {
      'category': LocalizationService.t('about_help'),
      'items': [
        {
          'title': LocalizationService.t('about_us'),
          'icon': 'info',
          'color': '0xFFF5222D',
        },
        {
          'title': LocalizationService.t('help_center'),
          'icon': 'help',
          'color': '0xFFFAAD14',
        },
        {
          'title': LocalizationService.t('feedback'),
          'icon': 'feedback',
          'color': '0xFF1890FF',
        },
      ],
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.getBackgroundColor(context),
      appBar: AppBar(title: Text(LocalizationService.t('settings'))),
      body: Column(
        children: [
          // 固定的用户信息卡片
          _buildUserProfile(),
          const SizedBox(height: AppTheme.paddingMedium),
          // 可滚动的设置菜单部分
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  // 设置菜单
                  ..._translatedSettingsMenus.map((menuGroup) {
                    return _buildMenuGroup(menuGroup);
                  }),
                  const SizedBox(height: AppTheme.paddingLarge),
                  // 免责声明
                  _buildDisclaimer(),
                  const SizedBox(height: AppTheme.paddingLarge),
                  // 退出登录按钮
                  _buildLogoutButton(),
                  const SizedBox(height: AppTheme.paddingLarge),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUserProfile() {
    return Container(
      margin: const EdgeInsets.all(AppTheme.paddingMedium),
      padding: const EdgeInsets.all(AppTheme.paddingLarge),
      decoration: BoxDecoration(
        color: AppTheme.getCardColor(context),
        borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
        boxShadow: [AppTheme.getCardShadow(context)],
      ),
      child: Row(
        children: [
          // 头像
          Container(
            width: 70,
            height: 70,
            decoration: BoxDecoration(
              color: AppTheme.primaryColor,
              borderRadius: BorderRadius.circular(35),
            ),
            child: const Icon(Icons.person, size: 36, color: AppTheme.white),
          ),
          const SizedBox(width: AppTheme.paddingMedium),
          // 用户信息
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _getUserDisplayName(),
                  style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  _getUserDepartmentAndPosition(),
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppTheme.getTextSecondaryColor(context),
                  ),
                ),
              ],
            ),
          ),
          // 箭头
          const Icon(Icons.chevron_right, color: AppTheme.textTertiary),
        ],
      ),
    );
  }

  Widget _buildMenuGroup(Map<String, dynamic> menuGroup) {
    return Container(
      margin: const EdgeInsets.symmetric(
        horizontal: AppTheme.paddingMedium,
        vertical: AppTheme.paddingSmall,
      ),
      decoration: BoxDecoration(
        color: AppTheme.getCardColor(context),
        borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
        boxShadow: [AppTheme.getCardShadow(context)],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 分组标题
          Padding(
            padding: const EdgeInsets.fromLTRB(
              AppTheme.paddingMedium,
              AppTheme.paddingMedium,
              AppTheme.paddingMedium,
              AppTheme.paddingSmall,
            ),
            child: Text(
              menuGroup['category'],
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: AppTheme.textTertiary,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          // 菜单项
          ...((menuGroup['items'] as List).map((item) {
            return _buildMenuItem(item);
          })),
        ],
      ),
    );
  }

  Widget _buildMenuItem(Map<String, dynamic> item) {
    return ListTile(
      contentPadding: const EdgeInsets.symmetric(
        horizontal: AppTheme.paddingMedium,
        vertical: 4,
      ),
      leading: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: Color(int.parse(item['color'])),
          borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
        ),
        child: Icon(
          _getIconData(item['icon']),
          color: AppTheme.white,
          size: 20,
        ),
      ),
      title: Text(
        item['title'],
        style: Theme.of(
          context,
        ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w500),
      ),
      subtitle:
          _getSubtitleText(item) != null
              ? Text(
                _getSubtitleText(item)!,
                style: Theme.of(
                  context,
                ).textTheme.bodySmall?.copyWith(color: AppTheme.textTertiary),
              )
              : null,
      trailing: const Icon(Icons.chevron_right, color: AppTheme.textTertiary),
      onTap: () => _handleMenuTap(item),
    );
  }

  Widget _buildLogoutButton() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: AppTheme.paddingMedium),
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _logout,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppTheme.errorColor,
          foregroundColor: AppTheme.white,
          padding: const EdgeInsets.symmetric(vertical: AppTheme.paddingMedium),
        ),
        child: Text(LocalizationService.t('logout')),
      ),
    );
  }

  Widget _buildDisclaimer() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: AppTheme.paddingMedium),
      padding: const EdgeInsets.all(AppTheme.paddingMedium),
      decoration: BoxDecoration(
        color: AppTheme.getCardColor(context),
        borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
        border: Border.all(
          color: AppTheme.getBorderColor(context),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.info_outline,
                color: AppTheme.getTextSecondaryColor(context),
                size: 20,
              ),
              const SizedBox(width: AppTheme.paddingSmall),
              Text(
                LocalizationService.t('disclaimer_title'),
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppTheme.getTextPrimaryColor(context),
                ),
              ),
            ],
          ),
          const SizedBox(height: AppTheme.paddingSmall),
          Text(
            LocalizationService.t('disclaimer_content'),
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: AppTheme.getTextSecondaryColor(context),
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }

  String? _getSubtitleText(Map<String, dynamic> item) {
    final title = item['title'];
    if (title == LocalizationService.t('language_settings')) {
      return LocalizationService.getLanguageDisplayName(_currentLanguage);
    } else if (title == LocalizationService.t('appearance_settings')) {
      return ThemeService.getThemeDisplayName(_currentTheme);
    } else {
      return item['subtitle'];
    }
  }

  IconData _getIconData(String iconName) {
    switch (iconName) {
      case 'security':
        return Icons.security;
      case 'notifications':
        return Icons.notifications;
      case 'privacy_tip':
        return Icons.privacy_tip;
      case 'business':
        return Icons.business;
      case 'people':
        return Icons.people;
      case 'apps':
        return Icons.apps;
      case 'palette':
        return Icons.palette;
      case 'language':
        return Icons.language;
      case 'dns':
        return Icons.dns;
      case 'info':
        return Icons.info;
      case 'help':
        return Icons.help;
      case 'feedback':
        return Icons.feedback;
      case 'bug_report':
        return Icons.bug_report;
      case 'network_check':
        return Icons.network_check;
      default:
        return Icons.settings;
    }
  }

  void _handleMenuTap(Map<String, dynamic> item) {
    final title = item['title'];

    if (title == LocalizationService.t('language_settings')) {
      _showLanguageSettings();
    } else if (title == LocalizationService.t('appearance_settings')) {
      _showThemeSettings();
    } else if (title == LocalizationService.t('server_configuration')) {
      _openServerConfig();
    } else if (title == LocalizationService.t('network_test_title')) {
      _openNetworkTest();
    } else if (title == LocalizationService.t('error_log_title')) {
      _openErrorLog();
    } else if (title == 'API调用测试') {
      _openApiTest();
    } else if (title == '空数据处理测试') {
      _openEmptyDataTest();
    } else if (title == '工作台缓存演示') {
      _openWorkspaceCacheDemo();
    } else if (title == '数据清理测试') {
      _openDataCleanupTest();
    } else if (title == '404页面测试') {
      _open404Test();
    } else if (title == 'WebView 404测试') {
      _openWebView404Test();
    } else if (title == LocalizationService.t('about_us')) {
      _showAboutDialog();
    } else if (title == LocalizationService.t('help_center')) {
      _showHelpCenter();
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            '$title ${LocalizationService.t('feature_under_development')}',
          ),
          backgroundColor: AppTheme.primaryColor,
        ),
      );
    }
  }

  void _logout() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(LocalizationService.t('confirm_logout')),
          content: Text(LocalizationService.t('logout_message')),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(LocalizationService.t('cancel')),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                _performLogout();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.errorColor,
              ),
              child: Text(LocalizationService.t('logout_confirm')),
            ),
          ],
        );
      },
    );
  }

  void _performLogout() async {
    try {
      // 显示加载提示
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(LocalizationService.t('clearing_user_data')),
            backgroundColor: AppTheme.primaryColor,
            duration: const Duration(seconds: 2),
          ),
        );
      }

      // 使用AuthService清除所有用户数据
      final authService = AuthService();
      await authService.clearAllUserData();

      // 导航到登录界面
      if (mounted) {
        Navigator.of(context).pushAndRemoveUntil(
          MaterialPageRoute(builder: (context) => const LoginScreen()),
          (route) => false,
        );

        // 显示清除完成提示
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(LocalizationService.t('user_data_cleared')),
            backgroundColor: AppTheme.successColor,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      debugPrint('退出登录失败: $e');

      // 如果清除失败，仍然跳转到登录页面
      if (mounted) {
        Navigator.of(context).pushAndRemoveUntil(
          MaterialPageRoute(builder: (context) => const LoginScreen()),
          (route) => false,
        );

        // 显示错误提示
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(LocalizationService.t('logout_error')),
            backgroundColor: AppTheme.errorColor,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    }
  }

  void _showLanguageSettings() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(LocalizationService.t('select_language')),
            content: SizedBox(
              width: double.maxFinite,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  RadioListTile<String>(
                    title: Text(LocalizationService.t('chinese')),
                    value: 'zh',
                    groupValue: _currentLanguage,
                    onChanged: (value) async {
                      await LocalizationService.setLanguage('zh');
                      if (mounted) {
                        setState(() {
                          _currentLanguage = 'zh';
                        });
                        if (mounted) {
                          Navigator.of(context).pop();
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text(
                                LocalizationService.t('language_updated'),
                              ),
                              backgroundColor: AppTheme.successColor,
                            ),
                          );
                          // Restart app to apply new language
                          _restartApp();
                        }
                      }
                    },
                  ),
                  RadioListTile<String>(
                    title: Text(LocalizationService.t('english')),
                    value: 'en',
                    groupValue: _currentLanguage,
                    onChanged: (value) async {
                      await LocalizationService.setLanguage('en');
                      if (mounted) {
                        setState(() {
                          _currentLanguage = 'en';
                        });
                        if (mounted) {
                          Navigator.of(context).pop();
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text(
                                LocalizationService.t('language_updated'),
                              ),
                              backgroundColor: AppTheme.successColor,
                            ),
                          );
                          // Restart app to apply new language
                          _restartApp();
                        }
                      }
                    },
                  ),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text(LocalizationService.t('cancel')),
              ),
            ],
          ),
    );
  }

  void _restartApp() {
    // 只刷新当前页面，不跳转到首页
    if (mounted) {
      setState(() {
        // 重新初始化当前语言状态
        _currentLanguage = LocalizationService.currentLanguage;
      });

      // 通知应用更新语言，但不进行页面跳转
      // 使用MyApp的静态方法来重建应用
      MyApp.rebuildApp();
    }
  }

  void _showThemeSettings() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(LocalizationService.t('select_theme')),
            content: SizedBox(
              width: double.maxFinite,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  RadioListTile<String>(
                    title: Text(LocalizationService.t('light_theme')),
                    value: 'light',
                    groupValue: _currentTheme,
                    onChanged: (value) async {
                      await ThemeService.setTheme('light');
                      if (mounted) {
                        setState(() {
                          _currentTheme = 'light';
                        });
                        if (mounted) {
                          Navigator.of(context).pop();
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text(
                                LocalizationService.t('theme_updated'),
                              ),
                              backgroundColor: AppTheme.successColor,
                            ),
                          );
                          // Theme will take effect automatically, no restart needed
                        }
                      }
                    },
                  ),
                  RadioListTile<String>(
                    title: Text(LocalizationService.t('dark_theme')),
                    value: 'dark',
                    groupValue: _currentTheme,
                    onChanged: (value) async {
                      await ThemeService.setTheme('dark');
                      if (mounted) {
                        setState(() {
                          _currentTheme = 'dark';
                        });
                        if (mounted) {
                          Navigator.of(context).pop();
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text(
                                LocalizationService.t('theme_updated'),
                              ),
                              backgroundColor: AppTheme.successColor,
                            ),
                          );
                          // Theme will take effect automatically, no restart needed
                        }
                      }
                    },
                  ),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text(LocalizationService.t('cancel')),
              ),
            ],
          ),
    );
  }

  void _openServerConfig() {
    Navigator.of(
      context,
    ).push(MaterialPageRoute(builder: (context) => const ServerConfigScreen()));
  }

  void _openNetworkTest() {
    Navigator.of(
      context,
    ).push(MaterialPageRoute(builder: (context) => const NetworkTestScreen()));
  }

  void _openErrorLog() {
    Navigator.of(
      context,
    ).push(MaterialPageRoute(builder: (context) => const ErrorLogScreen()));
  }

  void _openApiTest() {
    Navigator.of(
      context,
    ).push(MaterialPageRoute(builder: (context) => const TestApiCallsScreen()));
  }

  void _openEmptyDataTest() {
    Navigator.of(
      context,
    ).push(MaterialPageRoute(builder: (context) => const EmptyDataTestScreen()));
  }

  void _openWorkspaceCacheDemo() {
    Navigator.of(
      context,
    ).push(MaterialPageRoute(builder: (context) => const WorkspaceCacheDemoScreen()));
  }

  void _showAboutDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(LocalizationService.t('about_us')),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  LocalizationService.t('app_name'),
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text('${LocalizationService.t('version')}: 1.0.0'),
                const SizedBox(height: 8),
                Text('${LocalizationService.t('build_time')}: 2024-12-28'),
                const SizedBox(height: 16),
                Text(
                  LocalizationService.t('app_description'),
                  style: const TextStyle(fontSize: 14),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text(LocalizationService.t('ok')),
              ),
            ],
          ),
    );
  }

  void _showHelpCenter() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(LocalizationService.t('help_center')),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  LocalizationService.t('common_questions'),
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 12),
                Text(LocalizationService.t('help_question_1')),
                Text(LocalizationService.t('help_question_2')),
                Text(LocalizationService.t('help_question_3')),
                Text(LocalizationService.t('help_question_4')),
                const SizedBox(height: 16),
                Text(
                  '${LocalizationService.t('technical_support')}: <EMAIL>',
                  style: const TextStyle(
                    color: AppTheme.primaryColor,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text(LocalizationService.t('ok')),
              ),
            ],
          ),
    );
  }

  void _openDataCleanupTest() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const DataCleanupTestScreen(),
      ),
    );
  }

  void _open404Test() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const Test404Screen(),
      ),
    );
  }

  void _openWebView404Test() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const TestWebView404Screen(),
      ),
    );
  }
}
