import 'package:flutter/material.dart';
import '../constants/app_theme.dart';
import '../services/localization_service.dart';
import 'webview_screen.dart';

/// 测试WebView 404页面的屏幕
class TestWebView404Screen extends StatelessWidget {
  const TestWebView404Screen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(LocalizationService.t('test_webview_404')),
        backgroundColor: AppTheme.getSurfaceColor(context),
        foregroundColor: AppTheme.getTextPrimaryColor(context),
      ),
      body: Padding(
        padding: const EdgeInsets.all(AppTheme.paddingLarge),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Text(
              LocalizationService.t('test_webview_404_description'),
              style: TextStyle(
                fontSize: 16,
                color: AppTheme.getTextSecondaryColor(context),
                height: 1.5,
              ),
            ),
            
            const SizedBox(height: AppTheme.paddingLarge),
            
            // 测试无效URL
            _buildTestButton(
              context,
              title: LocalizationService.t('test_invalid_url'),
              description: LocalizationService.t('test_invalid_url_desc'),
              url: 'invalid-url-test',
              icon: Icons.link_off,
              color: AppTheme.errorColor,
            ),
            
            const SizedBox(height: AppTheme.paddingMedium),
            
            // 测试不存在的域名
            _buildTestButton(
              context,
              title: LocalizationService.t('test_nonexistent_domain'),
              description: LocalizationService.t('test_nonexistent_domain_desc'),
              url: 'https://nonexistent-domain-12345.com',
              icon: Icons.dns,
              color: AppTheme.warningColor,
            ),
            
            const SizedBox(height: AppTheme.paddingMedium),
            
            // 测试连接超时
            _buildTestButton(
              context,
              title: LocalizationService.t('test_timeout'),
              description: LocalizationService.t('test_timeout_desc'),
              url: 'https://httpstat.us/200?sleep=35000',
              icon: Icons.timer,
              color: AppTheme.infoColor,
            ),
            
            const SizedBox(height: AppTheme.paddingMedium),
            
            // 测试404页面
            _buildTestButton(
              context,
              title: LocalizationService.t('test_404_page'),
              description: LocalizationService.t('test_404_page_desc'),
              url: 'https://httpstat.us/404',
              icon: Icons.error_outline,
              color: AppTheme.primaryColor,
            ),
            
            const SizedBox(height: AppTheme.paddingLarge),
            
            Container(
              padding: const EdgeInsets.all(AppTheme.paddingMedium),
              decoration: BoxDecoration(
                color: AppTheme.infoColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(AppTheme.radiusSmall),
                border: Border.all(
                  color: AppTheme.infoColor.withOpacity(0.3),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.info_outline,
                    color: AppTheme.infoColor,
                    size: 20,
                  ),
                  const SizedBox(width: AppTheme.paddingSmall),
                  Expanded(
                    child: Text(
                      LocalizationService.t('test_webview_note'),
                      style: TextStyle(
                        fontSize: 14,
                        color: AppTheme.infoColor,
                        height: 1.4,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTestButton(
    BuildContext context, {
    required String title,
    required String description,
    required String url,
    required IconData icon,
    required Color color,
  }) {
    return Card(
      elevation: 2,
      color: AppTheme.getCardColor(context),
      child: InkWell(
        onTap: () {
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => WebViewScreen(
                title: title,
                url: url,
              ),
            ),
          );
        },
        borderRadius: BorderRadius.circular(AppTheme.radiusSmall),
        child: Padding(
          padding: const EdgeInsets.all(AppTheme.paddingMedium),
          child: Row(
            children: [
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(AppTheme.radiusSmall),
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: 24,
                ),
              ),
              
              const SizedBox(width: AppTheme.paddingMedium),
              
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: AppTheme.getTextPrimaryColor(context),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      description,
                      style: TextStyle(
                        fontSize: 14,
                        color: AppTheme.getTextSecondaryColor(context),
                        height: 1.3,
                      ),
                    ),
                  ],
                ),
              ),
              
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: AppTheme.getTextSecondaryColor(context),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
