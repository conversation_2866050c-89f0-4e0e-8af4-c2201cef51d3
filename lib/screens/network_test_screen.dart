import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../constants/app_theme.dart';
import '../services/network_status_service.dart';
import '../services/http_client.dart';
import '../services/api_service.dart';
import '../services/cache_service.dart';
import '../services/localization_service.dart';
import '../services/debug_log_service.dart';

/// 网络错误处理测试页面
class NetworkTestScreen extends StatefulWidget {
  const NetworkTestScreen({super.key});

  @override
  State<NetworkTestScreen> createState() => _NetworkTestScreenState();
}

class _NetworkTestScreenState extends State<NetworkTestScreen> {
  final NetworkStatusService _networkStatus = NetworkStatusService();
  final HttpClient _httpClient = HttpClient();
  final DebugLogService _debugLog = DebugLogService();

  NetworkStatus? _currentStatus;
  Map<String, int> _errorStats = {};
  bool _isLoading = false;
  String _lastResult = '';
  bool _logEnabled = false;

  @override
  void initState() {
    super.initState();
    _initializeNetworkStatus();
    _loadErrorStats();
    _initializeDebugLog();
  }

  void _initializeDebugLog() async {
    await _debugLog.initialize();
    setState(() {
      _logEnabled = _debugLog.isEnabled;
    });
  }

  void _initializeNetworkStatus() {
    _currentStatus = _networkStatus.currentStatus;
    
    _networkStatus.statusStream.listen((status) {
      if (mounted) {
        setState(() {
          _currentStatus = status;
        });
      }
    });
  }

  void _loadErrorStats() {
    setState(() {
      _errorStats = _httpClient.getErrorStats();
    });
  }

  Future<void> _testApiCall(String testType) async {
    setState(() {
      _isLoading = true;
      _lastResult = '';
    });

    try {
      Map<String, dynamic> result;
      
      switch (testType) {
        case 'home_cache':
          result = await ApiService.getHomeInfo();
          break;
        case 'home_refresh':
          result = await ApiService.getHomeInfo(forceRefresh: true);
          break;
        case 'messages_cache':
          result = await ApiService.getMessages();
          break;
        case 'messages_refresh':
          result = await ApiService.getMessages(forceRefresh: true);
          break;
        case 'apps_cache':
          result = await ApiService.getAppList();
          break;
        case 'apps_refresh':
          result = await ApiService.getAppList(forceRefresh: true);
          break;
        default:
          result = {'success': false, 'message': LocalizationService.t('unknown_test_type')};
      }

      setState(() {
        _lastResult = _formatResult(result);
      });

    } catch (e) {
      setState(() {
        _lastResult = '${LocalizationService.t('error')}: ${e.toString()}';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
      _loadErrorStats();
    }
  }

  String _formatResult(Map<String, dynamic> result) {
    final success = result['success'] ?? false;
    final message = result['message'] ?? '';
    final fromCache = result['fromCache'] ?? false;
    final error = result['error'];

    String formatted = '${LocalizationService.t('status')}: ${success ? LocalizationService.t('success') : LocalizationService.t('failed')}\n';
    formatted += '${LocalizationService.t('message')}: $message\n';
    formatted += '${LocalizationService.t('source')}: ${fromCache ? LocalizationService.t('cache') : LocalizationService.t('network')}\n';

    if (error != null) {
      formatted += '${LocalizationService.t('error')}: $error\n';
    }

    if (result['data'] != null) {
      final data = result['data'] as Map<String, dynamic>;
      formatted += '${LocalizationService.t('data_items')}: ${data.keys.length}\n';
    }

    return formatted;
  }

  Future<void> _clearCache() async {
    await CacheService.clearAllCache();
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(LocalizationService.t('clear_all_cache')),
          backgroundColor: AppTheme.successColor,
        ),
      );
    }
  }

  Future<void> _clearExpiredCache() async {
    await CacheService.clearExpiredCache();
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(LocalizationService.t('clear_expired_cache')),
          backgroundColor: AppTheme.successColor,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(LocalizationService.t('network_test_title')),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildNetworkStatusCard(),
            const SizedBox(height: 16),
            _buildTestButtonsCard(),
            const SizedBox(height: 16),
            _buildResultCard(),
            const SizedBox(height: 16),
            _buildErrorStatsCard(),
            const SizedBox(height: 16),
            _buildCacheControlCard(),
            const SizedBox(height: 16),
            _buildDebugLogCard(),
          ],
        ),
      ),
    );
  }

  Widget _buildNetworkStatusCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              LocalizationService.t('network_status'),
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            if (_currentStatus != null) ...[
              _buildStatusRow(LocalizationService.t('connection_status'), _currentStatus!.isConnected ? LocalizationService.t('connected') : LocalizationService.t('disconnected')),
              _buildStatusRow(LocalizationService.t('connection_type'), _getNetworkTypeText(_currentStatus!.type)),
              _buildStatusRow(LocalizationService.t('metered_network'), _currentStatus!.isMetered ? LocalizationService.t('yes') : LocalizationService.t('no')),
              _buildStatusRow(LocalizationService.t('last_check'), _formatTime(_currentStatus!.lastChecked)),
            ] else
              Text(LocalizationService.t('network_status_unknown')),
          ],
        ),
      ),
    );
  }

  Widget _buildTestButtonsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              LocalizationService.t('api_test'),
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                _buildTestButton(LocalizationService.t('home_cache'), 'home_cache'),
                _buildTestButton(LocalizationService.t('home_refresh'), 'home_refresh'),
                _buildTestButton(LocalizationService.t('messages_cache'), 'messages_cache'),
                _buildTestButton(LocalizationService.t('messages_refresh'), 'messages_refresh'),
                _buildTestButton(LocalizationService.t('apps_cache'), 'apps_cache'),
                _buildTestButton(LocalizationService.t('apps_refresh'), 'apps_refresh'),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildResultCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              LocalizationService.t('test_results'),
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            if (_isLoading)
              const Center(child: CircularProgressIndicator())
            else if (_lastResult.isNotEmpty)
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  _lastResult,
                  style: const TextStyle(fontFamily: 'monospace'),
                ),
              )
            else
              Text(LocalizationService.t('click_button_to_test')),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorStatsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              LocalizationService.t('error_stats'),
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            if (_errorStats.isEmpty)
              Text(LocalizationService.t('no_error_records'))
            else
              ..._errorStats.entries.map((entry) =>
                _buildStatusRow(entry.key, '${entry.value} ${LocalizationService.t('times')}')
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildCacheControlCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              LocalizationService.t('cache_control'),
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: _clearCache,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.warningColor,
                      foregroundColor: Colors.white,
                    ),
                    child: Text(LocalizationService.t('clear_all_cache')),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _clearExpiredCache,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.infoColor,
                      foregroundColor: Colors.white,
                    ),
                    child: Text(LocalizationService.t('clear_expired_cache')),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Text(
            value,
            style: const TextStyle(fontWeight: FontWeight.w500),
          ),
        ],
      ),
    );
  }

  Widget _buildTestButton(String label, String testType) {
    return ElevatedButton(
      onPressed: _isLoading ? null : () => _testApiCall(testType),
      style: ElevatedButton.styleFrom(
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
      ),
      child: Text(label),
    );
  }

  String _getNetworkTypeText(NetworkType type) {
    switch (type) {
      case NetworkType.wifi:
        return LocalizationService.t('wifi_network');
      case NetworkType.mobile:
        return LocalizationService.t('mobile_network');
      case NetworkType.ethernet:
        return LocalizationService.t('ethernet_network');
      case NetworkType.none:
        return LocalizationService.t('disconnected');
      case NetworkType.unknown:
        return LocalizationService.t('unknown_network');
    }
  }

  String _formatTime(DateTime time) {
    return '${time.hour.toString().padLeft(2, '0')}:'
           '${time.minute.toString().padLeft(2, '0')}:'
           '${time.second.toString().padLeft(2, '0')}';
  }

  Widget _buildDebugLogCard() {
    final logs = _debugLog.logs;
    final logStats = _debugLog.getLogStats();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  LocalizationService.t('debug_log_management'),
                  style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                Switch(
                  value: _logEnabled,
                  onChanged: (value) async {
                    await _debugLog.setEnabled(value);
                    setState(() {
                      _logEnabled = value;
                    });
                  },
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              '${LocalizationService.t('log_status')}: ${_logEnabled ? LocalizationService.t('log_enabled') : LocalizationService.t('log_disabled')}',
              style: TextStyle(
                color: _logEnabled ? AppTheme.successColor : AppTheme.errorColor,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Text('${LocalizationService.t('total_logs')}: ${logs.length}'),
            const SizedBox(height: 8),
            Row(
              children: [
                Text('DEBUG: ${logStats[LogLevel.debug] ?? 0}'),
                const SizedBox(width: 16),
                Text('INFO: ${logStats[LogLevel.info] ?? 0}'),
                const SizedBox(width: 16),
                Text('WARN: ${logStats[LogLevel.warning] ?? 0}'),
                const SizedBox(width: 16),
                Text('ERROR: ${logStats[LogLevel.error] ?? 0}'),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: logs.isEmpty ? null : () => _showLogViewer(),
                    icon: const Icon(Icons.visibility),
                    label: Text(LocalizationService.t('view_logs')),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.primaryColor,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: logs.isEmpty ? null : () => _copyLogs(),
                    icon: const Icon(Icons.copy),
                    label: Text(LocalizationService.t('copy_logs')),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.infoColor,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: logs.isEmpty ? null : () => _clearLogs(),
                    icon: const Icon(Icons.clear),
                    label: Text(LocalizationService.t('clear_logs')),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.errorColor,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _showLogViewer() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(LocalizationService.t('debug_logs_title')),
        content: SizedBox(
          width: double.maxFinite,
          height: 400,
          child: SingleChildScrollView(
            child: SelectableText(
              _debugLog.getLogsAsText(),
              style: const TextStyle(fontFamily: 'monospace', fontSize: 12),
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(LocalizationService.t('close')),
          ),
          TextButton(
            onPressed: () {
              _copyLogs();
              Navigator.of(context).pop();
            },
            child: Text(LocalizationService.t('copy_logs')),
          ),
        ],
      ),
    );
  }

  void _copyLogs() async {
    final logsText = _debugLog.getLogsAsText();
    if (logsText.isNotEmpty) {
      await Clipboard.setData(ClipboardData(text: logsText));
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(LocalizationService.t('logs_copied_success')),
            backgroundColor: AppTheme.successColor,
          ),
        );
      }
    }
  }

  void _clearLogs() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(LocalizationService.t('confirm_clear_logs')),
        content: Text(LocalizationService.t('clear_logs_message')),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(LocalizationService.t('cancel')),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: AppTheme.errorColor),
            child: Text(LocalizationService.t('confirm')),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      await _debugLog.clearLogs();
      setState(() {});
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(LocalizationService.t('debug_logs_cleared')),
            backgroundColor: AppTheme.successColor,
          ),
        );
      }
    }
  }
}
