import 'package:flutter/material.dart';
import '../constants/app_theme.dart';
import '../services/localization_service.dart';
import '../services/app_state_manager.dart';
import '../widgets/animated_widgets.dart';
import '../widgets/network_status_indicator.dart';
import 'home_screen.dart';
import 'apps_screen.dart';
import 'workspace_screen.dart';
import 'messages_screen.dart';
import 'settings_screen.dart';

class MainScreen extends StatefulWidget {
  const MainScreen({super.key});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen>
    with TickerProviderStateMixin {
  int _currentIndex = 0;
  late AnimationController _animationController;

  // 强制刷新状态
  bool _needsForceRefresh = false;
  bool _isInitialized = false;

  late final List<Widget> _screens;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    // 延迟初始化，先检查强制刷新状态
    _initializeApp();
  }

  /// 初始化应用，检查是否需要强制刷新
  Future<void> _initializeApp() async {
    try {
      final appStateManager = AppStateManager();
      _needsForceRefresh = await appStateManager.shouldForceRefresh();

      if (_needsForceRefresh) {
        debugPrint('MainScreen: 检测到需要强制刷新，开始强制刷新流程');
        await appStateManager.startForceRefresh();
      }

      // 创建页面实例
      _screens = [
        const HomeScreen(),
        const AppsScreen(),
        const WorkspaceScreen(),
        const MessagesScreen(),
        const SettingsScreen(),
      ];

      setState(() {
        _isInitialized = true;
      });

      // 如果进行了强制刷新，在页面加载完成后标记完成
      if (_needsForceRefresh) {
        // 延迟一段时间确保页面加载完成
        Future.delayed(const Duration(seconds: 2), () async {
          await appStateManager.completeForceRefresh();
          debugPrint('MainScreen: 强制刷新流程已完成');
        });
      }
    } catch (e) {
      debugPrint('MainScreen: 初始化失败: $e');
      // 即使失败也要显示页面
      _screens = [
        const HomeScreen(),
        const AppsScreen(),
        const WorkspaceScreen(),
        const MessagesScreen(),
        const SettingsScreen(),
      ];
      setState(() {
        _isInitialized = true;
      });
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  List<BottomNavigationBarItem> get _bottomNavItems => [
    BottomNavigationBarItem(
      icon: Semantics(
        label: '${LocalizationService.t('home')}${LocalizationService.t('page_navigation')}',
        child: const Icon(Icons.home),
      ),
      label: LocalizationService.t('home'),
    ),
    BottomNavigationBarItem(
      icon: Semantics(
        label: '${LocalizationService.t('apps')}${LocalizationService.t('page_navigation')}',
        child: const Icon(Icons.apps),
      ),
      label: LocalizationService.t('apps'),
    ),
    BottomNavigationBarItem(
      icon: Semantics(
        label: '${LocalizationService.t('workspace')}${LocalizationService.t('page_navigation')}',
        child: const Icon(Icons.desktop_windows),
      ),
      label: LocalizationService.t('workspace'),
    ),
    BottomNavigationBarItem(
      icon: Semantics(
        label: '${LocalizationService.t('messages')}${LocalizationService.t('page_navigation')}',
        child: const Icon(Icons.message),
      ),
      label: LocalizationService.t('messages'),
    ),
    BottomNavigationBarItem(
      icon: Semantics(
        label: '${LocalizationService.t('settings')}${LocalizationService.t('page_navigation')}',
        child: const Icon(Icons.person),
      ),
      label: LocalizationService.t('settings'),
    ),
  ];

  void _onTabTapped(int index) {
    if (index != _currentIndex) {
      setState(() {
        _currentIndex = index;
      });

      // 触发动画
      _animationController.forward().then((_) {
        _animationController.reset();
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    // 如果还没有初始化完成，显示加载状态
    if (!_isInitialized) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    return Scaffold(
      body: NetworkStatusBanner(
        child: IndexedStack(
          index: _currentIndex,
          children: _screens.map((screen) =>
            FadeInAnimation(
              duration: const Duration(milliseconds: 300),
              child: screen,
            ),
          ).toList(),
        ),
      ),
      bottomNavigationBar: Container(
        decoration: BoxDecoration(
          border: Border(
            top: BorderSide(color: AppTheme.getBorderColor(context), width: 1),
          ),
        ),
        child: BottomNavigationBar(
          currentIndex: _currentIndex,
          onTap: _onTabTapped,
          type: BottomNavigationBarType.fixed,
          backgroundColor: AppTheme.getSurfaceColor(context),
          selectedItemColor: AppTheme.primaryColor,
          unselectedItemColor: AppTheme.getTextSecondaryColor(context),
          selectedFontSize: AppTheme.fontSizeSmall,
          unselectedFontSize: AppTheme.fontSizeSmall,
          elevation: 0,
          items: _bottomNavItems,
        ),
      ),
    );
  }
}
