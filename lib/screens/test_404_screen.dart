import 'package:flutter/material.dart';
import '../services/localization_service.dart';
import 'page_not_found_screen.dart';

/// 测试404页面的屏幕
class Test404Screen extends StatelessWidget {
  const Test404Screen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('测试404页面'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const PageNotFoundScreen(
                      title: '测试页面',
                      description: '这是一个测试404页面',
                      errorMessage: 'net::ERR_NAME_NOT_RESOLVED',
                    ),
                  ),
                );
              },
              child: const Text('显示404页面'),
            ),
            const SizedBox(height: 20),
            Text('当前语言: ${LocalizationService.currentLanguage}'),
            const SizedBox(height: 10),
            Text('翻译测试: ${LocalizationService.t('page_load_failed')}'),
          ],
        ),
      ),
    );
  }
}
