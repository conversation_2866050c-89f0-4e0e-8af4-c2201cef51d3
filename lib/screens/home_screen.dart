import 'package:flutter/material.dart';
import 'package:carousel_slider/carousel_slider.dart';
import '../constants/app_theme.dart';
import '../services/api_service.dart';
import '../services/app_state_manager.dart';
import '../services/cache_service.dart';
import '../services/localization_service.dart';
import '../screens/webview_screen.dart';
import '../widgets/skeleton_widgets.dart';
import '../widgets/animated_widgets.dart';
import '../widgets/page_transitions.dart';
import '../widgets/accessibility_widgets.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  int _currentCarouselIndex = 0;
  List<Map<String, dynamic>> _banners = [];
  List<Map<String, dynamic>> _news = [];
  bool _isLoading = true;
  bool _isRefreshing = false;
  bool _hasCache = false;
  DateTime? _lastRefreshTime;

  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  Future<void> _initializeData() async {
    debugPrint('=== 首页初始化开始 ===');

    // 使用AppStateManager检查是否需要强制刷新
    final appStateManager = AppStateManager();
    final shouldForceRefresh = await appStateManager.shouldForceRefresh();
    debugPrint('首页检查强制刷新标记结果: $shouldForceRefresh');

    if (shouldForceRefresh) {
      // 需要强制刷新，显示骨架屏并强制加载数据
      debugPrint('首页检测到强制刷新标记，强制加载数据');
      setState(() {
        _hasCache = false;
        _isLoading = true;
      });
      _loadHomeData();
      return;
    }

    // 正常流程：首先检查是否有缓存数据
    final cachedData = await CacheService.getHomeData();
    if (cachedData != null) {
      // 有缓存数据，直接显示，不显示骨架屏
      setState(() {
        _hasCache = true;
        _isLoading = false;
        _banners = List<Map<String, dynamic>>.from(cachedData['banners'] ?? []);
        _news = List<Map<String, dynamic>>.from(cachedData['news'] ?? []);
      });
    } else {
      // 没有缓存数据，显示骨架屏并加载数据
      _loadHomeData();
    }
  }

  Future<void> _loadHomeData() async {
    try {
      final result = await ApiService.getHomeInfo(context: context);
      if (result['success'] && mounted) {
        final banners = List<Map<String, dynamic>>.from(result['data']['banners']);
        final news = List<Map<String, dynamic>>.from(result['data']['news']);

        setState(() {
          _banners = banners;
          _news = news;
          _isLoading = false;
          _hasCache = true;
        });
      } else if (mounted) {
        // 检查是否为token过期错误
        if (result['isTokenExpired'] == true) {
          // Token过期时，TokenExpiryHandler已经处理了对话框和跳转
          // 这里只需要更新UI状态，不显示额外的错误信息
          setState(() {
            _isLoading = false;
          });
          return;
        }

        // API调用失败，显示错误信息
        setState(() {
          _isLoading = false;
        });

        // 如果有错误信息，显示给用户
        if (result['message'] != null) {
          Color backgroundColor;
          if (result['fromCache'] == true) {
            // 从缓存获取数据
            backgroundColor = result['isOffline'] == true
                ? AppTheme.warningColor  // 离线缓存
                : AppTheme.infoColor;    // 在线缓存
          } else {
            backgroundColor = AppTheme.errorColor; // 网络错误
          }

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(result['message']),
              backgroundColor: backgroundColor,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${LocalizationService.t('load_failed')}: ${e.toString()}'),
            backgroundColor: AppTheme.errorColor,
          ),
        );
      }
    }
  }

  Future<void> _refreshHomeData() async {
    // 检查是否在刷新中或距离上次刷新不足3秒
    final now = DateTime.now();
    if (_isRefreshing ||
        (_lastRefreshTime != null &&
            now.difference(_lastRefreshTime!).inSeconds < 3)) {
      return;
    }

    setState(() {
      _isRefreshing = true;
    });

    _lastRefreshTime = now;

    try {
      // 强制刷新，不使用缓存
      final result = await ApiService.getHomeInfo(forceRefresh: true, context: context);
      if (result['success'] && mounted) {
        final banners = List<Map<String, dynamic>>.from(result['data']['banners']);
        final news = List<Map<String, dynamic>>.from(result['data']['news']);

        setState(() {
          _banners = banners;
          _news = news;
        });

        // 显示刷新成功提示
        if (mounted) {
          String message;
          Color backgroundColor;

          if (result['fromCache'] == true) {
            // 从缓存获取数据
            if (result['isOffline'] == true) {
              message = LocalizationService.t('fetch_success_offline_cache');
              backgroundColor = AppTheme.warningColor;
            } else {
              message = LocalizationService.t('fetch_success_cache');
              backgroundColor = AppTheme.infoColor;
            }
          } else {
            // 从网络获取新数据
            message = LocalizationService.t('refresh_success');
            backgroundColor = AppTheme.successColor;
          }

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(message),
              duration: const Duration(seconds: 1),
              backgroundColor: backgroundColor,
            ),
          );
        }
      } else if (mounted) {
        // 检查是否为token过期错误
        if (result['isTokenExpired'] == true) {
          // Token过期时，TokenExpiryHandler已经处理了对话框和跳转
          // 这里不显示额外的错误信息
          return;
        }

        // 刷新失败，显示错误信息
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(result['message'] ?? LocalizationService.t('refresh_failed')),
            backgroundColor: AppTheme.errorColor,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        // 显示刷新失败提示
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(LocalizationService.t('refresh_failed')),
            duration: const Duration(seconds: 2),
            backgroundColor: AppTheme.errorColor,
          ),
        );
      }
    } finally {
      // 3秒后重新启用刷新按钮
      Future.delayed(const Duration(seconds: 3), () {
        if (mounted) {
          setState(() {
            _isRefreshing = false;
          });
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.getBackgroundColor(context),
      appBar: AppBar(
        title: AccessibleText(
          LocalizationService.t('home'),
          isHeading: true,
          headingLevel: 1,
        ),
        automaticallyImplyLeading: false,
        actions: [
          Semantics(
            label: _isRefreshing ? LocalizationService.t('refreshing') : LocalizationService.t('refresh_page_content'),
            button: true,
            enabled: !_isRefreshing,
            child: IconButton(
              onPressed: _isRefreshing ? null : _refreshHomeData,
              icon: _isRefreshing
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
                      ),
                    )
                  : const Icon(Icons.refresh),
              style: IconButton.styleFrom(
                disabledForegroundColor: Theme.of(context).brightness == Brightness.dark
                    ? AppTheme.darkTextSecondary
                    : AppTheme.textSecondary,
              ),
            ),
          ),
        ],
      ),
      body: SafeArea(
        child: _isLoading && !_hasCache
            ? const Column(
                children: [
                  // 轮播图骨架屏
                  CarouselSkeleton(),
                  SizedBox(height: AppTheme.paddingMedium),
                  // 资讯列表骨架屏
                  Expanded(child: NewsListSkeleton()),
                ],
              )
            : Column(
                children: [
                  // 固定的轮播图
                  _buildCarousel(),
                  const SizedBox(height: AppTheme.paddingMedium),
                  // 可滚动的企业资讯
                  Expanded(child: _buildNewsSection()),
                ],
              ),
      ),
    );
  }

  Widget _buildCarousel() {
    return Container(
      margin: const EdgeInsets.all(AppTheme.paddingMedium),
      child: Column(
        children: [
          CarouselSlider(
            options: CarouselOptions(
              height: 180,
              autoPlay: true,
              autoPlayInterval: const Duration(seconds: 3),
              enlargeCenterPage: true,
              viewportFraction: 1.0,
              onPageChanged: (index, reason) {
                setState(() {
                  _currentCarouselIndex = index;
                });
              },
            ),
            items:
                _banners.map((item) {
                  return Builder(
                    builder: (BuildContext context) {
                      return GestureDetector(
                        onTap: () => _openBannerDetail(item),
                        child: Container(
                          width: MediaQuery.of(context).size.width,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(
                              AppTheme.radiusMedium,
                            ),
                          ),
                          child: Stack(
                            children: [
                              // 背景图片
                              ClipRRect(
                                borderRadius: BorderRadius.circular(
                                  AppTheme.radiusMedium,
                                ),
                                child: Image.network(
                                  item['image'],
                                  fit: BoxFit.cover,
                                  width: MediaQuery.of(context).size.width,
                                  height: 200,
                                  errorBuilder: (context, error, stackTrace) {
                                    // 图片加载失败时显示默认占位符
                                    return Container(
                                      width: MediaQuery.of(context).size.width,
                                      height: 200,
                                      decoration: BoxDecoration(
                                        color: AppTheme.getCardColor(context),
                                        borderRadius: BorderRadius.circular(
                                          AppTheme.radiusMedium,
                                        ),
                                      ),
                                      child: Column(
                                        mainAxisAlignment: MainAxisAlignment.center,
                                        children: [
                                          Icon(
                                            Icons.image_not_supported_outlined,
                                            size: 48,
                                            color: AppTheme.getTextSecondaryColor(context),
                                          ),
                                          const SizedBox(height: 8),
                                          Text(
                                            LocalizationService.t('image_load_failed'),
                                            style: TextStyle(
                                              color: AppTheme.getTextSecondaryColor(context),
                                              fontSize: 14,
                                            ),
                                          ),
                                        ],
                                      ),
                                    );
                                  },
                                  loadingBuilder: (context, child, loadingProgress) {
                                    if (loadingProgress == null) return child;
                                    return Container(
                                      width: MediaQuery.of(context).size.width,
                                      height: 200,
                                      decoration: BoxDecoration(
                                        color: AppTheme.getCardColor(context),
                                        borderRadius: BorderRadius.circular(
                                          AppTheme.radiusMedium,
                                        ),
                                      ),
                                      child: Center(
                                        child: CircularProgressIndicator(
                                          value: loadingProgress.expectedTotalBytes != null
                                              ? loadingProgress.cumulativeBytesLoaded /
                                                  loadingProgress.expectedTotalBytes!
                                              : null,
                                          color: AppTheme.primaryColor,
                                        ),
                                      ),
                                    );
                                  },
                                ),
                              ),
                              // 渐变遮罩和文字
                              Container(
                                width: MediaQuery.of(context).size.width,
                                height: 200,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(
                                    AppTheme.radiusMedium,
                                  ),
                                  gradient: LinearGradient(
                                    begin: Alignment.topCenter,
                                    end: Alignment.bottomCenter,
                                    colors: [
                                      Colors.transparent,
                                      Colors.black.withValues(alpha: 0.7),
                                    ],
                                  ),
                                ),
                                child: Padding(
                                  padding: const EdgeInsets.all(
                                    AppTheme.paddingLarge,
                                  ),
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.end,
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        item['title'],
                                        style: const TextStyle(
                                          fontSize: AppTheme.fontSizeTitle,
                                          fontWeight: FontWeight.bold,
                                          color: AppTheme.white,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      );
                    },
                  );
                }).toList(),
          ),
          const SizedBox(height: AppTheme.paddingSmall),
          // 轮播指示器
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children:
                _banners.asMap().entries.map((entry) {
                  return Container(
                    width: 8,
                    height: 8,
                    margin: const EdgeInsets.symmetric(horizontal: 2),
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color:
                          _currentCarouselIndex == entry.key
                              ? AppTheme.primaryColor
                              : AppTheme.getBorderColor(context),
                    ),
                  );
                }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildNewsSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: AppTheme.paddingMedium),
      decoration: BoxDecoration(
        color: AppTheme.getCardColor(context),
        borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
        boxShadow: [AppTheme.getCardShadow(context)],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 固定的标题
          Padding(
            padding: const EdgeInsets.all(AppTheme.paddingMedium),
            child: Text(
              LocalizationService.t('company_news'),
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
            ),
          ),
          // 可滚动的新闻列表
          Expanded(
            child: ListView.separated(
              padding: EdgeInsets.zero,
              itemCount: _news.length,
              separatorBuilder: (context, index) => const Divider(height: 1),
              itemBuilder: (context, index) {
                final newsItem = _news[index];
                return AnimatedListItem(
                  index: index,
                  child: Semantics(
                    label: '${LocalizationService.t('news_item_label')}：${newsItem['title']}，${newsItem['category']}，${newsItem['publishTime']}',
                    button: true,
                    child: AnimatedButton(
                      onPressed: () => _openNewsDetail(newsItem),
                      child: ListTile(
                  contentPadding: const EdgeInsets.all(AppTheme.paddingMedium),
                  title: Text(
                    newsItem['title'],
                    style: const TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: AppTheme.fontSizeMedium,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  subtitle: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SizedBox(height: 4),
                      Text(
                        newsItem['summary'],
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: AppTheme.fontSizeSmall,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 2,
                            ),
                            decoration: BoxDecoration(
                              color: AppTheme.primaryColor.withValues(
                                alpha: 0.1,
                              ),
                              borderRadius: BorderRadius.circular(10),
                            ),
                            child: Text(
                              newsItem['category'],
                              style: const TextStyle(
                                color: AppTheme.primaryColor,
                                fontSize: 10,
                              ),
                            ),
                          ),
                          const SizedBox(width: 8),
                          Text(
                            newsItem['publishTime'],
                            style: TextStyle(
                              color: Colors.grey[500],
                              fontSize: 10,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                        trailing: const Icon(Icons.chevron_right, color: Colors.grey),
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  void _openBannerDetail(Map<String, dynamic> banner) {
    // 检查轮播图数据是否有效
    final bannerTitle = banner['title'] as String? ?? '';
    final bannerUrl = banner['url'] as String? ?? '';
    final bannerId = banner['id'] ?? 0;

    if (bannerTitle.isEmpty) {
      // 轮播图标题为空，显示提示信息
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(LocalizationService.t('banner_data_invalid')),
          backgroundColor: AppTheme.warningColor,
        ),
      );
      return;
    }

    // 如果轮播图URL为空，使用默认的详情页面
    final targetUrl = bannerUrl.isNotEmpty
        ? bannerUrl
        : 'assets/html/news_detail.html?id=$bannerId&type=banner';

    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => WebViewScreen(
          title: bannerTitle,
          url: targetUrl,
        ),
      ),
    );
  }

  void _openNewsDetail(Map<String, dynamic> news) {
    // 检查新闻数据是否有效
    final newsTitle = news['title'] as String? ?? '';
    final newsUrl = news['url'] as String? ?? '';
    final newsId = news['id'] ?? 0;

    if (newsTitle.isEmpty) {
      // 新闻标题为空，显示提示信息
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(LocalizationService.t('news_data_invalid')),
          backgroundColor: AppTheme.warningColor,
        ),
      );
      return;
    }

    // 如果新闻URL为空，使用默认的详情页面
    final targetUrl = newsUrl.isNotEmpty
        ? newsUrl
        : 'assets/html/news_detail.html?id=$newsId&type=news';

    Navigator.of(context).push(
      PageTransitions.slideRoute(
        page: WebViewScreen(
          title: newsTitle,
          url: targetUrl,
        ),
        type: PageTransitionType.slideFromRight,
        duration: const Duration(milliseconds: 300),
      ),
    );
  }
}
