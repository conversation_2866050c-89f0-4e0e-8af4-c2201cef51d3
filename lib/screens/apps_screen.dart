import 'package:flutter/material.dart';
import '../constants/app_theme.dart';
import '../constants/app_constants.dart';
import '../services/app_state_manager.dart';
import '../services/cache_service.dart';
import '../services/localization_service.dart';
import '../services/api_service.dart';
import '../screens/webview_screen.dart';
import '../widgets/skeleton_widgets.dart';
import '../widgets/animated_widgets.dart';
import '../widgets/page_transitions.dart';

class AppsScreen extends StatefulWidget {
  const AppsScreen({super.key});

  @override
  State<AppsScreen> createState() => _AppsScreenState();
}

class _AppsScreenState extends State<AppsScreen> {
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _searchFocusNode = FocusNode();
  List<Map<String, dynamic>> _expandedCategories = [];
  List<Map<String, dynamic>> _filteredCategories = [];
  bool _isEditMode = false;
  bool _isEditButtonDisabled = false;
  bool _isRefreshing = false;
  bool _isLoading = true;
  bool _hasCache = false;
  DateTime? _lastRefreshTime;
  Set<String> _favoriteApps = {};
  bool _isAllExpanded = true; // 跟踪是否全部展开

  @override
  void initState() {
    super.initState();
    // 默认展开所有分类
    _expandedCategories = List.from(AppConstants.appCategories);
    _filteredCategories = List.from(AppConstants.appCategories);
    _loadFavoriteApps();
    _initializeData();
  }

  Future<void> _initializeData() async {
    debugPrint('=== 应用页面初始化开始 ===');

    // 使用AppStateManager检查是否需要强制刷新
    final appStateManager = AppStateManager();
    final shouldForceRefresh = await appStateManager.shouldForceRefresh();
    debugPrint('应用页面检查强制刷新标记结果: $shouldForceRefresh');

    if (shouldForceRefresh) {
      // 需要强制刷新，显示骨架屏并强制加载数据
      debugPrint('应用页面检测到强制刷新标记，强制加载数据');
      setState(() {
        _hasCache = false;
        _isLoading = true;
      });
      _loadAppsDataFromAPI();
      return;
    }

    // 正常流程：检查是否有缓存数据
    final cachedData = await CacheService.getAppsData();
    if (cachedData != null) {
      // 有缓存数据，先显示缓存数据
      final categories = cachedData['categories'] as List<dynamic>?;
      if (categories != null) {
        setState(() {
          _expandedCategories = List<Map<String, dynamic>>.from(categories);
          _filteredCategories = List<Map<String, dynamic>>.from(categories);
          _hasCache = true;
          _isLoading = false;
          _updateAllExpandedState();
        });
      } else {
        // 缓存数据格式不正确，使用默认数据
        setState(() {
          _expandedCategories = List.from(AppConstants.appCategories);
          _filteredCategories = List.from(AppConstants.appCategories);
          _hasCache = true;
          _isLoading = false;
        });
      }
    } else {
      // 没有缓存数据，调用API获取
      _loadAppsDataFromAPI();
    }
  }

  /// 从API加载应用数据
  Future<void> _loadAppsDataFromAPI() async {
    try {
      final result = await ApiService.getAppList(context: context);
      if (result['success'] == true) {
        final data = result['data'] as Map<String, dynamic>;
        final categories = data['categories'] as List<dynamic>? ?? [];

        // 处理API返回的数据，确保重点字段不为空
        final processedCategories = _processApiCategories(categories);

        setState(() {
          _expandedCategories = processedCategories;
          _filteredCategories = List<Map<String, dynamic>>.from(processedCategories);
          _isLoading = false;
          _hasCache = true;
          _updateAllExpandedState();
        });
      } else {
        // 检查是否为token过期错误
        if (result['isTokenExpired'] == true) {
          // Token过期时，TokenExpiryHandler已经处理了对话框和跳转
          // 这里只需要更新UI状态，不显示额外的错误信息
          setState(() {
            _expandedCategories = [];
            _filteredCategories = [];
            _isLoading = false;
            _hasCache = true;
          });
          return;
        }

        // API调用失败，保持空数据，不使用默认数据
        setState(() {
          _expandedCategories = [];
          _filteredCategories = [];
          _isLoading = false;
          _hasCache = true;
        });

        // 显示错误信息（如果有的话）
        if (result['message'] != null && mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(result['message']),
              backgroundColor: AppTheme.errorColor,
              duration: const Duration(seconds: 2),
            ),
          );
        }
      }
    } catch (e) {
      // 异常情况，保持空数据，不使用默认数据
      setState(() {
        _expandedCategories = [];
        _filteredCategories = [];
        _isLoading = false;
        _hasCache = true;
      });

      // 显示异常信息
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${LocalizationService.t('fetch_failed')}: ${e.toString()}'),
            backgroundColor: AppTheme.errorColor,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    }
  }

  Future<void> _loadFavoriteApps() async {
    // 从本地存储加载常用应用
    // TODO: 实现从SharedPreferences加载
    setState(() {
      _favoriteApps = {
        LocalizationService.t('schedule_management'),
        LocalizationService.t('task_collaboration'),
      };
    });
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        // 点击其他区域时失去搜索栏焦点
        _searchFocusNode.unfocus();
      },
      child: Scaffold(
        backgroundColor: AppTheme.getBackgroundColor(context),
        appBar: AppBar(
          title: Text(LocalizationService.t('apps')),
          actions: [
            // 全局折叠/展开按钮
            IconButton(
              onPressed: _toggleAllCategories,
              icon: Icon(_isAllExpanded ? Icons.unfold_less : Icons.unfold_more),
              tooltip: _isAllExpanded
                  ? LocalizationService.t('collapse_all')
                  : LocalizationService.t('expand_all'),
            ),
            IconButton(
              onPressed: _isEditButtonDisabled ? null : _toggleEditMode,
              icon: _isEditButtonDisabled
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
                      ),
                    )
                  : Icon(_isEditMode ? Icons.save : Icons.edit),
              tooltip: _isEditMode
                  ? LocalizationService.t('save_tooltip')
                  : LocalizationService.t('edit_tooltip'),
              style: IconButton.styleFrom(
                disabledForegroundColor: Theme.of(context).brightness == Brightness.dark
                    ? AppTheme.darkTextSecondary
                    : AppTheme.textSecondary,
              ),
            ),
          ],
        ),
        body: _isLoading && !_hasCache
            ? const Column(
                children: [
                  // 搜索栏骨架
                  SizedBox(height: 80),
                  // 应用网格骨架屏
                  Expanded(child: AppGridSkeleton()),
                ],
              )
            : Column(
                children: [
                  // 搜索栏
                  _buildSearchBar(),
                  // 编辑模式提示
                  if (_isEditMode) _buildEditModeHint(),
                  // 应用列表
                  Expanded(
                    child: RefreshIndicator(
                      onRefresh: _refreshApps,
                      child: _filteredCategories.isEmpty
                          ? _buildEmptyStateWithRefresh()
                          : ListView.builder(
                              padding: const EdgeInsets.all(AppTheme.paddingMedium),
                              itemCount: _filteredCategories.length,
                              itemBuilder: (context, index) {
                                final category = _filteredCategories[index];
                                final isExpanded = _expandedCategories.any(
                                  (expandedCategory) =>
                                      expandedCategory['title'] == category['title'],
                                );
                                return _buildAppCategory(category, isExpanded);
                              },
                            ),
                    ),
                  ),
                ],
              ),
      ),
    );
  }

  Widget _buildSearchBar() {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Container(
      padding: const EdgeInsets.all(AppTheme.paddingMedium),
      color: AppTheme.getSurfaceColor(context),
      child: TextField(
        controller: _searchController,
        focusNode: _searchFocusNode,
        onChanged: _filterApps,
        style: TextStyle(
          color: isDark ? AppTheme.darkTextPrimary : AppTheme.textPrimary,
          fontSize: AppTheme.fontSizeMedium,
        ),
        decoration: InputDecoration(
          hintText: LocalizationService.t('search_apps'),
          hintStyle: TextStyle(
            color: isDark ? AppTheme.darkTextTertiary : AppTheme.textTertiary,
            fontSize: AppTheme.fontSizeMedium,
          ),
          prefixIcon: Icon(
            Icons.search,
            color: isDark ? AppTheme.darkTextTertiary : AppTheme.textTertiary,
          ),
          suffixIcon:
              _searchController.text.isNotEmpty
                  ? IconButton(
                    onPressed: _clearSearch,
                    icon: Icon(
                      Icons.clear,
                      color:
                          isDark
                              ? AppTheme.darkTextTertiary
                              : AppTheme.textTertiary,
                    ),
                  )
                  : null,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
            borderSide: BorderSide(
              color: isDark ? AppTheme.darkBorder : AppTheme.borderColor,
              width: 1,
            ),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
            borderSide: BorderSide(
              color: isDark ? AppTheme.darkBorder : AppTheme.borderColor,
              width: 1,
            ),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
            borderSide: BorderSide(color: AppTheme.primaryColor, width: 2),
          ),
          filled: true,
          fillColor: isDark ? AppTheme.darkCard : AppTheme.white,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: AppTheme.paddingMedium,
            vertical: AppTheme.paddingSmall,
          ),
        ),
      ),
    );
  }

  void _filterApps(String query) {
    setState(() {
      if (query.isEmpty) {
        _filteredCategories = List.from(AppConstants.appCategories);
      } else {
        _filteredCategories =
            AppConstants.appCategories
                .map((category) {
                  final filteredApps =
                      (category['apps'] as List).where((app) {
                        return app['name'].toLowerCase().contains(
                          query.toLowerCase(),
                        );
                      }).toList();

                  return {...category, 'apps': filteredApps};
                })
                .where((category) => (category['apps'] as List).isNotEmpty)
                .toList();
      }
    });
  }

  void _clearSearch() {
    setState(() {
      _searchController.clear();
      _filteredCategories = List.from(AppConstants.appCategories);
    });
  }

  void _toggleEditMode() {
    if (_isEditMode) {
      // 退出编辑模式时禁用按钮3秒
      setState(() {
        _isEditMode = false;
        _isEditButtonDisabled = true;
      });

      // 保存常用应用设置
      _saveFavoriteApps();

      // 3秒后重新启用编辑按钮
      Future.delayed(const Duration(seconds: 3), () {
        if (mounted) {
          setState(() {
            _isEditButtonDisabled = false;
          });
        }
      });
    } else {
      // 进入编辑模式时显示提示
      setState(() {
        _isEditMode = true;
      });

      // 显示编辑提示
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(LocalizationService.t('edit_mode_hint')),
          duration: const Duration(seconds: 3),
          backgroundColor: AppTheme.primaryColor,
        ),
      );
    }
  }

  Widget _buildEditModeHint() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(
        horizontal: AppTheme.paddingMedium,
        vertical: AppTheme.paddingSmall,
      ),
      color: AppTheme.primaryColor.withValues(alpha: 0.1),
      child: Row(
        children: [
          Icon(Icons.info_outline, size: 16, color: AppTheme.primaryColor),
          const SizedBox(width: AppTheme.paddingSmall),
          Expanded(
            child: Text(
              LocalizationService.t('edit_mode_hint'),
              style: TextStyle(
                color: AppTheme.primaryColor,
                fontSize: AppTheme.fontSizeSmall,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _refreshApps() async {
    // 检查是否在刷新中或距离上次刷新不足3秒
    final now = DateTime.now();
    if (_isRefreshing ||
        (_lastRefreshTime != null &&
            now.difference(_lastRefreshTime!).inSeconds < 3)) {
      return;
    }

    setState(() {
      _isRefreshing = true;
    });

    _lastRefreshTime = now;

    try {
      // 调用API获取应用列表数据
      final result = await ApiService.getAppList(forceRefresh: true, context: context);

      if (result['success'] == true) {
        final data = result['data'] as Map<String, dynamic>;
        final categories = data['categories'] as List<dynamic>? ?? [];

        // 处理API返回的数据，确保重点字段不为空
        final processedCategories = _processApiCategories(categories);

        // 更新应用分类数据
        setState(() {
          _expandedCategories = processedCategories;
          _filteredCategories = List<Map<String, dynamic>>.from(processedCategories);
          _updateAllExpandedState();
        });

        // 重新加载常用应用
        await _loadFavoriteApps();

        // 显示刷新成功提示
        if (mounted) {
          String message;
          Color backgroundColor;

          if (result['fromCache'] == true) {
            // 从缓存获取数据
            if (result['isOffline'] == true) {
              message = LocalizationService.t('fetch_success_offline_cache');
              backgroundColor = AppTheme.warningColor;
            } else {
              message = LocalizationService.t('fetch_success_cache');
              backgroundColor = AppTheme.infoColor;
            }
          } else {
            // 从网络获取新数据
            message = LocalizationService.t('refresh_success');
            backgroundColor = AppTheme.successColor;
          }

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(message),
              duration: const Duration(seconds: 1),
              backgroundColor: backgroundColor,
            ),
          );
        }
      } else {
        // 检查是否为token过期错误
        if (result['isTokenExpired'] == true) {
          // Token过期时，TokenExpiryHandler已经处理了对话框和跳转
          // 这里不显示额外的错误信息
          return;
        }

        // API调用失败，保持空数据，不使用本地数据作为备用
        setState(() {
          _expandedCategories = [];
          _filteredCategories = [];
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(LocalizationService.t('refresh_failed')),
              duration: const Duration(seconds: 2),
              backgroundColor: AppTheme.errorColor,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(LocalizationService.t('refresh_failed')),
            duration: const Duration(seconds: 2),
            backgroundColor: AppTheme.errorColor,
          ),
        );
      }
    } finally {
      // 3秒后重新启用刷新功能
      Future.delayed(const Duration(seconds: 3), () {
        if (mounted) {
          setState(() {
            _isRefreshing = false;
          });
        }
      });
    }
  }

  Future<void> _saveFavoriteApps() async {
    // TODO: 保存到SharedPreferences和后端API
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(LocalizationService.t('favorite_apps_saved')),
        backgroundColor: AppTheme.successColor,
      ),
    );
  }

  Widget _buildAppCategory(Map<String, dynamic> category, bool isExpanded) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppTheme.paddingMedium),
      decoration: BoxDecoration(
        color: AppTheme.getCardColor(context),
        borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
        boxShadow: [AppTheme.getCardShadow(context)],
      ),
      child: Column(
        children: [
          // 分类标题
          InkWell(
            onTap: () => _toggleCategory(category),
            child: Padding(
              padding: const EdgeInsets.all(AppTheme.paddingMedium),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    category['title'],
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Icon(
                    isExpanded
                        ? Icons.keyboard_arrow_down
                        : Icons.keyboard_arrow_right,
                    color: AppTheme.textSecondary,
                  ),
                ],
              ),
            ),
          ),
          // 应用网格
          if (isExpanded) _buildAppGrid(category['apps']),
        ],
      ),
    );
  }

  Widget _buildAppGrid(List<dynamic> apps) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(
        AppTheme.paddingMedium,
        0,
        AppTheme.paddingMedium,
        AppTheme.paddingMedium,
      ),
      child: GridView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 3,
          crossAxisSpacing: AppTheme.paddingMedium,
          mainAxisSpacing: AppTheme.paddingMedium,
          childAspectRatio: 1,
        ),
        itemCount: apps.length,
        itemBuilder: (context, index) {
          final app = apps[index];
          return AnimatedListItem(
            index: index,
            child: _buildAppItem(app),
          );
        },
      ),
    );
  }

  Widget _buildAppItem(Map<String, dynamic> app) {
    final isFavorite = _favoriteApps.contains(app['name']);

    return AnimatedButton(
      onPressed: () => _isEditMode ? _toggleFavorite(app['name']) : _openApp(app),
      child: Container(
        // 为编辑模式的checkbox预留空间
        padding:
            _isEditMode
                ? const EdgeInsets.only(top: 6, right: 6)
                : EdgeInsets.zero,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Stack(
              clipBehavior: Clip.none, // 允许子元素超出边界
              children: [
                Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    color: Color(int.parse(app['color'])),
                    borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
                  ),
                  child: Icon(
                    _getIconData(app['icon']),
                    color: AppTheme.white,
                    size: 24,
                  ),
                ),
                if (_isEditMode)
                  Positioned(
                    top: -6,
                    right: -6,
                    child: Container(
                      width: 20,
                      height: 20,
                      decoration: BoxDecoration(
                        color:
                            isFavorite ? AppTheme.primaryColor : AppTheme.white,
                        shape: BoxShape.circle,
                        border: Border.all(
                          color:
                              isFavorite
                                  ? AppTheme.white
                                  : AppTheme.getBorderColor(context),
                          width: 1.5,
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.15),
                            blurRadius: 3,
                            offset: const Offset(0, 1),
                          ),
                        ],
                      ),
                      child: Icon(
                        Icons.check,
                        size: 12,
                        color: isFavorite ? AppTheme.white : Colors.transparent,
                      ),
                    ),
                  ),
              ],
            ),
            const SizedBox(height: AppTheme.paddingSmall),
            Text(
              app['name'],
              style: Theme.of(context).textTheme.bodySmall,
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  void _toggleFavorite(String appName) {
    setState(() {
      if (_favoriteApps.contains(appName)) {
        _favoriteApps.remove(appName);
      } else {
        _favoriteApps.add(appName);
      }
    });
  }

  IconData _getIconData(String iconName) {
    switch (iconName) {
      case 'calendar_today':
        return Icons.calendar_today;
      case 'assignment':
        return Icons.assignment;
      case 'description':
        return Icons.description;
      case 'receipt':
        return Icons.receipt;
      case 'pie_chart':
        return Icons.pie_chart;
      default:
        return Icons.apps;
    }
  }

  void _toggleCategory(Map<String, dynamic> category) {
    setState(() {
      final isExpanded = _expandedCategories.any(
        (expandedCategory) => expandedCategory['title'] == category['title'],
      );

      if (isExpanded) {
        _expandedCategories.removeWhere(
          (expandedCategory) => expandedCategory['title'] == category['title'],
        );
      } else {
        _expandedCategories.add(category);
      }

      // 更新全局展开状态
      _updateAllExpandedState();
    });
  }

  void _toggleAllCategories() {
    setState(() {
      if (_isAllExpanded) {
        // 全部折叠
        _expandedCategories.clear();
        _isAllExpanded = false;
      } else {
        // 全部展开
        _expandedCategories = List<Map<String, dynamic>>.from(_filteredCategories);
        _isAllExpanded = true;
      }
    });
  }

  void _updateAllExpandedState() {
    _isAllExpanded = _expandedCategories.length == _filteredCategories.length;
  }

  void _openApp(Map<String, dynamic> app) {
    // 检查应用数据是否有效
    final appName = app['name'] as String? ?? '';
    final appUrl = app['url'] as String? ?? '';

    if (appName.isEmpty) {
      // 应用名称为空，显示提示信息
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(LocalizationService.t('app_data_invalid')),
          backgroundColor: AppTheme.warningColor,
        ),
      );
      return;
    }

    // 如果应用URL为空，使用默认的详情页面
    final targetUrl = appUrl.isNotEmpty
        ? appUrl
        : 'assets/html/app_detail.html?id=${_getAppId(appName)}';

    Navigator.of(context).push(
      PageTransitions.slideRoute(
        page: WebViewScreen(
          title: appName,
          url: targetUrl,
        ),
        type: PageTransitionType.slideFromRight,
        duration: const Duration(milliseconds: 300),
      ),
    );
  }

  String _getAppId(String appName) {
    if (appName == LocalizationService.t('schedule_management')) {
      return '1';
    } else if (appName == LocalizationService.t('task_collaboration')) {
      return '2';
    } else if (appName == LocalizationService.t('document_center')) {
      return '3';
    } else if (appName == LocalizationService.t('customer_management')) {
      return '4';
    } else {
      return '1';
    }
  }

  /// 处理API返回的分类数据，确保重点字段不为空
  List<Map<String, dynamic>> _processApiCategories(List<dynamic> categories) {
    final processedCategories = <Map<String, dynamic>>[];

    for (final category in categories) {
      final categoryData = category as Map<String, dynamic>? ?? {};

      // 确保title字段不为空
      final title = categoryData['title'] as String? ?? LocalizationService.t('unknown_category');
      final apps = categoryData['apps'] as List<dynamic>? ?? [];

      // 处理应用列表
      final processedApps = <Map<String, dynamic>>[];
      for (final app in apps) {
        final appData = app as Map<String, dynamic>? ?? {};

        // 确保重点字段不为空
        final name = appData['name'] as String? ?? LocalizationService.t('unknown_app');
        final icon = appData['icon'] as String? ?? 'apps';
        final color = appData['color'] as String? ?? _getDefaultColorForApp(name);

        processedApps.add({
          'name': name,
          'icon': icon,
          'color': color,
          'description': appData['description'] as String? ?? '',
          'url': appData['url'] as String? ?? '',
        });
      }

      processedCategories.add({
        'title': title,
        'apps': processedApps,
      });
    }

    return processedCategories;
  }

  /// 为应用获取默认颜色
  String _getDefaultColorForApp(String appName) {
    // 根据应用名称返回默认颜色
    final colors = [
      '0xFF3366CC', '0xFFFF9900', '0xFF52C41A', '0xFF1890FF', '0xFF722ED1',
      '0xFFF5222D', '0xFFFAAD14', '0xFF13C2C2', '0xFF9C27B0', '0xFF795548',
    ];
    return colors[appName.hashCode % colors.length];
  }

  Widget _buildEmptyStateWithRefresh() {
    return SingleChildScrollView(
      // 确保即使内容不足也能触发下拉刷新
      physics: const AlwaysScrollableScrollPhysics(),
      padding: const EdgeInsets.all(AppTheme.paddingMedium),
      child: ConstrainedBox(
        constraints: BoxConstraints(
          minHeight: MediaQuery.of(context).size.height - 200,
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.apps_outlined,
              size: 64,
              color: AppTheme.textTertiary,
            ),
            const SizedBox(height: AppTheme.paddingMedium),
            Text(
              LocalizationService.t('no_apps_available'),
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppTheme.textTertiary,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppTheme.paddingSmall),
            Text(
              LocalizationService.t('pull_to_refresh'),
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: AppTheme.textTertiary,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _searchController.dispose();
    _searchFocusNode.dispose();
    super.dispose();
  }
}
