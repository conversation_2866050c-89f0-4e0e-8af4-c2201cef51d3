import 'package:flutter/material.dart';
import '../constants/app_theme.dart';
import '../services/localization_service.dart';

/// 连接错误页面（404页面）
/// 当服务器连接失败时显示的友好错误页面
class ConnectionErrorScreen extends StatelessWidget {
  final String? serverAddress;
  final String? serverPort;
  final String? errorMessage;

  const ConnectionErrorScreen({
    super.key,
    this.serverAddress,
    this.serverPort,
    this.errorMessage,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.getSurfaceColor(context),
      appBar: AppBar(
        title: Text(LocalizationService.t('connection_error')),
        backgroundColor: AppTheme.getSurfaceColor(context),
        foregroundColor: AppTheme.getTextPrimaryColor(context),
        elevation: 0,
      ),
      body: GestureDetector(
        onPanEnd: (DragEndDetails details) {
          // 只有在水平滑动速度大于垂直滑动速度时才处理
          if (details.velocity.pixelsPerSecond.dx > 300 &&
              details.velocity.pixelsPerSecond.dx.abs() > details.velocity.pixelsPerSecond.dy.abs()) {
            Navigator.of(context).pop();
          }
        },
        child: Center(
          child: Padding(
            padding: const EdgeInsets.all(AppTheme.paddingLarge),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
              // 错误图标
              Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  color: AppTheme.errorColor.withOpacity(0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.cloud_off_rounded,
                  size: 60,
                  color: AppTheme.errorColor,
                ),
              ),
              
              const SizedBox(height: AppTheme.paddingLarge),
              
              // 错误标题
              Text(
                LocalizationService.t('connection_failed_title'),
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.getTextPrimaryColor(context),
                ),
                textAlign: TextAlign.center,
              ),
              
              const SizedBox(height: AppTheme.paddingMedium),
              
              // 错误描述
              Text(
                LocalizationService.t('connection_failed_description'),
                style: TextStyle(
                  fontSize: 16,
                  color: AppTheme.getTextSecondaryColor(context),
                  height: 1.5,
                ),
                textAlign: TextAlign.center,
              ),
              
              const SizedBox(height: AppTheme.paddingLarge),
              
              // 服务器信息卡片
              if (serverAddress != null && serverPort != null)
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(AppTheme.paddingMedium),
                  decoration: BoxDecoration(
                    color: AppTheme.getCardColor(context),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: AppTheme.getBorderColor(context),
                      width: 1,
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        LocalizationService.t('target_server'),
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: AppTheme.getTextPrimaryColor(context),
                        ),
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          Icon(
                            Icons.dns_rounded,
                            size: 16,
                            color: AppTheme.getTextSecondaryColor(context),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              '$serverAddress:$serverPort',
                              style: TextStyle(
                                fontSize: 14,
                                color: AppTheme.getTextSecondaryColor(context),
                                fontFamily: 'monospace',
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              
              const SizedBox(height: AppTheme.paddingLarge),
              
              // 错误详情（如果有）
              if (errorMessage != null)
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(AppTheme.paddingMedium),
                  decoration: BoxDecoration(
                    color: AppTheme.errorColor.withOpacity(0.05),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: AppTheme.errorColor.withOpacity(0.2),
                      width: 1,
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        LocalizationService.t('connection_error_details'),
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: AppTheme.errorColor,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        errorMessage!,
                        style: TextStyle(
                          fontSize: 12,
                          color: AppTheme.errorColor,
                          fontFamily: 'monospace',
                        ),
                      ),
                    ],
                  ),
                ),
              
              const SizedBox(height: AppTheme.paddingXLarge),
              
              // 建议操作
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(AppTheme.paddingMedium),
                decoration: BoxDecoration(
                  color: AppTheme.infoColor.withOpacity(0.05),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: AppTheme.infoColor.withOpacity(0.2),
                    width: 1,
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      LocalizationService.t('suggestions'),
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: AppTheme.infoColor,
                      ),
                    ),
                    const SizedBox(height: 8),
                    ...LocalizationService.t('connection_suggestions')
                        .split('\n')
                        .map((suggestion) => Padding(
                              padding: const EdgeInsets.only(bottom: 4),
                              child: Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    '• ',
                                    style: TextStyle(
                                      fontSize: 14,
                                      color: AppTheme.infoColor,
                                    ),
                                  ),
                                  Expanded(
                                    child: Text(
                                      suggestion,
                                      style: TextStyle(
                                        fontSize: 14,
                                        color: AppTheme.getTextSecondaryColor(context),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            )),
                  ],
                ),
              ),
              
              const SizedBox(height: AppTheme.paddingXLarge),
              
              // 返回按钮
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.arrow_back),
                  label: Text(LocalizationService.t('back_to_config')),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.primaryColor,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
