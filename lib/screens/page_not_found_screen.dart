import 'package:flutter/material.dart';
import '../constants/app_theme.dart';
import '../services/localization_service.dart';

/// 通用404页面
/// 当页面无法加载或不存在时显示的友好错误页面
class PageNotFoundScreen extends StatefulWidget {
  final String? title;
  final String? description;
  final String? errorMessage;
  final VoidCallback? onRetry;
  final VoidCallback? onGoBack;

  const PageNotFoundScreen({
    super.key,
    this.title,
    this.description,
    this.errorMessage,
    this.onRetry,
    this.onGoBack,
  });

  @override
  State<PageNotFoundScreen> createState() => _PageNotFoundScreenState();
}

class _PageNotFoundScreenState extends State<PageNotFoundScreen> {

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.getSurfaceColor(context),
      appBar: AppBar(
        title: Text(
          widget.title ?? LocalizationService.t('page_not_found'),
          style: TextStyle(
            color: AppTheme.getTextPrimaryColor(context),
          ),
        ),
        backgroundColor: AppTheme.getSurfaceColor(context),
        foregroundColor: AppTheme.getTextPrimaryColor(context),
        elevation: 0,
        leading: IconButton(
          onPressed: widget.onGoBack ?? () => Navigator.of(context).pop(),
          icon: Icon(
            Icons.arrow_back,
            color: AppTheme.getTextPrimaryColor(context),
          ),
        ),
      ),
      body: GestureDetector(
        onHorizontalDragEnd: (DragEndDetails details) {
          // 检测右滑手势（从左向右滑动）
          if (details.primaryVelocity != null && details.primaryVelocity! > 300) {
            Navigator.of(context).pop();
          }
        },
        child: Center(
          child: Padding(
            padding: const EdgeInsets.all(AppTheme.paddingLarge),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
              // 404图标
              Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  color: AppTheme.warningColor.withOpacity(0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.error_outline_rounded,
                  size: 60,
                  color: AppTheme.warningColor,
                ),
              ),
              
              const SizedBox(height: AppTheme.paddingLarge),
              
              // 错误标题
              Text(
                LocalizationService.t('page_load_failed'),
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.getTextPrimaryColor(context),
                ),
                textAlign: TextAlign.center,
              ),
              
              const SizedBox(height: AppTheme.paddingMedium),
              
              // 错误描述
              Text(
                widget.description ?? LocalizationService.t('page_load_failed_description'),
                style: TextStyle(
                  fontSize: 16,
                  color: AppTheme.getTextSecondaryColor(context),
                  height: 1.5,
                ),
                textAlign: TextAlign.center,
              ),
              
              const SizedBox(height: AppTheme.paddingLarge),
              
              // 错误详情（如果有）
              if (widget.errorMessage != null)
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(AppTheme.paddingMedium),
                  decoration: BoxDecoration(
                    color: AppTheme.warningColor.withOpacity(0.05),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: AppTheme.warningColor.withOpacity(0.2),
                      width: 1,
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        LocalizationService.t('error_info'),
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: AppTheme.warningColor,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        widget.errorMessage!,
                        style: TextStyle(
                          fontSize: 12,
                          color: AppTheme.warningColor,
                          fontFamily: 'monospace',
                        ),
                      ),
                    ],
                  ),
                ),
              
              const SizedBox(height: AppTheme.paddingXLarge),
              
              // 操作按钮
              Row(
                children: [
                  // 返回按钮
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: widget.onGoBack ?? () => Navigator.of(context).pop(),
                      icon: const Icon(Icons.arrow_back),
                      label: Text(LocalizationService.t('go_back')),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: AppTheme.getTextPrimaryColor(context),
                        side: BorderSide(
                          color: AppTheme.getBorderColor(context),
                        ),
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                    ),
                  ),
                  
                  // 重试按钮（如果有重试回调）
                  if (widget.onRetry != null) ...[
                    const SizedBox(width: AppTheme.paddingMedium),
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: widget.onRetry,
                        icon: const Icon(Icons.refresh),
                        label: Text(LocalizationService.t('retry')),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppTheme.primaryColor,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                      ),
                    ),
                  ],
                ],
              ),
              
              const SizedBox(height: AppTheme.paddingLarge),
              
              // 帮助提示
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(AppTheme.paddingMedium),
                decoration: BoxDecoration(
                  color: AppTheme.infoColor.withOpacity(0.05),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: AppTheme.infoColor.withOpacity(0.2),
                    width: 1,
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      LocalizationService.t('help_tips'),
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: AppTheme.infoColor,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      LocalizationService.t('page_load_help_tips'),
                      style: TextStyle(
                        fontSize: 14,
                        color: AppTheme.getTextSecondaryColor(context),
                      ),
                    ),
                  ],
                ),
              ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
