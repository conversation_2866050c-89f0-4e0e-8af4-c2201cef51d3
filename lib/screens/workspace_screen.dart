import 'package:flutter/material.dart';
import '../constants/app_theme.dart';
import '../services/app_state_manager.dart';
import '../services/localization_service.dart';
import '../services/api_service.dart';
import '../services/cache_service.dart';
import 'webview_screen.dart';
import '../widgets/skeleton_widgets.dart';
import '../widgets/animated_widgets.dart';
import '../widgets/page_transitions.dart';

class WorkspaceScreen extends StatefulWidget {
  const WorkspaceScreen({super.key});

  @override
  State<WorkspaceScreen> createState() => _WorkspaceScreenState();
}

class _WorkspaceScreenState extends State<WorkspaceScreen> {
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _searchFocusNode = FocusNode();
  bool _isEditMode = false;
  bool _isEditButtonDisabled = false;
  bool _isRefreshing = false;
  bool _isLoading = true;
  bool _hasCache = false;
  DateTime? _lastRefreshTime;
  bool _isFrequentAppsExpanded = true;

  // 常用应用数据 - 改为实际的列表而不是getter
  List<Map<String, dynamic>> _frequentApps = [];

  List<Map<String, dynamic>> _filteredApps = [];

  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  Future<void> _initializeData() async {
    // 使用AppStateManager检查是否需要强制刷新
    final appStateManager = AppStateManager();
    final shouldForceRefresh = await appStateManager.shouldForceRefresh();

    if (shouldForceRefresh) {
      // 需要强制刷新，显示骨架屏并强制加载数据
      debugPrint('工作台页面检测到强制刷新标记，强制加载数据');
      setState(() {
        _hasCache = false;
        _isLoading = true;
      });
      _loadWorkspaceData();
      return;
    }

    // 正常流程：首先检查是否有缓存数据（与应用页面保持一致）
    final cachedData = await CacheService.getWorkspaceData();
    if (cachedData != null) {
      // 有缓存数据，先显示缓存数据，不显示骨架屏
      final favoriteApps = cachedData['favoriteApps'] as List<dynamic>? ?? [];
      final processedApps = _processWorkspaceApps(favoriteApps);

      setState(() {
        _frequentApps = processedApps;
        _filteredApps = List.from(_frequentApps);
        _hasCache = true;
        _isLoading = false;
      });
    } else {
      // 没有缓存数据，调用API加载数据，显示骨架屏
      _loadWorkspaceData();
    }
  }

  Future<void> _loadWorkspaceData() async {
    try {
      final result = await ApiService.getWorkspaceApps(context: context);
      if (result['success'] == true) {
        final data = result['data'] as Map<String, dynamic>;
        final favoriteApps = data['favoriteApps'] as List<dynamic>? ?? [];

        // 处理API返回的数据，确保重点字段不为空
        final processedApps = _processWorkspaceApps(favoriteApps);

        setState(() {
          _frequentApps = processedApps;
          _filteredApps = List.from(_frequentApps);
          _isLoading = false;
          _hasCache = true;
        });
      } else {
        // 检查是否为token过期错误
        if (result['isTokenExpired'] == true) {
          // Token过期时，TokenExpiryHandler已经处理了对话框和跳转
          // 这里只需要更新UI状态，不显示额外的错误信息
          setState(() {
            _frequentApps = [];
            _filteredApps = [];
            _isLoading = false;
            _hasCache = true;
          });
          return;
        }

        // API调用失败，保持空数据
        setState(() {
          _frequentApps = [];
          _filteredApps = [];
          _isLoading = false;
          _hasCache = true;
        });

        // 显示提示信息
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('${result['message'] ?? LocalizationService.t('api_call_failed')}'),
              duration: const Duration(seconds: 2),
              backgroundColor: Colors.orange,
            ),
          );
        }
      }
    } catch (e) {
      // 异常情况，保持空数据
      setState(() {
        _frequentApps = [];
        _filteredApps = [];
        _isLoading = false;
        _hasCache = true;
      });
    }
  }



  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        // 点击其他区域时失去搜索栏焦点
        _searchFocusNode.unfocus();
      },
      child: Scaffold(
        backgroundColor: AppTheme.getBackgroundColor(context),
        appBar: AppBar(
          title: Text(LocalizationService.t('workspace')),
          actions: [
            IconButton(
              onPressed: _isEditButtonDisabled ? null : _toggleEditMode,
              icon: _isEditButtonDisabled
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
                      ),
                    )
                  : Icon(_isEditMode ? Icons.save : Icons.edit),
              tooltip: _isEditMode
                  ? LocalizationService.t('save')
                  : LocalizationService.t('edit'),
              style: IconButton.styleFrom(
                disabledForegroundColor: Theme.of(context).brightness == Brightness.dark
                    ? AppTheme.darkTextSecondary
                    : AppTheme.textSecondary,
              ),
            ),
          ],
        ),
        body: _isLoading && !_hasCache
            ? const Column(
                children: [
                  // 搜索栏骨架
                  SizedBox(height: 80),
                  // 工作台应用骨架屏
                  Expanded(
                    child: SingleChildScrollView(
                      padding: EdgeInsets.all(AppTheme.paddingMedium),
                      child: WorkspaceAppsSkeleton(),
                    ),
                  ),
                ],
              )
            : Column(
                children: [
                  // 搜索栏
                  _buildSearchBar(),
                  // 工作台内容
                  Expanded(
                    child: RefreshIndicator(
                      onRefresh: _refreshWorkspace,
                      child: SingleChildScrollView(
                        // 确保即使内容不足也能触发下拉刷新
                        physics: const AlwaysScrollableScrollPhysics(),
                        padding: const EdgeInsets.all(AppTheme.paddingMedium),
                        child: ConstrainedBox(
                          constraints: BoxConstraints(
                            minHeight: MediaQuery.of(context).size.height - 200,
                          ),
                          child: Column(
                            children: [_buildFrequentApps()],
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
      ),
    );
  }

  Widget _buildSearchBar() {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Container(
      padding: const EdgeInsets.all(AppTheme.paddingMedium),
      color: AppTheme.getSurfaceColor(context),
      child: TextField(
        controller: _searchController,
        focusNode: _searchFocusNode,
        onChanged: _filterApps,
        style: TextStyle(
          color: isDark ? AppTheme.darkTextPrimary : AppTheme.textPrimary,
          fontSize: AppTheme.fontSizeMedium,
        ),
        decoration: InputDecoration(
          hintText: LocalizationService.t('search_apps'),
          hintStyle: TextStyle(
            color: isDark ? AppTheme.darkTextTertiary : AppTheme.textTertiary,
            fontSize: AppTheme.fontSizeMedium,
          ),
          prefixIcon: Icon(
            Icons.search,
            color: isDark ? AppTheme.darkTextTertiary : AppTheme.textTertiary,
          ),
          suffixIcon:
              _searchController.text.isNotEmpty
                  ? IconButton(
                    onPressed: _clearSearch,
                    icon: Icon(
                      Icons.clear,
                      color:
                          isDark
                              ? AppTheme.darkTextTertiary
                              : AppTheme.textTertiary,
                    ),
                  )
                  : null,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
            borderSide: BorderSide(
              color: isDark ? AppTheme.darkBorder : AppTheme.borderColor,
              width: 1,
            ),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
            borderSide: BorderSide(
              color: isDark ? AppTheme.darkBorder : AppTheme.borderColor,
              width: 1,
            ),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
            borderSide: BorderSide(color: AppTheme.primaryColor, width: 2),
          ),
          filled: true,
          fillColor: isDark ? AppTheme.darkCard : AppTheme.white,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: AppTheme.paddingMedium,
            vertical: AppTheme.paddingSmall,
          ),
        ),
      ),
    );
  }

  void _filterApps(String query) {
    setState(() {
      if (query.isEmpty) {
        _filteredApps = List.from(_frequentApps);
      } else {
        _filteredApps =
            _frequentApps.where((app) {
              return app['name'].toLowerCase().contains(query.toLowerCase());
            }).toList();
      }
    });
  }

  void _clearSearch() {
    setState(() {
      _searchController.clear();
      _filteredApps = List.from(_frequentApps);
    });
  }

  void _toggleEditMode() {
    if (_isEditMode) {
      // 退出编辑模式时禁用按钮3秒
      setState(() {
        _isEditMode = false;
        _isEditButtonDisabled = true;
      });

      // 保存应用排序
      _saveAppOrder();

      // 3秒后重新启用编辑按钮
      Future.delayed(const Duration(seconds: 3), () {
        if (mounted) {
          setState(() {
            _isEditButtonDisabled = false;
          });
        }
      });
    } else {
      // 进入编辑模式
      setState(() {
        _isEditMode = true;
      });

      // 显示编辑提示
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(LocalizationService.t('workspace_edit_hint')),
          duration: const Duration(seconds: 3),
          backgroundColor: AppTheme.primaryColor,
        ),
      );
    }
  }

  Future<void> _refreshWorkspace() async {
    // 检查是否在刷新中或距离上次刷新不足3秒
    final now = DateTime.now();
    if (_isRefreshing ||
        (_lastRefreshTime != null &&
            now.difference(_lastRefreshTime!).inSeconds < 3)) {
      return;
    }

    setState(() {
      _isRefreshing = true;
    });

    _lastRefreshTime = now;

    try {
      // 调用API获取工作台应用数据
      final result = await ApiService.getWorkspaceApps(forceRefresh: true, context: context);

      if (result['success'] == true) {
        final data = result['data'] as Map<String, dynamic>;
        final favoriteApps = data['favoriteApps'] as List<dynamic>? ?? [];

        // 处理API返回的数据，确保重点字段不为空
        final processedApps = _processWorkspaceApps(favoriteApps);

        // 更新工作台应用数据
        setState(() {
          _frequentApps = processedApps;
          _filteredApps = List.from(_frequentApps);
        });

        // 显示刷新成功提示
        if (mounted) {
          String message;
          Color backgroundColor;

          if (result['fromCache'] == true) {
            // 从缓存获取数据
            if (result['isOffline'] == true) {
              message = LocalizationService.t('fetch_success_offline_cache');
              backgroundColor = AppTheme.warningColor;
            } else {
              message = LocalizationService.t('fetch_success_cache');
              backgroundColor = AppTheme.infoColor;
            }
          } else {
            // 从网络获取新数据
            message = LocalizationService.t('refresh_success');
            backgroundColor = AppTheme.successColor;
          }

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(message),
              duration: const Duration(seconds: 1),
              backgroundColor: backgroundColor,
            ),
          );
        }
      } else {
        // 检查是否为token过期错误
        if (result['isTokenExpired'] == true) {
          // Token过期时，TokenExpiryHandler已经处理了对话框和跳转
          // 这里不显示额外的错误信息
          return;
        }

        // API调用失败，保持当前数据不变
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(LocalizationService.t('refresh_failed')),
              duration: const Duration(seconds: 2),
              backgroundColor: AppTheme.errorColor,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(LocalizationService.t('refresh_failed')),
            duration: const Duration(seconds: 2),
            backgroundColor: AppTheme.errorColor,
          ),
        );
      }
    } finally {
      // 3秒后重新启用刷新功能
      Future.delayed(const Duration(seconds: 3), () {
        if (mounted) {
          setState(() {
            _isRefreshing = false;
          });
        }
      });
    }
  }

  Future<void> _saveAppOrder() async {
    // TODO: 保存到SharedPreferences和后端API
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(LocalizationService.t('changes_saved')),
        backgroundColor: AppTheme.successColor,
      ),
    );
  }

  void _toggleFrequentAppsExpansion() {
    setState(() {
      _isFrequentAppsExpanded = !_isFrequentAppsExpanded;
    });
  }

  void _showReorderDialog(Map<String, dynamic> app) {
    showDialog(
      context: context,
      builder:
          (context) => Dialog(
            child: Container(
              constraints: BoxConstraints(
                maxWidth: MediaQuery.of(context).size.width * 0.9,
                maxHeight: MediaQuery.of(context).size.height * 0.8,
              ),
              child: Padding(
                padding: const EdgeInsets.all(AppTheme.paddingLarge),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 标题
                    Text(
                      LocalizationService.t('adjust_app_order'),
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: AppTheme.paddingMedium),

                    // 应用信息
                    Container(
                      padding: const EdgeInsets.all(AppTheme.paddingMedium),
                      decoration: BoxDecoration(
                        color: AppTheme.getCardColor(context),
                        borderRadius: BorderRadius.circular(
                          AppTheme.radiusMedium,
                        ),
                        border: Border.all(
                          color: AppTheme.getBorderColor(context),
                          width: 1,
                        ),
                      ),
                      child: Row(
                        children: [
                          Container(
                            width: 40,
                            height: 40,
                            decoration: BoxDecoration(
                              color: Color(int.parse(app['color'])),
                              borderRadius: BorderRadius.circular(
                                AppTheme.radiusSmall,
                              ),
                            ),
                            child: Icon(
                              _getIconData(app['icon']),
                              color: AppTheme.white,
                              size: 20,
                            ),
                          ),
                          const SizedBox(width: AppTheme.paddingMedium),
                          Expanded(
                            child: Text(
                              app['name'],
                              style: Theme.of(context).textTheme.bodyLarge,
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: AppTheme.paddingLarge),

                    // 操作说明
                    Text(
                      LocalizationService.t('select_new_position'),
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: AppTheme.textSecondary,
                      ),
                    ),
                    const SizedBox(height: AppTheme.paddingMedium),

                    // 操作按钮
                    Column(
                      children: [
                        SizedBox(
                          width: double.infinity,
                          child: ElevatedButton.icon(
                            onPressed: () => _moveApp(app, -1),
                            icon: const Icon(Icons.keyboard_arrow_up),
                            label: Text(LocalizationService.t('move_forward')),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: AppTheme.primaryColor,
                              foregroundColor: AppTheme.white,
                              padding: const EdgeInsets.symmetric(
                                vertical: AppTheme.paddingMedium,
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(height: AppTheme.paddingSmall),
                        SizedBox(
                          width: double.infinity,
                          child: ElevatedButton.icon(
                            onPressed: () => _moveApp(app, 1),
                            icon: const Icon(Icons.keyboard_arrow_down),
                            label: Text(LocalizationService.t('move_backward')),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: AppTheme.primaryColor,
                              foregroundColor: AppTheme.white,
                              padding: const EdgeInsets.symmetric(
                                vertical: AppTheme.paddingMedium,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: AppTheme.paddingLarge),

                    // 取消按钮
                    SizedBox(
                      width: double.infinity,
                      child: TextButton(
                        onPressed: () => Navigator.of(context).pop(),
                        style: TextButton.styleFrom(
                          padding: const EdgeInsets.symmetric(
                            vertical: AppTheme.paddingMedium,
                          ),
                        ),
                        child: Text(LocalizationService.t('cancel')),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
    );
  }

  void _moveApp(Map<String, dynamic> app, int direction) {
    // 通过名称查找应用，因为对象引用可能不同
    final currentIndex = _frequentApps.indexWhere(
      (item) => item['name'] == app['name'],
    );

    if (currentIndex == -1) {
      Navigator.of(context).pop();
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('${LocalizationService.t('app_not_found')}: ${app['name']}'),
          backgroundColor: AppTheme.errorColor,
        ),
      );
      return;
    }

    final newIndex = currentIndex + direction;
    if (newIndex < 0 || newIndex >= _frequentApps.length) {
      Navigator.of(context).pop();
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(LocalizationService.t('already_first_last')),
          backgroundColor: AppTheme.warningColor,
        ),
      );
      return;
    }

    setState(() {
      final item = _frequentApps.removeAt(currentIndex);
      _frequentApps.insert(newIndex, item);
      _filteredApps = List.from(_frequentApps);
    });

    Navigator.of(context).pop();

    // 显示更详细的移动信息
    final directionKey = direction == -1 ? 'moved_forward_to_position' : 'moved_backward_to_position';
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('${app['name']} ${LocalizationService.t(directionKey)} ${newIndex + 1}'),
        backgroundColor: AppTheme.successColor,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  Widget _buildFrequentApps() {
    return Container(
      decoration: BoxDecoration(
        color: AppTheme.getCardColor(context),
        borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
        boxShadow: [AppTheme.getCardShadow(context)],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          InkWell(
            onTap: _toggleFrequentAppsExpansion,
            child: Padding(
              padding: const EdgeInsets.all(AppTheme.paddingMedium),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    LocalizationService.t('frequent_apps'),
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Icon(
                    _isFrequentAppsExpanded
                        ? Icons.keyboard_arrow_down
                        : Icons.keyboard_arrow_right,
                    color: AppTheme.textSecondary,
                  ),
                ],
              ),
            ),
          ),
          if (_isFrequentAppsExpanded)
            Padding(
              padding: const EdgeInsets.fromLTRB(
                AppTheme.paddingMedium,
                0,
                AppTheme.paddingMedium,
                AppTheme.paddingMedium,
              ),
              child: _filteredApps.isEmpty
                  ? _buildEmptyState()
                  : GridView.builder(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 3,
                        crossAxisSpacing: AppTheme.paddingMedium,
                        mainAxisSpacing: AppTheme.paddingMedium,
                        childAspectRatio: 1,
                      ),
                      itemCount: _filteredApps.length,
                      itemBuilder: (context, index) {
                        final app = _filteredApps[index];
                        return AnimatedListItem(
                          index: index,
                          child: _buildAppItem(app),
                        );
                      },
                    ),
            ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Container(
      padding: const EdgeInsets.all(AppTheme.paddingLarge),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.apps_outlined,
            size: 64,
            color: AppTheme.textTertiary,
          ),
          const SizedBox(height: AppTheme.paddingMedium),
          Text(
            LocalizationService.t('no_frequent_apps'),
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppTheme.textTertiary,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppTheme.paddingSmall),
          Text(
            LocalizationService.t('no_frequent_apps_hint'),
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: AppTheme.textTertiary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildAppItem(Map<String, dynamic> app) {
    return AnimatedButton(
      onPressed: () => _isEditMode ? _showReorderDialog(app) : _openApp(app),
      child: Stack(
        children: [
          Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    color: Color(int.parse(app['color'])),
                    borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
                  ),
                  child: Center(
                    child: Icon(
                      _getIconData(app['icon']),
                      color: AppTheme.white,
                      size: 24,
                    ),
                  ),
                ),
                const SizedBox(height: AppTheme.paddingSmall),
                Text(
                  app['name'],
                  style: Theme.of(context).textTheme.bodySmall,
                  textAlign: TextAlign.center,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
          if (_isEditMode)
            Positioned(
              top: 0,
              right: 0,
              child: Container(
                width: 20,
                height: 20,
                decoration: BoxDecoration(
                  color: AppTheme.primaryColor,
                  shape: BoxShape.circle,
                  border: Border.all(color: AppTheme.white, width: 2),
                ),
                child: const Icon(
                  Icons.drag_handle,
                  size: 12,
                  color: AppTheme.white,
                ),
              ),
            ),
        ],
      ),
    );
  }

  IconData _getIconData(String iconName) {
    switch (iconName) {
      case 'calendar_today':
        return Icons.calendar_today;
      case 'assignment':
        return Icons.assignment;
      case 'description':
        return Icons.description;
      case 'business':
        return Icons.business;
      case 'folder_open':
        return Icons.folder_open;
      case 'pie_chart':
        return Icons.pie_chart;
      case 'email':
        return Icons.email;
      case 'video_call':
        return Icons.video_call;
      case 'people':
        return Icons.people;
      case 'access_time':
        return Icons.access_time;
      case 'trending_up':
        return Icons.trending_up;
      case 'chat':
        return Icons.chat;
      case 'receipt':
        return Icons.receipt;
      case 'account_balance':
        return Icons.account_balance;
      case 'receipt_long':
        return Icons.receipt_long;
      case 'assessment':
        return Icons.assessment;
      case 'school':
        return Icons.school;
      case 'inventory':
        return Icons.inventory;
      case 'campaign':
        return Icons.campaign;
      case 'forum':
        return Icons.forum;
      case 'folder':
        return Icons.folder;
      case 'calculate':
        return Icons.calculate;
      case 'qr_code_scanner':
        return Icons.qr_code_scanner;
      case 'note_add':
        return Icons.note_add;
      default:
        return Icons.apps;
    }
  }

  void _openApp(Map<String, dynamic> app) {
    // 检查应用数据是否有效
    final appName = app['name'] as String? ?? '';
    final appUrl = app['url'] as String? ?? '';

    if (appName.isEmpty) {
      // 应用名称为空，显示提示信息
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(LocalizationService.t('app_data_invalid')),
          backgroundColor: AppTheme.warningColor,
        ),
      );
      return;
    }

    // 如果应用URL为空，使用默认的详情页面
    final targetUrl = appUrl.isNotEmpty
        ? appUrl
        : 'assets/html/workspace_detail.html?id=${_getAppId(appName)}';

    Navigator.of(context).push(
      PageTransitions.slideRoute(
        page: WebViewScreen(
          title: appName,
          url: targetUrl,
        ),
        type: PageTransitionType.slideFromRight,
        duration: const Duration(milliseconds: 300),
      ),
    );
  }

  String _getAppId(String appName) {
    if (appName == LocalizationService.t('schedule_management')) {
      return '1';
    } else if (appName == LocalizationService.t('task_collaboration')) {
      return '2';
    } else if (appName == LocalizationService.t('document_center')) {
      return '3';
    } else if (appName == LocalizationService.t('customer_management')) {
      return '4';
    } else if (appName == LocalizationService.t('project_management')) {
      return '5';
    } else if (appName == LocalizationService.t('financial_reports')) {
      return '6';
    } else {
      return '1';
    }
  }

  /// 处理工作台应用数据，确保重点字段不为空
  List<Map<String, dynamic>> _processWorkspaceApps(List<dynamic> apps) {
    final processedApps = <Map<String, dynamic>>[];

    for (final app in apps) {
      final appData = app as Map<String, dynamic>? ?? {};

      // 确保重点字段不为空
      final name = appData['name'] as String? ?? LocalizationService.t('unknown_app');
      final icon = appData['icon'] as String? ?? 'apps';
      final color = appData['color'] as String? ?? _getDefaultColorForApp(name);

      processedApps.add({
        'name': name,
        'icon': icon,
        'color': color,
        'description': appData['description'] as String? ?? '',
        'url': appData['url'] as String? ?? '',
        'sort': appData['sort'] as int? ?? 0,
      });
    }

    return processedApps;
  }

  /// 为应用获取默认颜色
  String _getDefaultColorForApp(String appName) {
    // 根据应用名称返回默认颜色
    final colors = [
      '0xFF3366CC', '0xFFFF9900', '0xFF52C41A', '0xFF1890FF', '0xFF722ED1',
      '0xFFF5222D', '0xFFFAAD14', '0xFF13C2C2', '0xFF9C27B0', '0xFF795548',
    ];
    return colors[appName.hashCode % colors.length];
  }

  @override
  void dispose() {
    _searchController.dispose();
    _searchFocusNode.dispose();
    super.dispose();
  }
}
