import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../constants/app_theme.dart';
import '../services/localization_service.dart';
import '../services/error_log_service.dart';

class ErrorLogScreen extends StatefulWidget {
  const ErrorLogScreen({super.key});

  @override
  State<ErrorLogScreen> createState() => _ErrorLogScreenState();
}

class _ErrorLogScreenState extends State<ErrorLogScreen> {
  List<ErrorLogEntry> _logs = [];
  List<ErrorLogEntry> _filteredLogs = [];
  bool _isLoading = true;
  String? _selectedLogId;
  String? _selectedFilter; // 当前选择的过滤器

  @override
  void initState() {
    super.initState();
    _loadLogs();
  }

  Future<void> _loadLogs() async {
    setState(() {
      _isLoading = true;
    });

    try {
      await ErrorLogService.initialize();
      setState(() {
        _logs = ErrorLogService.getAllLogs();
        _applyFilter();
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${LocalizationService.t('loading')} ${LocalizationService.t('error')}: $e'),
            backgroundColor: AppTheme.errorColor,
          ),
        );
      }
    }
  }

  Future<void> _clearAllLogs() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(LocalizationService.t('clear_all_logs')),
        content: Text(LocalizationService.t('clear_logs_confirm')),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(LocalizationService.t('cancel')),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: Text(LocalizationService.t('confirm')),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      await ErrorLogService.clearAllLogs();
      await _loadLogs();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(LocalizationService.t('logs_cleared')),
            backgroundColor: AppTheme.successColor,
          ),
        );
      }
    }
  }

  Future<void> _copyErrorInfo(ErrorLogEntry log) async {
    await Clipboard.setData(ClipboardData(text: log.toString()));
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(LocalizationService.t('copy_success')),
          backgroundColor: AppTheme.successColor,
        ),
      );
    }
  }

  Future<void> _exportAllLogs() async {
    // 根据当前过滤状态决定复制哪些数据
    final logsToExport = _selectedFilter != null ? _filteredLogs : _logs;
    final exportData = ErrorLogService.exportLogsListAsString(
      logsToExport,
      filterType: _selectedFilter,
    );

    await Clipboard.setData(ClipboardData(text: exportData));
    if (mounted) {
      // 根据是否有过滤器显示不同的提示信息
      final message = _selectedFilter != null
          ? '${LocalizationService.t('copy_filtered_success')}: ${_getErrorTypeDisplayName(_selectedFilter!)}'
          : LocalizationService.t('copy_success');

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: AppTheme.successColor,
        ),
      );
    }
  }

  /// 应用过滤器
  void _applyFilter() {
    if (_selectedFilter == null) {
      _filteredLogs = List.from(_logs);
    } else {
      _filteredLogs = _logs.where((log) => log.type == _selectedFilter).toList();
    }
  }

  /// 设置过滤器
  void _setFilter(String? filterType) {
    setState(() {
      _selectedFilter = filterType;
      _applyFilter();
    });
  }

  /// 清除过滤器，显示所有日志
  void _clearFilter() {
    _setFilter(null);
  }

  String _getErrorTypeDisplayName(String type) {
    switch (type) {
      case 'flutter_error':
        return LocalizationService.t('flutter_error');
      case 'dart_error':
        return LocalizationService.t('dart_error');
      case 'log_network_error':
        return LocalizationService.t('log_network_error');
      case 'app_error':
        return LocalizationService.t('app_error');
      default:
        return LocalizationService.t('log_unknown_error');
    }
  }

  Color _getErrorTypeColor(String type) {
    switch (type) {
      case 'flutter_error':
        return AppTheme.errorColor;
      case 'dart_error':
        return AppTheme.warningColor;
      case 'log_network_error':
        return AppTheme.infoColor;
      case 'app_error':
        return AppTheme.primaryColor;
      default:
        return AppTheme.textSecondary;
    }
  }

  IconData _getErrorTypeIcon(String type) {
    switch (type) {
      case 'flutter_error':
        return Icons.bug_report;
      case 'dart_error':
        return Icons.code;
      case 'log_network_error':
        return Icons.wifi_off;
      case 'app_error':
        return Icons.error_outline;
      default:
        return Icons.help_outline;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.getBackgroundColor(context),
      appBar: AppBar(
        title: Text(LocalizationService.t('error_log_title')),
        actions: [
          // 添加测试错误按钮（仅在调试模式下显示）
          if (kDebugMode)
            IconButton(
              icon: const Icon(Icons.bug_report),
              onPressed: _createTestError,
              tooltip: LocalizationService.t('create_test_error'),
            ),
          if (_logs.isNotEmpty) ...[
            IconButton(
              icon: const Icon(Icons.file_copy),
              onPressed: _exportAllLogs,
              tooltip: LocalizationService.t('copy_error'),
            ),
            IconButton(
              icon: const Icon(Icons.clear_all),
              onPressed: _clearAllLogs,
              tooltip: LocalizationService.t('clear_all_logs'),
            ),
          ],
        ],
      ),
      body: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : _logs.isEmpty
                ? _buildEmptyState()
                : Column(
                  children: [
                    _buildStatsCard(),
                    if (_selectedFilter != null) _buildFilterHeader(),
                    Expanded(child: _buildLogsList()),
                  ],
                ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.check_circle_outline,
            size: 64,
            color: AppTheme.successColor,
          ),
          const SizedBox(height: 16),
          Text(
            LocalizationService.t('no_error_logs'),
            style: TextStyle(
              fontSize: 18,
              color: AppTheme.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatsCard() {
    final stats = ErrorLogService.getErrorStats();
    
    return Container(
      margin: const EdgeInsets.all(AppTheme.paddingMedium),
      padding: const EdgeInsets.all(AppTheme.paddingMedium),
      decoration: BoxDecoration(
        color: AppTheme.getCardColor(context),
        borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
        boxShadow: [AppTheme.getCardShadow(context)],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.analytics, color: AppTheme.primaryColor),
              const SizedBox(width: 8),
              Text(
                LocalizationService.t('error_count'),
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              Text(
                '${_logs.length}',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.primaryColor,
                ),
              ),
            ],
          ),
          if (stats.isNotEmpty) ...[
            const SizedBox(height: 12),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: stats.entries.map((entry) {
                final isSelected = _selectedFilter == entry.key;
                return GestureDetector(
                  onTap: () => _setFilter(entry.key),
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: isSelected
                          ? _getErrorTypeColor(entry.key).withValues(alpha: 0.2)
                          : _getErrorTypeColor(entry.key).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: isSelected
                            ? _getErrorTypeColor(entry.key)
                            : _getErrorTypeColor(entry.key).withValues(alpha: 0.3),
                        width: isSelected ? 2 : 1,
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          _getErrorTypeIcon(entry.key),
                          size: 16,
                          color: _getErrorTypeColor(entry.key),
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '${_getErrorTypeDisplayName(entry.key)}: ${entry.value}',
                          style: TextStyle(
                            fontSize: 12,
                            color: _getErrorTypeColor(entry.key),
                            fontWeight: isSelected ? FontWeight.bold : FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              }).toList(),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildFilterHeader() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: AppTheme.paddingMedium),
      padding: const EdgeInsets.all(AppTheme.paddingSmall),
      decoration: BoxDecoration(
        color: AppTheme.getCardColor(context),
        borderRadius: BorderRadius.circular(AppTheme.radiusSmall),
        border: Border.all(
          color: _getErrorTypeColor(_selectedFilter!).withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.filter_list,
            size: 16,
            color: _getErrorTypeColor(_selectedFilter!),
          ),
          const SizedBox(width: 8),
          Text(
            '${LocalizationService.t('filter_by')}: ${_getErrorTypeDisplayName(_selectedFilter!)}',
            style: TextStyle(
              fontSize: 14,
              color: _getErrorTypeColor(_selectedFilter!),
              fontWeight: FontWeight.w500,
            ),
          ),
          const Spacer(),
          GestureDetector(
            onTap: () => _setFilter(null),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: AppTheme.textSecondary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.clear,
                    size: 14,
                    color: AppTheme.textSecondary,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    LocalizationService.t('show_all'),
                    style: TextStyle(
                      fontSize: 12,
                      color: AppTheme.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLogsList() {
    final logsToShow = _filteredLogs.isEmpty && _selectedFilter == null ? _logs : _filteredLogs;

    if (logsToShow.isEmpty && _selectedFilter != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: 64,
              color: AppTheme.textSecondary,
            ),
            const SizedBox(height: 16),
            Text(
              '${LocalizationService.t('no_logs_found')}: ${_getErrorTypeDisplayName(_selectedFilter!)}',
              style: TextStyle(
                fontSize: 16,
                color: AppTheme.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: AppTheme.paddingMedium),
      itemCount: logsToShow.length,
      itemBuilder: (context, index) {
        final log = logsToShow[index];
        final isSelected = _selectedLogId == log.id;
        
        return Card(
          margin: const EdgeInsets.only(bottom: AppTheme.paddingSmall),
          child: Column(
            children: [
              ListTile(
                leading: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: _getErrorTypeColor(log.type).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    _getErrorTypeIcon(log.type),
                    color: _getErrorTypeColor(log.type),
                    size: 20,
                  ),
                ),
                title: Text(
                  _getErrorTypeDisplayName(log.type),
                  style: const TextStyle(fontWeight: FontWeight.w500),
                ),
                subtitle: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      log.message,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      _formatDateTime(log.timestamp),
                      style: TextStyle(
                        fontSize: 12,
                        color: AppTheme.textTertiary,
                      ),
                    ),
                  ],
                ),
                trailing: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    IconButton(
                      icon: const Icon(Icons.copy, size: 20),
                      onPressed: () => _copyErrorInfo(log),
                      tooltip: LocalizationService.t('copy_error'),
                    ),
                    IconButton(
                      icon: Icon(
                        isSelected ? Icons.expand_less : Icons.expand_more,
                        size: 20,
                      ),
                      onPressed: () {
                        setState(() {
                          _selectedLogId = isSelected ? null : log.id;
                        });
                      },
                      tooltip: isSelected 
                          ? LocalizationService.t('close_details')
                          : LocalizationService.t('view_details'),
                    ),
                  ],
                ),
                onTap: () {
                  setState(() {
                    _selectedLogId = isSelected ? null : log.id;
                  });
                },
              ),
              if (isSelected) _buildLogDetails(log),
            ],
          ),
        );
      },
    );
  }

  Widget _buildLogDetails(ErrorLogEntry log) {
    return Container(
      padding: const EdgeInsets.all(AppTheme.paddingMedium),
      decoration: BoxDecoration(
        color: AppTheme.getBackgroundColor(context),
        border: Border(
          top: BorderSide(
            color: AppTheme.getBorderColor(context).withValues(alpha: 0.5),
          ),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildDetailRow(
            LocalizationService.t('error_type'),
            _getErrorTypeDisplayName(log.type),
          ),
          _buildDetailRow(
            LocalizationService.t('error_time'),
            _formatDateTime(log.timestamp),
          ),
          _buildDetailRow(
            LocalizationService.t('error_message'),
            log.message,
            isMultiline: true,
          ),
          if (log.stackTrace != null) ...[
            const SizedBox(height: 8),
            Text(
              LocalizationService.t('stack_trace'),
              style: const TextStyle(
                fontWeight: FontWeight.w500,
                fontSize: 14,
              ),
            ),
            const SizedBox(height: 4),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: AppTheme.getCardColor(context),
                borderRadius: BorderRadius.circular(4),
                border: Border.all(
                  color: AppTheme.getBorderColor(context).withValues(alpha: 0.3),
                ),
              ),
              child: Text(
                log.stackTrace!,
                style: TextStyle(
                  fontFamily: 'monospace',
                  fontSize: 12,
                  color: AppTheme.textSecondary,
                ),
              ),
            ),
          ],
          if (log.additionalInfo != null && log.additionalInfo!.isNotEmpty) ...[
            const SizedBox(height: 8),
            Text(
              LocalizationService.t('error_details'),
              style: const TextStyle(
                fontWeight: FontWeight.w500,
                fontSize: 14,
              ),
            ),
            const SizedBox(height: 4),
            ...log.additionalInfo!.entries.map((entry) {
              return _buildDetailRow(entry.key, entry.value.toString());
            }),
          ],
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value, {bool isMultiline = false}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: TextStyle(
                fontWeight: FontWeight.w500,
                color: AppTheme.textSecondary,
                fontSize: 13,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontSize: 13,
                color: AppTheme.textPrimary,
              ),
              maxLines: isMultiline ? null : 3,
              overflow: isMultiline ? null : TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  String _formatDateTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return '${difference.inDays}${LocalizationService.t('days_ago')}';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}${LocalizationService.t('hours_ago')}';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}${LocalizationService.t('minutes_ago')}';
    } else {
      return LocalizationService.t('just_now');
    }
  }

  /// 创建测试错误（仅在调试模式下使用）
  Future<void> _createTestError() async {
    if (!kDebugMode) return;

    final testTypes = [
      'flutter_error',
      'dart_error',
      'log_network_error',
      'app_error',
    ];

    final testMessageKeys = [
      'test_flutter_error',
      'test_dart_error',
      'test_network_error',
      'test_app_error',
    ];

    final random = DateTime.now().millisecondsSinceEpoch % testTypes.length;
    final type = testTypes[random];
    final message = LocalizationService.t(testMessageKeys[random]);

    await ErrorLogService.logError(
      type: type,
      message: message,
      stackTrace: 'Test stack trace:\n  at _createTestError (error_log_screen.dart:${DateTime.now().millisecondsSinceEpoch % 100})\n  at onPressed (error_log_screen.dart:166)',
      additionalInfo: {
        'testMode': true,
        'timestamp': DateTime.now().toIso8601String(),
        'randomValue': random,
      },
    );

    // 刷新日志列表
    await _loadLogs();

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('${LocalizationService.t('test_error_created')}: $message'),
          backgroundColor: AppTheme.infoColor,
        ),
      );
    }
  }
}