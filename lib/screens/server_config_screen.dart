import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../constants/app_theme.dart';
import '../constants/app_constants.dart';
import '../services/localization_service.dart';
import '../services/http_client.dart' as http_service;
import 'connection_error_screen.dart';
// import 'qr_scanner_screen.dart';  // 暂时注释掉

class ServerConfigScreen extends StatefulWidget {
  final String? prefilledName;
  final String? prefilledAddress;
  final String? prefilledPort;
  final bool clearFields;

  const ServerConfigScreen({
    super.key,
    this.prefilledName,
    this.prefilledAddress,
    this.prefilledPort,
    this.clearFields = false,
  });

  @override
  State<ServerConfigScreen> createState() => _ServerConfigScreenState();
}

class _ServerConfigScreenState extends State<ServerConfigScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _addressController = TextEditingController();
  final _portController = TextEditingController();
  final _scrollController = ScrollController();
  List<Map<String, String>> _savedServers = [];
  bool _isLoading = false;
  bool _isTestingConnection = false;
  DateTime? _lastTestTime;

  // 验证错误状态
  String? _nameError;
  String? _addressError;
  String? _portError;

  @override
  void initState() {
    super.initState();
    _loadSavedServers();

    // Handle prefilled values or clear fields
    if (widget.clearFields) {
      // Clear all fields for new configuration
      setState(() {
        _nameController.text = '';
        _addressController.text = '';
        _portController.text = '';
      });
    } else if (widget.prefilledName != null ||
        widget.prefilledAddress != null ||
        widget.prefilledPort != null) {
      // Use prefilled values
      setState(() {
        _nameController.text = widget.prefilledName ?? '';
        _addressController.text = widget.prefilledAddress ?? '';
        _portController.text = widget.prefilledPort ?? '';
      });
    } else {
      // Load current server config
      _loadServerConfig();
    }
  }

  Future<void> _loadServerConfig() async {
    final prefs = await SharedPreferences.getInstance();
    final address =
        prefs.getString(AppConstants.keyServerAddress) ??
        AppConstants.defaultServerAddress;
    final port =
        prefs.getString(AppConstants.keyServerPort) ??
        AppConstants.defaultServerPort;
    final serverName = prefs.getString('selected_server_name') ?? '';

    setState(() {
      _addressController.text = address;
      _portController.text = port;
      _nameController.text = serverName;
    });
  }

  Future<void> _loadSavedServers() async {
    final prefs = await SharedPreferences.getInstance();
    final serversJson = prefs.getString(AppConstants.keySavedServers) ?? '[]';
    final List<dynamic> serversList = json.decode(serversJson);

    setState(() {
      _savedServers =
          serversList
              .map((server) => Map<String, String>.from(server))
              .toList();
    });
  }

  Future<void> _saveServerConfig() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    // 模拟测试连接
    await Future.delayed(const Duration(seconds: 1));

    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(
      AppConstants.keyServerAddress,
      _addressController.text,
    );
    await prefs.setString(AppConstants.keyServerPort, _portController.text);

    // 保存服务器名称
    final serverName =
        _nameController.text.trim().isEmpty
            ? '${_addressController.text}:${_portController.text}'
            : _nameController.text.trim();
    await prefs.setString('selected_server_name', serverName);

    // 添加到已保存的服务器列表
    final newServer = {
      'name': serverName,
      'address': _addressController.text,
      'port': _portController.text,
    };

    // 检查是否已存在相同的服务器
    final existingIndex = _savedServers.indexWhere(
      (server) =>
          server['address'] == _addressController.text &&
          server['port'] == _portController.text,
    );

    String successMessage;
    if (existingIndex >= 0) {
      _savedServers[existingIndex] = newServer;
      successMessage = LocalizationService.t('server_config_updated');
    } else {
      _savedServers.add(newServer);
      successMessage = LocalizationService.t('config_saved');
    }

    await _saveSavedServers();

    if (mounted) {
      setState(() {
        _isLoading = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(successMessage),
          backgroundColor: AppTheme.successColor,
        ),
      );

      Navigator.of(context).pop(true);
    }
  }

  Future<void> _testConnection() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // 检查节流：如果距离上次测试不足3秒，则不执行
    final now = DateTime.now();
    if (_isTestingConnection ||
        (_lastTestTime != null && now.difference(_lastTestTime!).inSeconds < 3)) {
      return;
    }

    setState(() {
      _isLoading = true;
      _isTestingConnection = true;
    });

    _lastTestTime = now;

    try {
      // 构建测试URL
      final testUrl = 'http://${_addressController.text}:${_portController.text}/api/system/ping';

      // 调用真实接口测试连接
      final httpClient = http_service.HttpClient();
      await httpClient.initialize();

      final response = await httpClient.request(
        http_service.HttpRequestConfig(
          url: testUrl,
          method: http_service.HttpMethod.get,
          timeout: const Duration(seconds: 10),
          useCache: false,
        ),
      );

      if (mounted) {
        setState(() {
          _isLoading = false;
          _isTestingConnection = false;
        });

        if (response.statusCode == 200) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(LocalizationService.t('connection_test_success_msg')),
              backgroundColor: AppTheme.successColor,
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('${LocalizationService.t('connection_test_failed')}: ${response.statusCode}'),
              backgroundColor: AppTheme.errorColor,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _isTestingConnection = false;
        });

        // 显示简单的错误消息提示
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(LocalizationService.t('connection_test_failed')),
            backgroundColor: AppTheme.errorColor,
          ),
        );
      }
    }
  }

  /// 显示连接错误页面
  void _showConnectionErrorPage() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => ConnectionErrorScreen(
          serverAddress: _addressController.text,
          serverPort: _portController.text,
          errorMessage: LocalizationService.t('connection_test_failed'),
        ),
      ),
    );
  }

  /// 验证服务器名称
  void _validateServerName(String value) {
    setState(() {
      if (value.trim().isEmpty) {
        _nameError = LocalizationService.t('server_name_required');
      } else {
        _nameError = null;
      }
    });
  }

  /// 验证服务器地址
  void _validateServerAddress(String value) {
    setState(() {
      if (value.trim().isEmpty) {
        _addressError = LocalizationService.t('server_address_required');
      } else {
        _addressError = null;
      }
    });
  }

  /// 验证服务器端口
  void _validateServerPort(String value) {
    setState(() {
      if (value.trim().isEmpty) {
        _portError = LocalizationService.t('server_port_required');
      } else {
        _portError = null;
      }
    });
  }

  void _showSavedServers() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder:
          (context) => Container(
            padding: const EdgeInsets.all(AppTheme.paddingLarge),
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        LocalizationService.t('saved_servers_title'),
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      IconButton(
                        onPressed: () => Navigator.of(context).pop(),
                        icon: const Icon(Icons.close),
                      ),
                    ],
                  ),
                  const SizedBox(height: AppTheme.paddingMedium),
                  if (_savedServers.isEmpty)
                    Padding(
                      padding: const EdgeInsets.all(AppTheme.paddingXLarge),
                      child: Text(LocalizationService.t('no_saved_servers')),
                    )
                  else
                    Container(
                      constraints: const BoxConstraints(maxHeight: 300),
                      child: Scrollbar(
                        thumbVisibility: true,
                        controller: _scrollController,
                        child: ListView.builder(
                          controller: _scrollController,
                          shrinkWrap: true,
                          itemCount: _savedServers.length,
                          itemBuilder: (context, index) {
                            final server = _savedServers[index];
                            return ListTile(
                              title: Text(server['name'] ?? ''),
                              subtitle: Text(
                                '${server['address']}:${server['port']}',
                              ),
                              trailing: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  IconButton(
                                    onPressed: () => _selectServer(server),
                                    icon: const Icon(
                                      Icons.check,
                                      color: AppTheme.primaryColor,
                                    ),
                                    tooltip: LocalizationService.t(
                                      'select_server',
                                    ),
                                  ),
                                  IconButton(
                                    onPressed: () => _editServer(server),
                                    icon: const Icon(
                                      Icons.edit,
                                      color: AppTheme.secondaryColor,
                                    ),
                                    tooltip: LocalizationService.t(
                                      'edit_server_tooltip',
                                    ),
                                  ),
                                  IconButton(
                                    onPressed: () => _deleteServer(server),
                                    icon: const Icon(
                                      Icons.delete,
                                      color: AppTheme.errorColor,
                                    ),
                                    tooltip: LocalizationService.t(
                                      'delete_server_tooltip',
                                    ),
                                  ),
                                ],
                              ),
                            );
                          },
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),
    );
  }

  void _selectServer(Map<String, String> server) {
    setState(() {
      _nameController.text = server['name'] ?? '';
      _addressController.text = server['address'] ?? '';
      _portController.text = server['port'] ?? '';
    });
    Navigator.of(context).pop();
  }

  void _editServer(Map<String, String> server) {
    final nameController = TextEditingController(text: server['name']);
    final addressController = TextEditingController(text: server['address']);
    final portController = TextEditingController(text: server['port']);
    final formKey = GlobalKey<FormState>();

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(LocalizationService.t('edit_server_config')),
            content: Form(
              key: formKey,
              child: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    TextFormField(
                      controller: nameController,
                      decoration: InputDecoration(
                        labelText: LocalizationService.t('server_name'),
                        hintText: LocalizationService.t('enter_server_name'),
                        border: const OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: addressController,
                      decoration: InputDecoration(
                        labelText: LocalizationService.t('server_address'),
                        hintText: LocalizationService.t('enter_server_address'),
                        border: const OutlineInputBorder(),
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return LocalizationService.t(
                            'server_address_required',
                          );
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: portController,
                      decoration: InputDecoration(
                        labelText: LocalizationService.t('server_port'),
                        hintText: LocalizationService.t('enter_server_port'),
                        border: const OutlineInputBorder(),
                      ),
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return LocalizationService.t('server_port_required');
                        }
                        return null;
                      },
                    ),
                  ],
                ),
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text(LocalizationService.t('cancel')),
              ),
              ElevatedButton(
                onPressed:
                    () => _updateServer(
                      server,
                      nameController,
                      addressController,
                      portController,
                      formKey,
                    ),
                child: Text(LocalizationService.t('save')),
              ),
            ],
          ),
    );
  }

  void _updateServer(
    Map<String, String> originalServer,
    TextEditingController nameController,
    TextEditingController addressController,
    TextEditingController portController,
    GlobalKey<FormState> formKey,
  ) {
    if (!formKey.currentState!.validate()) {
      return;
    }

    final newName =
        nameController.text.trim().isEmpty
            ? '${addressController.text}:${portController.text}'
            : nameController.text.trim();

    final updatedServer = {
      'name': newName,
      'address': addressController.text,
      'port': portController.text,
    };

    final index = _savedServers.indexOf(originalServer);
    if (index >= 0) {
      setState(() {
        _savedServers[index] = updatedServer;
      });
      _saveSavedServers();

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(LocalizationService.t('server_config_updated')),
          backgroundColor: AppTheme.successColor,
        ),
      );
    }
    Navigator.of(context).pop();
    Navigator.of(context).pop(); // 关闭服务器列表对话框
  }

  void _deleteServer(Map<String, String> server) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(LocalizationService.t('confirm_delete')),
            content: Text(
              '${LocalizationService.t('delete_server_message')} "${server['name']}" ${LocalizationService.t('question_mark')}',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text(LocalizationService.t('cancel')),
              ),
              ElevatedButton(
                onPressed: () {
                  setState(() {
                    _savedServers.remove(server);
                  });
                  _saveSavedServers();
                  Navigator.of(context).pop();
                  Navigator.of(context).pop();
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.errorColor,
                ),
                child: Text(LocalizationService.t('delete')),
              ),
            ],
          ),
    );
  }

  Future<void> _saveSavedServers() async {
    final prefs = await SharedPreferences.getInstance();
    final serversJson = json.encode(_savedServers);
    await prefs.setString(AppConstants.keySavedServers, serversJson);
  }

  void _scanQRCode() async {
    // 暂时注释掉真实扫描功能，直接显示输入对话框
    _showQRCodeInputDialog();

    // try {
    //   final result = await Navigator.of(context).push<String>(
    //     MaterialPageRoute(builder: (context) => const QRScannerScreen()),
    //   );

    //   if (result != null && result.isNotEmpty) {
    //     _parseQRCodeData(result);
    //   }
    // } catch (e) {
    //   // 如果扫描失败，回退到手动输入
    //   _showQRCodeInputDialog();
    // }
  }

  void _showQRCodeInputDialog() {
    final qrController = TextEditingController();

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(LocalizationService.t('qr_scan_simulation')),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(LocalizationService.t('qr_input_hint')),
                const SizedBox(height: 16),
                TextField(
                  controller: qrController,
                  decoration: InputDecoration(
                    hintText: LocalizationService.t('qr_example'),
                    border: const OutlineInputBorder(),
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text(LocalizationService.t('cancel')),
              ),
              ElevatedButton(
                onPressed: () {
                  _parseQRCodeData(qrController.text);
                  Navigator.of(context).pop();
                },
                child: Text(LocalizationService.t('confirm')),
              ),
            ],
          ),
    );
  }

  void _parseQRCodeData(String qrData) {
    try {
      final parts = qrData.split(',');
      if (parts.isNotEmpty) {
        final addressPort = parts[0].split(':');
        if (addressPort.length == 2) {
          setState(() {
            _addressController.text = addressPort[0];
            _portController.text = addressPort[1];
            if (parts.length > 1 && parts[1].trim().isNotEmpty) {
              _nameController.text = parts[1].trim();
            } else {
              _nameController.text = '';
            }
          });

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(LocalizationService.t('qr_parse_success')),
              backgroundColor: AppTheme.successColor,
            ),
          );
        } else {
          throw Exception('格式错误');
        }
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(LocalizationService.t('qr_format_error')),
          backgroundColor: AppTheme.errorColor,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(LocalizationService.t('server_configuration')),
        leading: IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: const Icon(Icons.arrow_back),
        ),
      ),
      body: SingleChildScrollView(
          padding: const EdgeInsets.all(AppTheme.paddingLarge),
          child: Form(
            key: _formKey,
            child: Column(
              children: [
              // 二维码扫描
              GestureDetector(
                onTap: _scanQRCode,
                child: Container(
                  padding: const EdgeInsets.all(AppTheme.paddingMedium),
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: AppTheme.primaryColor,
                      style: BorderStyle.solid,
                      width: 1,
                    ),
                    borderRadius: BorderRadius.circular(AppTheme.radiusSmall),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.qr_code_scanner,
                        size: 24,
                        color: AppTheme.primaryColor,
                      ),
                      const SizedBox(width: AppTheme.paddingSmall),
                      Text(
                        LocalizationService.t('scan_qr_code'),
                        style: TextStyle(
                          color: AppTheme.primaryColor,
                          fontSize: AppTheme.fontSizeMedium,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: AppTheme.paddingLarge),
              // 服务器名称
              TextFormField(
                controller: _nameController,
                decoration: InputDecoration(
                  labelText: LocalizationService.t('enter_server_name'),
                  hintText: LocalizationService.t('server_name_example'),
                  errorText: _nameError,
                  suffixIcon: _nameController.text.isNotEmpty
                      ? IconButton(
                          onPressed: () {
                            setState(() {
                              _nameController.clear();
                              _nameError = null;
                            });
                          },
                          icon: const Icon(Icons.clear),
                          tooltip: LocalizationService.t('clear'),
                        )
                      : null,
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return LocalizationService.t('server_name_required');
                  }
                  return null;
                },
                onChanged: (value) {
                  setState(() {
                    // 如果有错误且现在有内容，清除错误
                    if (_nameError != null && value.trim().isNotEmpty) {
                      _nameError = null;
                    }
                  });
                },
                onFieldSubmitted: (value) => _validateServerName(value),
                onEditingComplete: () => _validateServerName(_nameController.text),
              ),
              const SizedBox(height: AppTheme.paddingMedium),
              // 服务器地址
              TextFormField(
                controller: _addressController,
                decoration: InputDecoration(
                  labelText: LocalizationService.t('server_address'),
                  hintText: LocalizationService.t('enter_server_address'),
                  errorText: _addressError,
                  suffixIcon: _addressController.text.isNotEmpty
                      ? IconButton(
                          onPressed: () {
                            setState(() {
                              _addressController.clear();
                              _addressError = null;
                            });
                          },
                          icon: const Icon(Icons.clear),
                          tooltip: LocalizationService.t('clear'),
                        )
                      : null,
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return LocalizationService.t('server_address_required');
                  }
                  return null;
                },
                onChanged: (value) {
                  setState(() {
                    // 如果有错误且现在有内容，清除错误
                    if (_addressError != null && value.trim().isNotEmpty) {
                      _addressError = null;
                    }
                  });
                },
                onFieldSubmitted: (value) => _validateServerAddress(value),
                onEditingComplete: () => _validateServerAddress(_addressController.text),
              ),
              const SizedBox(height: AppTheme.paddingMedium),
              // 服务器端口
              TextFormField(
                controller: _portController,
                decoration: InputDecoration(
                  labelText: LocalizationService.t('server_port'),
                  hintText: LocalizationService.t('enter_server_port'),
                  errorText: _portError,
                  suffixIcon: _portController.text.isNotEmpty
                      ? IconButton(
                          onPressed: () {
                            setState(() {
                              _portController.clear();
                              _portError = null;
                            });
                          },
                          icon: const Icon(Icons.clear),
                          tooltip: LocalizationService.t('clear'),
                        )
                      : null,
                ),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return LocalizationService.t('server_port_required');
                  }
                  return null;
                },
                onChanged: (value) {
                  setState(() {
                    // 如果有错误且现在有内容，清除错误
                    if (_portError != null && value.trim().isNotEmpty) {
                      _portError = null;
                    }
                  });
                },
                onFieldSubmitted: (value) => _validateServerPort(value),
                onEditingComplete: () => _validateServerPort(_portController.text),
              ),
              const SizedBox(height: AppTheme.paddingLarge),
              // 已保存的服务器配置按钮
              SizedBox(
                width: double.infinity,
                child: OutlinedButton.icon(
                  onPressed: _showSavedServers,
                  icon: const Icon(Icons.storage),
                  label: Text(LocalizationService.t('saved_servers')),
                ),
              ),
              const SizedBox(height: AppTheme.paddingLarge),
              // 测试连接按钮
              SizedBox(
                width: double.infinity,
                child: OutlinedButton(
                  onPressed: (_isLoading || _isTestingConnection) ? null : _testConnection,
                  child:
                      (_isLoading || _isTestingConnection)
                          ? const SizedBox(
                            height: 20,
                            width: 20,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                          : Text(LocalizationService.t('test_connection')),
                ),
              ),
              const SizedBox(height: AppTheme.paddingMedium),
              // 保存配置按钮
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _isLoading ? null : _saveServerConfig,
                  child: Text(LocalizationService.t('save_config')),
                ),
              ),
              ],
            ),
          ),
        ),
    );
  }

  @override
  void dispose() {
    _nameController.dispose();
    _addressController.dispose();
    _portController.dispose();
    _scrollController.dispose();
    super.dispose();
  }
}
