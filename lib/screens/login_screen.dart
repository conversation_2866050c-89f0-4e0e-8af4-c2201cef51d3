import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../constants/app_theme.dart';
import '../constants/app_constants.dart';
import '../screens/server_config_screen.dart';
import '../screens/main_screen.dart';
import '../services/api_service.dart';
import '../services/auth_service.dart';
import '../services/localization_service.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final _formKey = GlobalKey<FormState>();
  final _usernameController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _rememberPassword = false;
  bool _isLoading = false;
  String _selectedServerName = '';
  bool _useMockLogin = true; // 默认勾选模拟登录

  @override
  void initState() {
    super.initState();
    _checkExistingLogin();
    _loadSavedCredentials();
    _loadSelectedServer();
    _loadMockLoginSetting();
  }

  /// 检查是否已经登录
  Future<void> _checkExistingLogin() async {
    final authService = AuthService();
    final isLoggedIn = await authService.isLoggedIn();

    if (isLoggedIn && mounted) {
      // 如果已经登录，直接跳转到主页面
      Navigator.of(context).pushReplacement(
        MaterialPageRoute(builder: (context) => const MainScreen()),
      );
    }
  }

  Future<void> _loadSavedCredentials() async {
    final authService = AuthService();
    final credentials = await authService.getSavedCredentials();

    if (mounted) {
      setState(() {
        _usernameController.text = credentials['username'];
        _passwordController.text =
            credentials['rememberPassword'] ? credentials['password'] : '';
        _rememberPassword = credentials['rememberPassword'];
      });
    }
  }

  Future<void> _loadSelectedServer() async {
    final prefs = await SharedPreferences.getInstance();
    final serverAddress = prefs.getString(AppConstants.keyServerAddress) ?? '';
    final serverPort = prefs.getString(AppConstants.keyServerPort) ?? '';
    final serverName = prefs.getString('selected_server_name') ?? '';

    setState(() {
      if (serverName.isNotEmpty) {
        _selectedServerName = serverName;
      } else if (serverAddress.isNotEmpty) {
        _selectedServerName = '$serverAddress:$serverPort';
      }
    });
  }

  Future<void> _loadMockLoginSetting() async {
    final useMockData = await ApiService.getUseMockData();
    setState(() {
      _useMockLogin = useMockData;
    });
  }

  Future<void> _login() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // 根据勾选状态使用模拟或真实API登录
      final result = await ApiService.login(
        _usernameController.text,
        _passwordController.text,
        useMockData: _useMockLogin,
      );

      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        if (result['success']) {
          debugPrint('登录成功，开始保存登录信息');
          debugPrint('登录结果完整数据: $result');
          debugPrint('登录结果data字段: ${result['data']}');
          debugPrint('使用模拟登录: $_useMockLogin');

          // 检查必要的数据字段
          if (result['data'] == null) {
            debugPrint('错误：登录结果中data字段为空');
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('登录数据格式错误：data字段为空'),
                  backgroundColor: AppTheme.errorColor,
                ),
              );
            }
            return;
          }

          final token = result['data']['token'];
          final user = result['data']['user'];
          debugPrint('提取的token: $token');
          debugPrint('提取的user: $user');

          if (token == null || user == null) {
            debugPrint('错误：token或user字段为空');
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('登录数据格式错误：缺少token或user信息'),
                  backgroundColor: AppTheme.errorColor,
                ),
              );
            }
            return;
          }

          // 使用AuthService保存登录信息
          final authService = AuthService();

          // 在保存登录信息之前检查强制刷新标记
          final shouldForceRefreshBefore = await authService.shouldForceRefresh();
          debugPrint('保存登录信息前的强制刷新标记: $shouldForceRefreshBefore');

          final saveSuccess = await authService.saveLoginInfo(
            token: token,
            userInfo: jsonEncode(user),
            username: _usernameController.text,
            password: _passwordController.text,
            rememberPassword: _rememberPassword,
          );

          debugPrint('登录信息保存结果: $saveSuccess');

          if (saveSuccess && mounted) {
            // 登录成功后，再次检查是否需要强制刷新数据
            final shouldForceRefresh = await authService.shouldForceRefresh();
            debugPrint('保存登录信息后的强制刷新标记: $shouldForceRefresh');

            // 解决方案：如果是真实接口登录，确保设置强制刷新标记
            if (!_useMockLogin) {
              debugPrint('真实接口登录，确保设置强制刷新标记');
              await authService.setForceRefreshFlag(true);
              debugPrint('已为真实接口登录设置强制刷新标记');
            } else if (!shouldForceRefresh) {
              // 模拟登录时，只有在没有强制刷新标记时才设置
              debugPrint('模拟登录但未检测到强制刷新标记，检查是否需要设置');
            }

            if (shouldForceRefresh || !_useMockLogin) {
              debugPrint('检测到强制刷新标记或真实接口登录，将在主页面加载时强制刷新所有数据');
            } else {
              debugPrint('未检测到强制刷新标记，将使用正常缓存逻辑');
            }

            // 导航到主界面
            if (mounted) {
              Navigator.of(context).pushReplacement(
                MaterialPageRoute(builder: (context) => const MainScreen()),
              );
            }
          } else if (mounted) {
            // 保存失败，显示错误信息
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(LocalizationService.t('login_success')),
                backgroundColor: AppTheme.warningColor,
              ),
            );
            // 即使保存失败也跳转，但提示用户
            Navigator.of(context).pushReplacement(
              MaterialPageRoute(builder: (context) => const MainScreen()),
            );
          }
        } else {
          // 显示错误信息
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(result['message']),
              backgroundColor: AppTheme.errorColor,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(LocalizationService.t('login_network_error')),
            backgroundColor: AppTheme.errorColor,
          ),
        );
      }
    }
  }

  void _navigateToServerConfig() async {
    final result = await Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const ServerConfigScreen(clearFields: true),
      ),
    );

    // 如果服务器配置有更新，重新加载
    if (result == true) {
      _loadSelectedServer();
    }
  }

  void _navigateToServerConfigWithCurrent() async {
    final prefs = await SharedPreferences.getInstance();
    final serverAddress = prefs.getString(AppConstants.keyServerAddress) ?? '';
    final serverPort = prefs.getString(AppConstants.keyServerPort) ?? '';
    final serverName = prefs.getString('selected_server_name') ?? '';

    if (mounted) {
      final result = await Navigator.of(context).push(
        MaterialPageRoute(
          builder:
              (context) => ServerConfigScreen(
                prefilledName: serverName,
                prefilledAddress: serverAddress,
                prefilledPort: serverPort,
              ),
        ),
      );

      // 如果服务器配置有更新，重新加载
      if (result == true) {
        _loadSelectedServer();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.getBackgroundColor(context),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(AppTheme.paddingLarge),
          child: Form(
            key: _formKey,
            child: Column(
              children: [
                const SizedBox(height: 40),
                // Logo
                Container(
                  width: 120,
                  height: 120,
                  decoration: BoxDecoration(
                    color: AppTheme.primaryColor,
                    borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
                  ),
                  child: const Icon(
                    Icons.business,
                    size: 60,
                    color: AppTheme.white,
                  ),
                ),
                const SizedBox(height: AppTheme.paddingXLarge),
                // 应用名称
                Text(
                  LocalizationService.t('app_name'),
                  style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                    color: AppTheme.primaryColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: AppTheme.paddingXLarge),
                // 用户名输入框
                TextFormField(
                  controller: _usernameController,
                  decoration: InputDecoration(
                    labelText: LocalizationService.t('username'),
                    hintText: LocalizationService.t('enter_username'),
                    prefixIcon: const Icon(Icons.person),
                    suffixIcon:
                        _usernameController.text.isNotEmpty
                            ? IconButton(
                              onPressed: () {
                                setState(() {
                                  _usernameController.clear();
                                });
                              },
                              icon: const Icon(Icons.clear),
                            )
                            : null,
                  ),
                  onChanged: (value) {
                    setState(() {});
                  },
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return LocalizationService.t('username_required');
                    }
                    return null;
                  },
                ),
                const SizedBox(height: AppTheme.paddingMedium),
                // 密码输入框
                TextFormField(
                  controller: _passwordController,
                  obscureText: true,
                  decoration: InputDecoration(
                    labelText: LocalizationService.t('password'),
                    hintText: LocalizationService.t('enter_password'),
                    prefixIcon: const Icon(Icons.lock),
                    suffixIcon:
                        _passwordController.text.isNotEmpty
                            ? IconButton(
                              onPressed: () {
                                setState(() {
                                  _passwordController.clear();
                                });
                              },
                              icon: const Icon(Icons.clear),
                            )
                            : null,
                  ),
                  onChanged: (value) {
                    setState(() {});
                  },
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return LocalizationService.t('password_required');
                    }
                    return null;
                  },
                ),
                const SizedBox(height: AppTheme.paddingMedium),
                // 记住密码和服务器配置
                Row(
                  children: [
                    Checkbox(
                      value: _rememberPassword,
                      onChanged: (value) {
                        setState(() {
                          _rememberPassword = value ?? false;
                        });
                      },
                    ),
                    Text(LocalizationService.t('remember_password')),
                    const Spacer(),
                    GestureDetector(
                      onTap: _navigateToServerConfig,
                      child: Row(
                        children: [
                          Icon(
                            Icons.settings,
                            size: 16,
                            color: AppTheme.primaryColor,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            LocalizationService.t('server_config'),
                            style: TextStyle(
                              color: AppTheme.primaryColor,
                              fontSize: AppTheme.fontSizeMedium,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: AppTheme.paddingSmall),
                // 模拟登录选项
                Row(
                  children: [
                    Checkbox(
                      value: _useMockLogin,
                      onChanged: (value) async {
                        final newValue = value ?? true;
                        setState(() {
                          _useMockLogin = newValue;
                        });
                        // 同时更新全局设置
                        await ApiService.setUseMockData(newValue);
                      },
                    ),
                    Expanded(
                      child: Text(
                        LocalizationService.t('use_mock_login'),
                        style: const TextStyle(
                          fontSize: AppTheme.fontSizeSmall,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: AppTheme.paddingXLarge),
                // 登录按钮
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _login,
                    child:
                        _isLoading
                            ? const SizedBox(
                              height: 20,
                              width: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  AppTheme.white,
                                ),
                              ),
                            )
                            : Text(LocalizationService.t('login_button')),
                  ),
                ),
                const SizedBox(height: 50),
                // 服务器信息显示
                if (_selectedServerName.isNotEmpty)
                  GestureDetector(
                    onTap: _navigateToServerConfigWithCurrent,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: AppTheme.paddingMedium,
                        vertical: AppTheme.paddingSmall,
                      ),
                      margin: const EdgeInsets.only(
                        bottom: AppTheme.paddingMedium,
                      ),
                      decoration: BoxDecoration(
                        color: AppTheme.secondaryColor.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(
                          AppTheme.radiusSmall,
                        ),
                        border: Border.all(
                          color: AppTheme.secondaryColor,
                          width: 1,
                        ),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.dns,
                            size: 16,
                            color: AppTheme.secondaryColor,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            '${LocalizationService.t('current_server')}: $_selectedServerName',
                            style: TextStyle(
                              color: AppTheme.secondaryColor,
                              fontSize: AppTheme.fontSizeSmall,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          const SizedBox(width: 4),
                          Icon(
                            Icons.edit,
                            size: 12,
                            color: AppTheme.secondaryColor,
                          ),
                        ],
                      ),
                    ),
                  ),


                // 版权信息
                Text(
                  LocalizationService.t('copyright'),
                  style: Theme.of(context).textTheme.bodySmall,
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),
                Text(
                  '${LocalizationService.t('version_text')} ${AppConstants.appVersion}',
                  style: Theme.of(context).textTheme.bodySmall,
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _usernameController.dispose();
    _passwordController.dispose();
    super.dispose();
  }
}
