import 'package:flutter/material.dart';
import '../services/cache_service.dart';
import '../services/api_service.dart';
import '../services/localization_service.dart';
import '../constants/app_theme.dart';

class WorkspaceCacheDemoScreen extends StatefulWidget {
  const WorkspaceCacheDemoScreen({super.key});

  @override
  State<WorkspaceCacheDemoScreen> createState() => _WorkspaceCacheDemoScreenState();
}

class _WorkspaceCacheDemoScreenState extends State<WorkspaceCacheDemoScreen> {
  bool _hasCache = false;
  Map<String, dynamic>? _cacheData;
  bool _isLoading = false;
  String _lastAction = '';

  @override
  void initState() {
    super.initState();
    _checkCache();
  }

  Future<void> _checkCache() async {
    setState(() {
      _isLoading = true;
    });

    final hasCache = await CacheService.hasWorkspaceCache();
    final cacheData = await CacheService.getWorkspaceData();

    setState(() {
      _hasCache = hasCache;
      _cacheData = cacheData;
      _isLoading = false;
      _lastAction = '检查缓存状态';
    });
  }

  Future<void> _callApi() async {
    setState(() {
      _isLoading = true;
    });

    final result = await ApiService.getWorkspaceApps(useMockData: true);
    
    setState(() {
      _isLoading = false;
      _lastAction = 'API调用: ${result['success'] ? '成功' : '失败'}';
    });

    await _checkCache();
  }

  Future<void> _clearCache() async {
    setState(() {
      _isLoading = true;
    });

    await CacheService.clearWorkspaceCache();
    
    setState(() {
      _isLoading = false;
      _lastAction = '清除缓存';
    });

    await _checkCache();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('工作台缓存演示'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '缓存状态',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Icon(
                          _hasCache ? Icons.check_circle : Icons.cancel,
                          color: _hasCache ? Colors.green : Colors.red,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          _hasCache ? '有缓存数据' : '无缓存数据',
                          style: TextStyle(
                            color: _hasCache ? Colors.green : Colors.red,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    if (_lastAction.isNotEmpty) ...[
                      const SizedBox(height: 8),
                      Text(
                        '最后操作: $_lastAction',
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                    ],
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            if (_cacheData != null) ...[
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '缓存数据内容',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      const SizedBox(height: 8),
                      if (_cacheData!['frequentApps'] != null) ...[
                        Text(
                          '常用应用数量: ${(_cacheData!['frequentApps'] as List).length}',
                          style: Theme.of(context).textTheme.bodyMedium,
                        ),
                        const SizedBox(height: 8),
                        ...(_cacheData!['frequentApps'] as List).take(3).map((app) => 
                          Padding(
                            padding: const EdgeInsets.only(bottom: 4.0),
                            child: Text(
                              '• ${app['name'] ?? 'Unknown'}',
                              style: Theme.of(context).textTheme.bodySmall,
                            ),
                          ),
                        ),
                        if ((_cacheData!['frequentApps'] as List).length > 3)
                          Text(
                            '... 还有 ${(_cacheData!['frequentApps'] as List).length - 3} 个应用',
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              fontStyle: FontStyle.italic,
                            ),
                          ),
                      ],
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),
            ],
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '操作',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: _isLoading ? null : _checkCache,
                            icon: const Icon(Icons.refresh),
                            label: const Text('检查缓存'),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: _isLoading ? null : _callApi,
                            icon: const Icon(Icons.cloud_download),
                            label: const Text('调用API'),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton.icon(
                        onPressed: _isLoading ? null : _clearCache,
                        icon: const Icon(Icons.clear),
                        label: const Text('清除缓存'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.red,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            Card(
              color: Colors.blue.shade50,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.info, color: Colors.blue.shade700),
                        const SizedBox(width: 8),
                        Text(
                          '缓存机制说明',
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            color: Colors.blue.shade700,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '• 首次进入工作台页面时，如果有缓存数据会立即显示\n'
                      '• 如果没有缓存数据，会调用API获取数据并保存到缓存\n'
                      '• 下拉刷新时会重新调用API并更新缓存\n'
                      '• 缓存数据有效期为24小时，过期后会自动清除\n'
                      '• 这样可以避免每次进入页面都重新请求数据',
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                  ],
                ),
              ),
            ),
            if (_isLoading) ...[
              const SizedBox(height: 16),
              const Center(
                child: CircularProgressIndicator(),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
