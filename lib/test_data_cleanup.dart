import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'services/data_cleanup_service.dart';
import 'services/auth_service.dart';
import 'services/cache_service.dart';
import 'services/api_service.dart';

/// 数据清理功能测试页面
class DataCleanupTestScreen extends StatefulWidget {
  const DataCleanupTestScreen({super.key});

  @override
  State<DataCleanupTestScreen> createState() => _DataCleanupTestScreenState();
}

class _DataCleanupTestScreenState extends State<DataCleanupTestScreen> {
  Map<String, dynamic> _beforeStats = {};
  Map<String, dynamic> _afterStats = {};
  Map<String, dynamic> _retainedData = {};
  bool _isLoading = false;
  String _statusMessage = '';

  @override
  void initState() {
    super.initState();
    _loadInitialStats();
  }

  Future<void> _loadInitialStats() async {
    setState(() {
      _isLoading = true;
      _statusMessage = '正在加载初始数据统计...';
    });

    try {
      final cleanupService = DataCleanupService();
      final stats = await cleanupService.getClearedDataStats();
      final retained = await cleanupService.getRetainedDataInfo();

      setState(() {
        _beforeStats = stats;
        _retainedData = retained;
        _statusMessage = '初始数据统计加载完成';
      });
    } catch (e) {
      setState(() {
        _statusMessage = '加载初始数据失败: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _createTestData() async {
    setState(() {
      _isLoading = true;
      _statusMessage = '正在创建测试数据...';
    });

    try {
      final prefs = await SharedPreferences.getInstance();

      // 创建一些测试数据
      await prefs.setString('test_user_data', 'test_value');
      await prefs.setString('test_cache_data', 'cache_value');
      await prefs.setInt('test_timestamp', DateTime.now().millisecondsSinceEpoch);
      await prefs.setString('test_data_expiry', DateTime.now().add(const Duration(hours: 1)).millisecondsSinceEpoch.toString());

      // 创建一些缓存数据
      await CacheService.saveHomeData({'test': 'home_data'});
      await CacheService.saveAppsData({'test': 'apps_data'});
      await CacheService.saveWorkspaceData({'test': 'workspace_data'});
      await CacheService.saveMessagesData({'test': 'messages_data'});

      // 设置API模拟数据
      await ApiService.setUseMockData(true);

      setState(() {
        _statusMessage = '测试数据创建完成';
      });

      // 重新加载统计
      await _loadInitialStats();
    } catch (e) {
      setState(() {
        _statusMessage = '创建测试数据失败: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _performDataCleanup() async {
    setState(() {
      _isLoading = true;
      _statusMessage = '正在执行数据清理...';
    });

    try {
      final authService = AuthService();
      await authService.clearAllUserData();

      // 获取清理后的统计
      final cleanupService = DataCleanupService();
      final afterStats = await cleanupService.getClearedDataStats();
      final retainedAfter = await cleanupService.getRetainedDataInfo();

      setState(() {
        _afterStats = afterStats;
        _retainedData = retainedAfter;
        _statusMessage = '数据清理完成';
      });
    } catch (e) {
      setState(() {
        _statusMessage = '数据清理失败: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _checkSpecificData() async {
    setState(() {
      _isLoading = true;
      _statusMessage = '正在检查特定数据...';
    });

    try {
      final prefs = await SharedPreferences.getInstance();
      
      final results = <String, String>{};
      
      // 检查应该被清除的数据
      results['用户Token'] = prefs.getString('user_token') ?? '已清除';
      results['用户信息'] = prefs.getString('user_info') ?? '已清除';
      results['测试用户数据'] = prefs.getString('test_user_data') ?? '已清除';
      results['测试缓存数据'] = prefs.getString('test_cache_data') ?? '已清除';
      
      // 检查应该保留的数据
      results['用户名'] = prefs.getString('username') ?? '未设置';
      results['记住密码'] = prefs.getBool('remember_password')?.toString() ?? '未设置';
      results['主题设置'] = prefs.getString('selected_theme') ?? '未设置';
      results['语言设置'] = prefs.getString('selected_language') ?? '未设置';
      results['服务器地址'] = prefs.getString('server_address') ?? '未设置';

      // 检查缓存数据
      final homeCache = await CacheService.getHomeData();
      final appsCache = await CacheService.getAppsData();
      final workspaceCache = await CacheService.getWorkspaceData();
      final messagesCache = await CacheService.getMessagesData();

      results['首页缓存'] = homeCache != null ? '存在' : '已清除';
      results['应用缓存'] = appsCache != null ? '存在' : '已清除';
      results['工作台缓存'] = workspaceCache != null ? '存在' : '已清除';
      results['消息缓存'] = messagesCache != null ? '存在' : '已清除';

      // 显示结果
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('数据检查结果'),
          content: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: results.entries.map((entry) => 
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 2),
                  child: Text('${entry.key}: ${entry.value}'),
                )
              ).toList(),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('确定'),
            ),
          ],
        ),
      );

      setState(() {
        _statusMessage = '数据检查完成';
      });
    } catch (e) {
      setState(() {
        _statusMessage = '数据检查失败: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('数据清理功能测试'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // 状态信息
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('状态信息', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
                    const SizedBox(height: 8),
                    Text(_statusMessage),
                    if (_isLoading) 
                      const Padding(
                        padding: EdgeInsets.only(top: 8),
                        child: LinearProgressIndicator(),
                      ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // 操作按钮
            ElevatedButton(
              onPressed: _isLoading ? null : _createTestData,
              child: const Text('创建测试数据'),
            ),
            
            const SizedBox(height: 8),
            
            ElevatedButton(
              onPressed: _isLoading ? null : _performDataCleanup,
              style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
              child: const Text('执行数据清理', style: TextStyle(color: Colors.white)),
            ),
            
            const SizedBox(height: 8),
            
            ElevatedButton(
              onPressed: _isLoading ? null : _checkSpecificData,
              child: const Text('检查数据状态'),
            ),
            
            const SizedBox(height: 8),
            
            ElevatedButton(
              onPressed: _isLoading ? null : _loadInitialStats,
              child: const Text('刷新统计'),
            ),
            
            const SizedBox(height: 16),
            
            // 统计信息
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    if (_beforeStats.isNotEmpty) ...[
                      Card(
                        child: Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text('数据统计', style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
                              const SizedBox(height: 8),
                              ..._beforeStats.entries.map((entry) => 
                                Text('${entry.key}: ${entry.value}')
                              ),
                            ],
                          ),
                        ),
                      ),
                      const SizedBox(height: 8),
                    ],
                    
                    if (_retainedData.isNotEmpty) ...[
                      Card(
                        child: Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text('保留的数据', style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
                              const SizedBox(height: 8),
                              ..._retainedData.entries.map((entry) => 
                                Text('${entry.key}: ${entry.value}')
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
