import 'package:flutter/material.dart';
import 'services/api_service.dart';
import 'services/localization_service.dart';

class TestApiCallsScreen extends StatefulWidget {
  const TestApiCallsScreen({super.key});

  @override
  State<TestApiCallsScreen> createState() => _TestApiCallsScreenState();
}

class _TestApiCallsScreenState extends State<TestApiCallsScreen> {
  String _appListResult = '未调用';
  String _workspaceResult = '未调用';
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('API调用测试'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            ElevatedButton(
              onPressed: _isLoading ? null : _testAppListApi,
              child: const Text('测试应用列表API'),
            ),
            const SizedBox(height: 16),
            Text('应用列表API结果: $_appListResult'),
            const SizedBox(height: 32),
            ElevatedButton(
              onPressed: _isLoading ? null : _testWorkspaceApi,
              child: const Text('测试工作台API'),
            ),
            const SizedBox(height: 16),
            Text('工作台API结果: $_workspaceResult'),
            const SizedBox(height: 32),
            ElevatedButton(
              onPressed: _isLoading ? null : _testBothApis,
              child: const Text('测试所有API'),
            ),
            const SizedBox(height: 16),
            if (_isLoading) const CircularProgressIndicator(),
          ],
        ),
      ),
    );
  }

  Future<void> _testAppListApi() async {
    setState(() {
      _isLoading = true;
      _appListResult = '调用中...';
    });

    try {
      final result = await ApiService.getAppList(forceRefresh: true);
      setState(() {
        _appListResult = '成功: ${result['success']}, 消息: ${result['message']}${result['fromCache'] == true ? ' (缓存)' : ''}';
      });
    } catch (e) {
      setState(() {
        _appListResult = '错误: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testWorkspaceApi() async {
    setState(() {
      _isLoading = true;
      _workspaceResult = '调用中...';
    });

    try {
      final result = await ApiService.getWorkspaceApps(forceRefresh: true);
      setState(() {
        _workspaceResult = '成功: ${result['success']}, 消息: ${result['message']}${result['fromCache'] == true ? ' (缓存)' : ''}';
      });
    } catch (e) {
      setState(() {
        _workspaceResult = '错误: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testBothApis() async {
    await _testAppListApi();
    await _testWorkspaceApi();
  }
}
