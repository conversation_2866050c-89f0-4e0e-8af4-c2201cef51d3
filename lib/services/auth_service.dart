import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../constants/app_constants.dart';
import 'data_cleanup_service.dart';
import 'localization_service.dart';

class AuthService {
  static const String _tokenKey = 'user_token';
  static const String _userInfoKey = 'user_info';
  static const String _loginTimeKey = 'login_time';
  static const String _sessionValidKey = 'session_valid';
  static const String _forceRefreshKey = 'force_refresh_on_login';

  // 单例模式
  static final AuthService _instance = AuthService._internal();
  factory AuthService() => _instance;
  AuthService._internal();

  // 内存中的状态缓存，防止频繁读取SharedPreferences
  String? _cachedToken;
  String? _cachedUserInfo;
  bool? _cachedSessionValid;
  DateTime? _cachedLoginTime;

  // 强制刷新标记，用于重新登录时强制刷新数据
  bool _shouldForceRefresh = false;
  
  /// 保存登录信息
  Future<bool> saveLoginInfo({
    required String token,
    required String userInfo,
    required String username,
    required String password,
    required bool rememberPassword,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final loginTime = DateTime.now();
      
      // 保存登录信息
      await prefs.setString(_tokenKey, token);
      await prefs.setString(_userInfoKey, userInfo);
      await prefs.setString(_loginTimeKey, loginTime.toIso8601String());
      await prefs.setBool(_sessionValidKey, true);
      
      // 保存用户凭据
      await prefs.setString(AppConstants.keyUsername, username);
      if (rememberPassword) {
        await prefs.setString(AppConstants.keyPassword, password);
        await prefs.setBool(AppConstants.keyRememberPassword, true);
      } else {
        await prefs.remove(AppConstants.keyPassword);
        await prefs.setBool(AppConstants.keyRememberPassword, false);
      }
      
      // 更新缓存
      _cachedToken = token;
      _cachedUserInfo = userInfo;
      _cachedSessionValid = true;
      _cachedLoginTime = loginTime;
      
      // 在Android模拟器中，额外进行一次验证写入
      if (defaultTargetPlatform == TargetPlatform.android) {
        await _verifyAndroidPersistence(prefs);
      }
      
      return true;
    } catch (e) {
      debugPrint('保存登录信息失败: $e');
      return false;
    }
  }
  
  /// Android模拟器持久化验证
  Future<void> _verifyAndroidPersistence(SharedPreferences prefs) async {
    try {
      // 等待一小段时间确保数据写入完成
      await Future.delayed(const Duration(milliseconds: 100));

      // 验证数据是否正确保存
      final savedToken = prefs.getString(_tokenKey);
      final savedUserInfo = prefs.getString(_userInfoKey);
      final savedSessionValid = prefs.getBool(_sessionValidKey);

      if (savedToken == null || savedUserInfo == null || savedSessionValid != true) {
        debugPrint('Android持久化验证失败，尝试重新保存');

        // 重新保存
        await prefs.setString(_tokenKey, _cachedToken ?? '');
        await prefs.setString(_userInfoKey, _cachedUserInfo ?? '');
        await prefs.setBool(_sessionValidKey, true);
        // 再次等待确保写入完成
        await Future.delayed(const Duration(milliseconds: 100));
      }
    } catch (e) {
      debugPrint('Android持久化验证异常: $e');
    }
  }
  
  /// 检查登录状态
  Future<bool> isLoggedIn() async {
    try {
      // 首先检查缓存
      if (_cachedSessionValid == true && _cachedToken != null) {
        return true;
      }
      
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString(_tokenKey);
      final sessionValid = prefs.getBool(_sessionValidKey) ?? false;
      final loginTimeStr = prefs.getString(_loginTimeKey);
      
      if (token == null || !sessionValid || loginTimeStr == null) {
        return false;
      }
      
      // 检查登录时间是否过期（7天）
      final loginTime = DateTime.parse(loginTimeStr);
      final now = DateTime.now();
      final daysDiff = now.difference(loginTime).inDays;
      
      if (daysDiff > 7) {
        await clearLoginInfo();
        return false;
      }
      
      // 更新缓存
      _cachedToken = token;
      _cachedUserInfo = prefs.getString(_userInfoKey);
      _cachedSessionValid = sessionValid;
      _cachedLoginTime = loginTime;
      
      return true;
    } catch (e) {
      debugPrint('${LocalizationService.t('debug_check_login_status_failed')}: $e');
      return false;
    }
  }
  
  /// 获取用户Token
  Future<String?> getUserToken() async {
    if (_cachedToken != null) {
      return _cachedToken;
    }
    
    final prefs = await SharedPreferences.getInstance();
    final token = prefs.getString(_tokenKey);
    _cachedToken = token;
    return token;
  }
  
  /// 获取用户信息
  Future<String?> getUserInfo() async {
    if (_cachedUserInfo != null) {
      return _cachedUserInfo;
    }
    
    final prefs = await SharedPreferences.getInstance();
    final userInfo = prefs.getString(_userInfoKey);
    _cachedUserInfo = userInfo;
    return userInfo;
  }
  
  /// 获取保存的用户凭据
  Future<Map<String, dynamic>> getSavedCredentials() async {
    final prefs = await SharedPreferences.getInstance();
    return {
      'username': prefs.getString(AppConstants.keyUsername) ?? '',
      'password': prefs.getString(AppConstants.keyPassword) ?? '',
      'rememberPassword': prefs.getBool(AppConstants.keyRememberPassword) ?? false,
    };
  }
  
  /// 清除登录信息（仅清除认证相关数据，保留登录页面信息）
  Future<void> clearLoginInfo() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // 清除登录相关信息
      await prefs.remove(_tokenKey);
      await prefs.remove(_userInfoKey);
      await prefs.remove(_loginTimeKey);
      await prefs.setBool(_sessionValidKey, false);

      // 清除缓存
      _cachedToken = null;
      _cachedUserInfo = null;
      _cachedSessionValid = false;
      _cachedLoginTime = null;

      // 在Android平台等待一小段时间确保数据写入完成
      if (defaultTargetPlatform == TargetPlatform.android) {
        await Future.delayed(const Duration(milliseconds: 100));
      }
    } catch (e) {
      debugPrint('${LocalizationService.t('debug_clear_login_info_failed')}: $e');
    }
  }

  /// 完全清除所有用户数据（退出登录时使用，保留登录页面信息）
  Future<void> clearAllUserData() async {
    try {
      // 使用专门的数据清理服务
      final cleanupService = DataCleanupService();
      await cleanupService.clearAllUserData();

      // 清除内存缓存
      _cachedToken = null;
      _cachedUserInfo = null;
      _cachedSessionValid = false;
      _cachedLoginTime = null;

      // 设置强制刷新标记，确保下次登录时强制刷新数据
      await setForceRefreshFlag(true);

      // 在Android平台等待一小段时间确保数据写入完成
      if (defaultTargetPlatform == TargetPlatform.android) {
        await Future.delayed(const Duration(milliseconds: 200));
      }

      debugPrint(LocalizationService.t('debug_all_user_data_cleared'));
    } catch (e) {
      debugPrint('${LocalizationService.t('debug_clear_user_data_failed')}: $e');
      rethrow;
    }
  }

  /// 设置强制刷新标记
  Future<void> setForceRefreshFlag(bool value) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_forceRefreshKey, value);
      _shouldForceRefresh = value;
      debugPrint('强制刷新标记已设置为: $value');
    } catch (e) {
      debugPrint('设置强制刷新标记失败: $e');
    }
  }

  /// 检查是否需要强制刷新
  Future<bool> shouldForceRefresh() async {
    try {
      // 首先检查内存缓存
      if (_shouldForceRefresh) {
        return true;
      }

      final prefs = await SharedPreferences.getInstance();
      final forceRefresh = prefs.getBool(_forceRefreshKey) ?? false;
      _shouldForceRefresh = forceRefresh;
      return forceRefresh;
    } catch (e) {
      debugPrint('检查强制刷新标记失败: $e');
      return false;
    }
  }

  /// 清除强制刷新标记（在数据刷新完成后调用）
  Future<void> clearForceRefreshFlag() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_forceRefreshKey);
      _shouldForceRefresh = false;
      debugPrint(LocalizationService.t('debug_force_refresh_flag_cleared'));
    } catch (e) {
      debugPrint('${LocalizationService.t('debug_clear_force_refresh_failed')}: $e');
    }
  }
  
  /// 更新会话有效性
  Future<void> updateSessionValidity(bool isValid) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_sessionValidKey, isValid);
      _cachedSessionValid = isValid;
      
      if (defaultTargetPlatform == TargetPlatform.android) {
        await Future.delayed(const Duration(milliseconds: 100));
      }
    } catch (e) {
      debugPrint('${LocalizationService.t('debug_update_session_failed')}: $e');
    }
  }
  
  /// 刷新登录时间
  Future<void> refreshLoginTime() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final newLoginTime = DateTime.now();
      await prefs.setString(_loginTimeKey, newLoginTime.toIso8601String());
      _cachedLoginTime = newLoginTime;
      
      if (defaultTargetPlatform == TargetPlatform.android) {
        await Future.delayed(const Duration(milliseconds: 100));
      }
    } catch (e) {
      debugPrint('刷新登录时间失败: $e');
    }
  }
}
