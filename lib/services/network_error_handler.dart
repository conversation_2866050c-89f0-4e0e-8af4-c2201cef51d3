import 'dart:async';
import 'dart:io';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'network_status_service.dart';
import 'localization_service.dart';

/// 网络错误类型
enum NetworkErrorType {
  noConnection,      // 无网络连接
  timeout,          // 请求超时
  serverError,      // 服务器错误 (5xx)
  clientError,      // 客户端错误 (4xx)
  rateLimited,      // 请求频率限制
  tokenExpired,     // Token过期
  unknown,          // 未知错误
}

/// 重试策略
enum RetryStrategy {
  none,             // 不重试
  immediate,        // 立即重试
  exponential,      // 指数退避
  linear,           // 线性退避
  custom,           // 自定义策略
}

/// 网络错误信息
class NetworkError {
  final NetworkErrorType type;
  final String message;
  final int? statusCode;
  final DateTime timestamp;
  final String? requestUrl;
  final Map<String, dynamic>? details;

  const NetworkError({
    required this.type,
    required this.message,
    this.statusCode,
    required this.timestamp,
    this.requestUrl,
    this.details,
  });

  @override
  String toString() {
    return 'NetworkError(type: $type, message: $message, statusCode: $statusCode, url: $requestUrl)';
  }
}

/// Token过期异常
class TokenExpiredException implements Exception {
  final String message;
  final int? statusCode;
  final Uri? uri;

  const TokenExpiredException(
    this.message, {
    this.statusCode,
    this.uri,
  });

  @override
  String toString() {
    return 'TokenExpiredException: $message (statusCode: $statusCode)';
  }
}

/// 重试配置
class RetryConfig {
  final int maxRetries;
  final Duration initialDelay;
  final Duration maxDelay;
  final double backoffMultiplier;
  final RetryStrategy strategy;
  final List<NetworkErrorType> retryableErrors;

  const RetryConfig({
    this.maxRetries = 3,
    this.initialDelay = const Duration(seconds: 1),
    this.maxDelay = const Duration(seconds: 30),
    this.backoffMultiplier = 2.0,
    this.strategy = RetryStrategy.exponential,
    this.retryableErrors = const [
      NetworkErrorType.noConnection,
      NetworkErrorType.timeout,
      NetworkErrorType.serverError,
    ],
  });

  /// 离线优先的重试配置
  static const RetryConfig offlineFirst = RetryConfig(
    maxRetries: 2,
    initialDelay: Duration(seconds: 2),
    maxDelay: Duration(seconds: 10),
    strategy: RetryStrategy.linear,
    retryableErrors: [
      NetworkErrorType.noConnection,
      NetworkErrorType.timeout,
    ],
  );

  /// 实时数据的重试配置
  static const RetryConfig realtime = RetryConfig(
    maxRetries: 5,
    initialDelay: Duration(milliseconds: 500),
    maxDelay: Duration(seconds: 5),
    strategy: RetryStrategy.exponential,
    retryableErrors: [
      NetworkErrorType.noConnection,
      NetworkErrorType.timeout,
      NetworkErrorType.serverError,
    ],
  );
}

/// 请求去重管理器
class RequestDeduplicator {
  final Map<String, Completer<dynamic>> _pendingRequests = {};
  final Map<String, DateTime> _lastRequestTime = {};
  final Duration _deduplicationWindow = const Duration(seconds: 3);

  /// 检查是否应该去重请求
  bool shouldDeduplicate(String requestKey) {
    final lastTime = _lastRequestTime[requestKey];
    if (lastTime != null) {
      final timeDiff = DateTime.now().difference(lastTime);
      return timeDiff < _deduplicationWindow;
    }
    return false;
  }

  /// 获取或创建请求
  Future<T> getOrCreateRequest<T>(
    String requestKey,
    Future<T> Function() requestFunction,
  ) async {
    // 如果有相同的请求正在进行，等待其完成
    if (_pendingRequests.containsKey(requestKey)) {
      return await _pendingRequests[requestKey]!.future as T;
    }

    // 创建新的请求
    final completer = Completer<T>();
    _pendingRequests[requestKey] = completer;
    _lastRequestTime[requestKey] = DateTime.now();

    try {
      final result = await requestFunction();
      completer.complete(result);
      return result;
    } catch (error) {
      completer.completeError(error);
      rethrow;
    } finally {
      _pendingRequests.remove(requestKey);
    }
  }

  /// 清理过期的请求记录
  void cleanup() {
    final now = DateTime.now();
    _lastRequestTime.removeWhere((key, time) {
      return now.difference(time) > _deduplicationWindow;
    });
  }
}

/// 网络错误处理器
class NetworkErrorHandler {
  static final NetworkErrorHandler _instance = NetworkErrorHandler._internal();
  factory NetworkErrorHandler() => _instance;
  NetworkErrorHandler._internal();

  final NetworkStatusService _networkStatus = NetworkStatusService();
  final RequestDeduplicator _deduplicator = RequestDeduplicator();
  
  // 错误统计
  final Map<String, int> _errorCounts = {};
  final Map<String, DateTime> _lastErrorTime = {};
  
  // 清理定时器
  Timer? _cleanupTimer;

  /// 初始化错误处理器
  void initialize() {
    debugPrint('NetworkErrorHandler: 初始化网络错误处理器');
    
    // 启动定期清理
    _cleanupTimer = Timer.periodic(
      const Duration(minutes: 5),
      (_) => _cleanup(),
    );
  }

  /// 分析错误类型
  NetworkError analyzeError(dynamic error, {String? requestUrl}) {
    final timestamp = DateTime.now();

    if (error is TokenExpiredException) {
      return NetworkError(
        type: NetworkErrorType.tokenExpired,
        message: LocalizationService.t('token_expired'),
        statusCode: error.statusCode,
        timestamp: timestamp,
        requestUrl: requestUrl,
        details: {'originalError': error.toString()},
      );
    }

    if (error is SocketException) {
      return NetworkError(
        type: NetworkErrorType.noConnection,
        message: LocalizationService.t('network_connection_error'),
        timestamp: timestamp,
        requestUrl: requestUrl,
        details: {'originalError': error.toString()},
      );
    }

    if (error is TimeoutException) {
      return NetworkError(
        type: NetworkErrorType.timeout,
        message: LocalizationService.t('request_timeout'),
        timestamp: timestamp,
        requestUrl: requestUrl,
        details: {'originalError': error.toString()},
      );
    }
    
    if (error is HttpException) {
      final statusCode = _extractStatusCode(error.message);
      NetworkErrorType type;
      String message;
      
      if (statusCode != null) {
        if (statusCode >= 500) {
          type = NetworkErrorType.serverError;
          message = LocalizationService.t('server_error_msg');
        } else if (statusCode == 429) {
          type = NetworkErrorType.rateLimited;
          message = LocalizationService.t('request_too_frequent');
        } else if (statusCode >= 400) {
          type = NetworkErrorType.clientError;
          message = LocalizationService.t('client_error');
        } else {
          type = NetworkErrorType.unknown;
          message = LocalizationService.t('unknown_error');
        }
      } else {
        type = NetworkErrorType.unknown;
        message = LocalizationService.t('network_request_failed');
      }
      
      return NetworkError(
        type: type,
        message: message,
        statusCode: statusCode,
        timestamp: timestamp,
        requestUrl: requestUrl,
        details: {'originalError': error.toString()},
      );
    }
    
    return NetworkError(
      type: NetworkErrorType.unknown,
      message: error.toString(),
      timestamp: timestamp,
      requestUrl: requestUrl,
      details: {'originalError': error.toString()},
    );
  }

  /// 执行带重试的网络请求
  Future<T> executeWithRetry<T>(
    Future<T> Function() requestFunction, {
    String? requestKey,
    RetryConfig config = const RetryConfig(),
    bool useCache = true,
  }) async {
    // 检查网络状态
    if (!_networkStatus.isConnected && config.strategy != RetryStrategy.none) {
      throw NetworkError(
        type: NetworkErrorType.noConnection,
        message: LocalizationService.t('network_connection_error'),
        timestamp: DateTime.now(),
      );
    }

    // 请求去重
    if (requestKey != null) {
      if (_deduplicator.shouldDeduplicate(requestKey)) {
        debugPrint('NetworkErrorHandler: 请求去重 - $requestKey');
        throw NetworkError(
          type: NetworkErrorType.rateLimited,
          message: LocalizationService.t('request_too_frequent_retry'),
          timestamp: DateTime.now(),
        );
      }

      return await _deduplicator.getOrCreateRequest(requestKey, () async {
        return await _executeWithRetryInternal(requestFunction, config);
      });
    }

    return await _executeWithRetryInternal(requestFunction, config);
  }

  /// 内部重试执行逻辑
  Future<T> _executeWithRetryInternal<T>(
    Future<T> Function() requestFunction,
    RetryConfig config,
  ) async {
    int attemptCount = 0;
    NetworkError? lastError;

    while (attemptCount <= config.maxRetries) {
      try {
        final result = await requestFunction();
        
        // 请求成功，清除错误计数
        if (lastError?.requestUrl != null) {
          _errorCounts.remove(lastError!.requestUrl);
          _lastErrorTime.remove(lastError!.requestUrl);
        }
        
        return result;
      } catch (error) {
        attemptCount++;
        lastError = analyzeError(error);
        
        // 记录错误统计
        _recordError(lastError);
        
        debugPrint('NetworkErrorHandler: 请求失败 (尝试 $attemptCount/${config.maxRetries + 1}) - $lastError');
        
        // 检查是否应该重试
        if (attemptCount > config.maxRetries || !_shouldRetry(lastError, config)) {
          break;
        }
        
        // 检查网络状态
        if (!_networkStatus.isConnected) {
          debugPrint('NetworkErrorHandler: 网络已断开，停止重试');
          break;
        }
        
        // 计算延迟时间并等待
        final delay = _calculateDelay(attemptCount, config);
        debugPrint('NetworkErrorHandler: 等待 ${delay.inMilliseconds}ms 后重试');
        await Future.delayed(delay);
      }
    }

    // 所有重试都失败了，抛出最后一个错误
    throw lastError!;
  }

  /// 判断是否应该重试
  bool _shouldRetry(NetworkError error, RetryConfig config) {
    return config.retryableErrors.contains(error.type);
  }

  /// 计算重试延迟时间
  Duration _calculateDelay(int attemptCount, RetryConfig config) {
    switch (config.strategy) {
      case RetryStrategy.none:
        return Duration.zero;
      
      case RetryStrategy.immediate:
        return Duration.zero;
      
      case RetryStrategy.linear:
        final delay = config.initialDelay * attemptCount;
        return delay > config.maxDelay ? config.maxDelay : delay;
      
      case RetryStrategy.exponential:
        final delay = Duration(
          milliseconds: (config.initialDelay.inMilliseconds * 
                        pow(config.backoffMultiplier, attemptCount - 1)).round(),
        );
        return delay > config.maxDelay ? config.maxDelay : delay;
      
      case RetryStrategy.custom:
        // 自定义策略，可以根据需要实现
        return config.initialDelay;
    }
  }

  /// 记录错误统计
  void _recordError(NetworkError error) {
    if (error.requestUrl != null) {
      _errorCounts[error.requestUrl!] = (_errorCounts[error.requestUrl!] ?? 0) + 1;
      _lastErrorTime[error.requestUrl!] = error.timestamp;
    }
  }

  /// 获取错误统计
  Map<String, int> getErrorStats() {
    return Map.from(_errorCounts);
  }

  /// 清理过期数据
  void _cleanup() {
    _deduplicator.cleanup();
    
    final now = DateTime.now();
    _lastErrorTime.removeWhere((url, time) {
      final shouldRemove = now.difference(time) > const Duration(hours: 1);
      if (shouldRemove) {
        _errorCounts.remove(url);
      }
      return shouldRemove;
    });
  }

  /// 提取HTTP状态码
  int? _extractStatusCode(String message) {
    final regex = RegExp(r'(\d{3})');
    final match = regex.firstMatch(message);
    return match != null ? int.tryParse(match.group(1)!) : null;
  }

  /// 销毁错误处理器
  void dispose() {
    debugPrint('NetworkErrorHandler: 销毁网络错误处理器');
    _cleanupTimer?.cancel();
    _errorCounts.clear();
    _lastErrorTime.clear();
  }
}
