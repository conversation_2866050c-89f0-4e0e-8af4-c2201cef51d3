import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'localization_service.dart';

/// 错误日志条目
class ErrorLogEntry {
  final String id;
  final String type;
  final String message;
  final String? stackTrace;
  final DateTime timestamp;
  final Map<String, dynamic>? additionalInfo;

  ErrorLogEntry({
    required this.id,
    required this.type,
    required this.message,
    this.stackTrace,
    required this.timestamp,
    this.additionalInfo,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type,
      'message': message,
      'stackTrace': stackTrace,
      'timestamp': timestamp.toIso8601String(),
      'additionalInfo': additionalInfo,
    };
  }

  factory ErrorLogEntry.fromJson(Map<String, dynamic> json) {
    return ErrorLogEntry(
      id: json['id'],
      type: json['type'],
      message: json['message'],
      stackTrace: json['stackTrace'],
      timestamp: DateTime.parse(json['timestamp']),
      additionalInfo: json['additionalInfo'],
    );
  }

  @override
  String toString() {
    final buffer = StringBuffer();
    buffer.writeln('错误类型: $type');
    buffer.writeln('错误信息: $message');
    buffer.writeln('发生时间: ${timestamp.toString()}');
    if (stackTrace != null) {
      buffer.writeln('堆栈跟踪:');
      buffer.writeln(stackTrace);
    }
    if (additionalInfo != null && additionalInfo!.isNotEmpty) {
      buffer.writeln('附加信息:');
      additionalInfo!.forEach((key, value) {
        buffer.writeln('  $key: $value');
      });
    }
    return buffer.toString();
  }
}

/// 错误日志服务
class ErrorLogService {
  static const String _storageKey = 'error_logs';
  static const int _maxLogEntries = 1000; // 最大保存1000条日志
  static const int _maxLogAge = 30; // 最大保存30天

  static final List<ErrorLogEntry> _logs = [];
  static bool _isInitialized = false;

  /// 初始化服务
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await _loadLogs();
      await _cleanupOldLogs();
      _isInitialized = true;
      debugPrint('ErrorLogService: ${LocalizationService.t('debug_error_service_init')}: ${_logs.length}');
    } catch (e) {
      debugPrint('ErrorLogService: ${LocalizationService.t('debug_error_service_init_failed')} - $e');
    }
  }

  /// 记录错误
  static Future<void> logError({
    required String type,
    required String message,
    String? stackTrace,
    Map<String, dynamic>? additionalInfo,
  }) async {
    try {
      final entry = ErrorLogEntry(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        type: type,
        message: message,
        stackTrace: stackTrace,
        timestamp: DateTime.now(),
        additionalInfo: additionalInfo,
      );

      _logs.insert(0, entry); // 插入到列表开头，最新的在前面

      // 限制日志数量
      if (_logs.length > _maxLogEntries) {
        _logs.removeRange(_maxLogEntries, _logs.length);
      }

      await _saveLogs();
      debugPrint('ErrorLogService: ${LocalizationService.t('debug_error_logged')} - $type: $message');
    } catch (e) {
      debugPrint('ErrorLogService: ${LocalizationService.t('debug_error_log_failed')} - $e');
    }
  }

  /// 记录Flutter错误
  static Future<void> logFlutterError(FlutterErrorDetails details) async {
    await logError(
      type: 'flutter_error',
      message: details.exception.toString(),
      stackTrace: details.stack?.toString(),
      additionalInfo: {
        'library': details.library,
        'context': details.context?.toString(),
        'informationCollector': details.informationCollector?.toString(),
      },
    );
  }

  /// 记录Dart错误
  static Future<void> logDartError(Object error, StackTrace? stackTrace) async {
    await logError(
      type: 'dart_error',
      message: error.toString(),
      stackTrace: stackTrace?.toString(),
    );
  }

  /// 记录网络错误
  static Future<void> logNetworkError({
    required String message,
    String? url,
    int? statusCode,
    String? stackTrace,
  }) async {
    await logError(
      type: 'log_network_error',
      message: message,
      stackTrace: stackTrace,
      additionalInfo: {
        'url': url,
        'statusCode': statusCode,
      },
    );
  }

  /// 记录应用错误
  static Future<void> logAppError({
    required String message,
    String? stackTrace,
    Map<String, dynamic>? additionalInfo,
  }) async {
    await logError(
      type: 'app_error',
      message: message,
      stackTrace: stackTrace,
      additionalInfo: additionalInfo,
    );
  }

  /// 获取所有日志
  static List<ErrorLogEntry> getAllLogs() {
    return List.unmodifiable(_logs);
  }

  /// 获取指定类型的日志
  static List<ErrorLogEntry> getLogsByType(String type) {
    return _logs.where((log) => log.type == type).toList();
  }

  /// 获取日志数量
  static int getLogCount() {
    return _logs.length;
  }

  /// 获取各类型错误数量统计
  static Map<String, int> getErrorStats() {
    final stats = <String, int>{};
    for (final log in _logs) {
      stats[log.type] = (stats[log.type] ?? 0) + 1;
    }
    return stats;
  }

  /// 清除所有日志
  static Future<void> clearAllLogs() async {
    try {
      _logs.clear();
      await _saveLogs();
      debugPrint('ErrorLogService: ${LocalizationService.t('debug_all_logs_cleared')}');
    } catch (e) {
      debugPrint('ErrorLogService: ${LocalizationService.t('debug_clear_logs_failed')} - $e');
    }
  }

  /// 清除指定类型的日志
  static Future<void> clearLogsByType(String type) async {
    try {
      _logs.removeWhere((log) => log.type == type);
      await _saveLogs();
      debugPrint('ErrorLogService: ${LocalizationService.t('debug_logs_cleared_by_type')} $type ${LocalizationService.t('debug_cleanup_old_logs_suffix')}');
    } catch (e) {
      debugPrint('ErrorLogService: ${LocalizationService.t('debug_clear_logs_by_type_failed')} - $e');
    }
  }

  /// 导出日志为字符串
  static String exportLogsAsString() {
    return exportLogsListAsString(_logs);
  }

  /// 导出指定日志列表为字符串
  static String exportLogsListAsString(List<ErrorLogEntry> logs, {String? filterType}) {
    final buffer = StringBuffer();
    buffer.writeln('=== 错误日志导出 ===');
    buffer.writeln('导出时间: ${DateTime.now()}');

    if (filterType != null) {
      buffer.writeln('过滤类型: $filterType');
    }

    buffer.writeln('日志数量: ${logs.length}');
    buffer.writeln('');

    for (int i = 0; i < logs.length; i++) {
      buffer.writeln('--- 日志 ${i + 1} ---');
      buffer.writeln(logs[i].toString());
      buffer.writeln('');
    }

    return buffer.toString();
  }

  /// 从存储加载日志
  static Future<void> _loadLogs() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final logsJson = prefs.getString(_storageKey);
      
      if (logsJson != null) {
        final List<dynamic> logsList = jsonDecode(logsJson);
        _logs.clear();
        _logs.addAll(logsList.map((json) => ErrorLogEntry.fromJson(json)));
      }
    } catch (e) {
      debugPrint('ErrorLogService: ${LocalizationService.t('debug_load_logs_failed')} - $e');
    }
  }

  /// 保存日志到存储
  static Future<void> _saveLogs() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final logsJson = jsonEncode(_logs.map((log) => log.toJson()).toList());
      await prefs.setString(_storageKey, logsJson);
    } catch (e) {
      debugPrint('ErrorLogService: ${LocalizationService.t('debug_save_logs_failed')} - $e');
    }
  }

  /// 清理过期日志
  static Future<void> _cleanupOldLogs() async {
    try {
      final cutoffDate = DateTime.now().subtract(Duration(days: _maxLogAge));
      final initialCount = _logs.length;
      
      _logs.removeWhere((log) => log.timestamp.isBefore(cutoffDate));
      
      if (_logs.length < initialCount) {
        await _saveLogs();
        debugPrint('ErrorLogService: ${LocalizationService.t('debug_cleanup_old_logs')} ${initialCount - _logs.length} ${LocalizationService.t('debug_cleanup_old_logs_suffix')}');
      }
    } catch (e) {
      debugPrint('ErrorLogService: ${LocalizationService.t('debug_cleanup_logs_failed')} - $e');
    }
  }
}
