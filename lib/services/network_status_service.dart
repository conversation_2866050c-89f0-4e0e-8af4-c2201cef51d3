import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'localization_service.dart';

/// 网络连接类型
enum NetworkType {
  none,      // 无网络连接
  wifi,      // WiFi连接
  mobile,    // 移动网络
  ethernet,  // 以太网连接
  unknown,   // 未知连接类型
}

/// 网络状态信息
class NetworkStatus {
  final bool isConnected;
  final NetworkType type;
  final bool isMetered;  // 是否为计费网络
  final DateTime lastChecked;

  const NetworkStatus({
    required this.isConnected,
    required this.type,
    required this.isMetered,
    required this.lastChecked,
  });

  NetworkStatus copyWith({
    bool? isConnected,
    NetworkType? type,
    bool? isMetered,
    DateTime? lastChecked,
  }) {
    return NetworkStatus(
      isConnected: isConnected ?? this.isConnected,
      type: type ?? this.type,
      isMetered: isMetered ?? this.isMetered,
      lastChecked: lastChecked ?? this.lastChecked,
    );
  }

  @override
  String toString() {
    return 'NetworkStatus(isConnected: $isConnected, type: $type, isMetered: $isMetered, lastChecked: $lastChecked)';
  }
}

/// 网络状态管理服务
class NetworkStatusService {
  static final NetworkStatusService _instance = NetworkStatusService._internal();
  factory NetworkStatusService() => _instance;
  NetworkStatusService._internal();

  // 网络状态流控制器
  final StreamController<NetworkStatus> _statusController = StreamController<NetworkStatus>.broadcast();
  
  // 当前网络状态
  NetworkStatus _currentStatus = NetworkStatus(
    isConnected: false,
    type: NetworkType.none,
    isMetered: false,
    lastChecked: DateTime.now(),
  );

  // 网络检查定时器
  Timer? _checkTimer;
  
  // 监听器列表
  final List<Function(NetworkStatus)> _listeners = [];

  // 网络检查间隔（秒）
  static const int _checkInterval = 30;
  
  // 快速检查间隔（秒）- 用于网络状态变化时
  static const int _quickCheckInterval = 5;

  /// 获取网络状态流
  Stream<NetworkStatus> get statusStream => _statusController.stream;

  /// 获取当前网络状态
  NetworkStatus get currentStatus => _currentStatus;

  /// 是否已连接网络
  bool get isConnected => _currentStatus.isConnected;

  /// 是否为WiFi连接
  bool get isWiFi => _currentStatus.type == NetworkType.wifi;

  /// 是否为移动网络
  bool get isMobile => _currentStatus.type == NetworkType.mobile;

  /// 是否为计费网络
  bool get isMetered => _currentStatus.isMetered;

  /// 初始化网络状态服务
  Future<void> initialize() async {
    debugPrint('NetworkStatusService: 初始化网络状态服务');
    
    // 立即检查一次网络状态
    await _checkNetworkStatus();
    
    // 启动定期检查
    _startPeriodicCheck();
    
    debugPrint('NetworkStatusService: 初始化完成，当前状态: $_currentStatus');
  }

  /// 添加网络状态监听器
  void addListener(Function(NetworkStatus) listener) {
    _listeners.add(listener);
  }

  /// 移除网络状态监听器
  void removeListener(Function(NetworkStatus) listener) {
    _listeners.remove(listener);
  }

  /// 手动检查网络状态
  Future<NetworkStatus> checkNetworkStatus() async {
    await _checkNetworkStatus();
    return _currentStatus;
  }

  /// 检查网络状态的内部方法
  Future<void> _checkNetworkStatus() async {
    try {
      final previousStatus = _currentStatus;
      
      // 检查网络连接
      final isConnected = await _testNetworkConnection();
      final type = await _detectNetworkType();
      final isMetered = _isMeteredConnection(type);
      
      _currentStatus = NetworkStatus(
        isConnected: isConnected,
        type: type,
        isMetered: isMetered,
        lastChecked: DateTime.now(),
      );

      // 如果状态发生变化，通知监听器
      if (_hasStatusChanged(previousStatus, _currentStatus)) {
        _notifyListeners();
        _statusController.add(_currentStatus);
        
        debugPrint('NetworkStatusService: ${LocalizationService.t('debug_network_status_changed')} - $_currentStatus');
        
        // 如果网络状态发生变化，启动快速检查模式
        _startQuickCheck();
      }
    } catch (e) {
      debugPrint('NetworkStatusService: 检查网络状态失败 - $e');
      
      // 发生错误时，假设网络不可用
      _currentStatus = NetworkStatus(
        isConnected: false,
        type: NetworkType.none,
        isMetered: false,
        lastChecked: DateTime.now(),
      );
    }
  }

  /// 测试网络连接
  Future<bool> _testNetworkConnection() async {
    try {
      // 尝试连接多个可靠的服务器，增加超时时间和重试机制
      final List<String> testHosts = [
        'www.baidu.com',
        'cn.bing.com',
        '*******',
        'www.google.com',
      ];

      // 并发测试多个主机，提高检测速度和准确性
      final futures = testHosts.map((host) async {
        try {
          final result = await InternetAddress.lookup(host).timeout(
            const Duration(seconds: 8), // 增加超时时间
          );

          if (result.isNotEmpty && result[0].rawAddress.isNotEmpty) {
            return true;
          }
          return false;
        } catch (e) {
          return false;
        }
      });

      // 等待任意一个成功
      final results = await Future.wait(futures);
      final hasConnection = results.any((result) => result == true);

      debugPrint('NetworkStatusService: 网络连接测试结果 - $hasConnection');
      return hasConnection;
    } catch (e) {
      debugPrint('NetworkStatusService: 网络连接测试失败 - $e');
      return false;
    }
  }

  /// 检测网络类型
  Future<NetworkType> _detectNetworkType() async {
    try {
      // 在实际应用中，这里应该使用connectivity_plus插件
      // 由于当前项目没有该依赖，我们使用简化的检测逻辑
      
      // 简化的网络类型检测
      if (Platform.isAndroid || Platform.isIOS) {
        // 移动平台默认检测为移动网络，实际应用中需要更精确的检测
        return NetworkType.mobile;
      } else if (Platform.isWindows || Platform.isMacOS || Platform.isLinux) {
        // 桌面平台默认检测为以太网
        return NetworkType.ethernet;
      }
      
      return NetworkType.unknown;
    } catch (e) {
      debugPrint('NetworkStatusService: ${LocalizationService.t('debug_network_type_detect_failed')} - $e');
      return NetworkType.unknown;
    }
  }

  /// 判断是否为计费网络
  bool _isMeteredConnection(NetworkType type) {
    // 移动网络通常是计费的
    return type == NetworkType.mobile;
  }

  /// 检查网络状态是否发生变化
  bool _hasStatusChanged(NetworkStatus previous, NetworkStatus current) {
    return previous.isConnected != current.isConnected ||
           previous.type != current.type ||
           previous.isMetered != current.isMetered;
  }

  /// 通知所有监听器
  void _notifyListeners() {
    for (final listener in _listeners) {
      try {
        listener(_currentStatus);
      } catch (e) {
        debugPrint('NetworkStatusService: 通知监听器失败 - $e');
      }
    }
  }

  /// 启动定期检查
  void _startPeriodicCheck() {
    _checkTimer?.cancel();
    _checkTimer = Timer.periodic(
      const Duration(seconds: _checkInterval),
      (_) => _checkNetworkStatus(),
    );
  }

  /// 启动快速检查模式
  void _startQuickCheck() {
    _checkTimer?.cancel();
    
    // 快速检查5次，然后恢复正常间隔
    int quickCheckCount = 0;
    _checkTimer = Timer.periodic(
      const Duration(seconds: _quickCheckInterval),
      (timer) async {
        await _checkNetworkStatus();
        quickCheckCount++;
        
        if (quickCheckCount >= 5) {
          timer.cancel();
          _startPeriodicCheck();
        }
      },
    );
  }

  /// 销毁服务
  void dispose() {
    debugPrint('NetworkStatusService: 销毁网络状态服务');
    
    _checkTimer?.cancel();
    _statusController.close();
    _listeners.clear();
  }
}
