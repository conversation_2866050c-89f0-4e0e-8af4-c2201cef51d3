import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// 数据缓存服务
class CacheService {
  static const String _homeDataKey = 'home_data';
  static const String _appsDataKey = 'apps_data';
  static const String _workspaceDataKey = 'workspace_data';
  static const String _messagesDataKey = 'messages_data';
  
  static const String _homeDataTimestampKey = 'home_data_timestamp';
  static const String _appsDataTimestampKey = 'apps_data_timestamp';
  static const String _workspaceDataTimestampKey = 'workspace_data_timestamp';
  static const String _messagesDataTimestampKey = 'messages_data_timestamp';

  // 缓存有效期（分钟）- 增加到2小时，减少缓存过期导致的数据丢失
  static const int _cacheValidityMinutes = 120;

  /// 保存首页数据到缓存
  static Future<void> saveHomeData(Map<String, dynamic> data) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_homeD<PERSON><PERSON><PERSON>, json<PERSON>ncode(data));
    await prefs.setInt(_homeDataTimestampKey, DateTime.now().millisecondsSinceEpoch);
  }

  /// 获取首页缓存数据
  static Future<Map<String, dynamic>?> getHomeData() async {
    final prefs = await SharedPreferences.getInstance();
    final dataString = prefs.getString(_homeDataKey);

    try {
      final timestamp = prefs.getInt(_homeDataTimestampKey);
      if (dataString != null && timestamp != null) {
        if (_isCacheValid(timestamp)) {
          return jsonDecode(dataString);
        }
      }
    } catch (e) {
      // 如果时间戳获取失败，清除相关缓存
      await prefs.remove(_homeDataKey);
      await prefs.remove(_homeDataTimestampKey);
    }
    return null;
  }

  /// 保存应用数据到缓存
  static Future<void> saveAppsData(Map<String, dynamic> data) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_appsDataKey, jsonEncode(data));
    await prefs.setInt(_appsDataTimestampKey, DateTime.now().millisecondsSinceEpoch);
  }

  /// 获取应用缓存数据
  static Future<Map<String, dynamic>?> getAppsData() async {
    final prefs = await SharedPreferences.getInstance();
    final dataString = prefs.getString(_appsDataKey);

    try {
      final timestamp = prefs.getInt(_appsDataTimestampKey);
      if (dataString != null && timestamp != null) {
        if (_isCacheValid(timestamp)) {
          return jsonDecode(dataString);
        }
      }
    } catch (e) {
      // 如果时间戳获取失败，清除相关缓存
      await prefs.remove(_appsDataKey);
      await prefs.remove(_appsDataTimestampKey);
    }
    return null;
  }

  /// 保存工作台数据到缓存
  static Future<void> saveWorkspaceData(Map<String, dynamic> data) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_workspaceDataKey, jsonEncode(data));
    await prefs.setInt(_workspaceDataTimestampKey, DateTime.now().millisecondsSinceEpoch);
  }

  /// 获取工作台缓存数据
  static Future<Map<String, dynamic>?> getWorkspaceData() async {
    final prefs = await SharedPreferences.getInstance();
    final dataString = prefs.getString(_workspaceDataKey);

    try {
      final timestamp = prefs.getInt(_workspaceDataTimestampKey);
      if (dataString != null && timestamp != null) {
        if (_isCacheValid(timestamp)) {
          return jsonDecode(dataString);
        }
      }
    } catch (e) {
      // 如果时间戳获取失败，清除相关缓存
      await prefs.remove(_workspaceDataKey);
      await prefs.remove(_workspaceDataTimestampKey);
    }
    return null;
  }

  /// 保存消息数据到缓存
  static Future<void> saveMessagesData(Map<String, dynamic> data) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_messagesDataKey, jsonEncode(data));
    await prefs.setInt(_messagesDataTimestampKey, DateTime.now().millisecondsSinceEpoch);
  }

  /// 获取消息缓存数据
  static Future<Map<String, dynamic>?> getMessagesData() async {
    final prefs = await SharedPreferences.getInstance();
    final dataString = prefs.getString(_messagesDataKey);

    try {
      final timestamp = prefs.getInt(_messagesDataTimestampKey);
      if (dataString != null && timestamp != null) {
        if (_isCacheValid(timestamp)) {
          return jsonDecode(dataString);
        }
      }
    } catch (e) {
      // 如果时间戳获取失败，清除相关缓存
      await prefs.remove(_messagesDataKey);
      await prefs.remove(_messagesDataTimestampKey);
    }
    return null;
  }

  /// 检查缓存是否有效
  static bool _isCacheValid(int timestamp) {
    final now = DateTime.now().millisecondsSinceEpoch;
    final difference = now - timestamp;
    final validityDuration = Duration(minutes: _cacheValidityMinutes).inMilliseconds;
    return difference < validityDuration;
  }

  /// 清除指定页面的缓存
  static Future<void> clearHomeCache() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_homeDataKey);
    await prefs.remove(_homeDataTimestampKey);
  }

  static Future<void> clearAppsCache() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_appsDataKey);
    await prefs.remove(_appsDataTimestampKey);
  }

  static Future<void> clearWorkspaceCache() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_workspaceDataKey);
    await prefs.remove(_workspaceDataTimestampKey);
  }

  static Future<void> clearMessagesCache() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_messagesDataKey);
    await prefs.remove(_messagesDataTimestampKey);
  }

  /// 清除所有缓存
  static Future<void> clearAllCache() async {
    await clearHomeCache();
    await clearAppsCache();
    await clearWorkspaceCache();
    await clearMessagesCache();
  }

  /// 检查是否有缓存数据
  static Future<bool> hasHomeCache() async {
    final data = await getHomeData();
    return data != null;
  }

  static Future<bool> hasAppsCache() async {
    final data = await getAppsData();
    return data != null;
  }

  static Future<bool> hasWorkspaceCache() async {
    final data = await getWorkspaceData();
    return data != null;
  }

  static Future<bool> hasMessagesCache() async {
    final data = await getMessagesData();
    return data != null;
  }

  /// 获取缓存时间戳（用于显示最后更新时间）
  static Future<DateTime?> getHomeCacheTimestamp() async {
    final prefs = await SharedPreferences.getInstance();
    try {
      final timestamp = prefs.getInt(_homeDataTimestampKey);
      return timestamp != null ? DateTime.fromMillisecondsSinceEpoch(timestamp) : null;
    } catch (e) {
      return null;
    }
  }

  static Future<DateTime?> getAppsCacheTimestamp() async {
    final prefs = await SharedPreferences.getInstance();
    try {
      final timestamp = prefs.getInt(_appsDataTimestampKey);
      return timestamp != null ? DateTime.fromMillisecondsSinceEpoch(timestamp) : null;
    } catch (e) {
      return null;
    }
  }

  static Future<DateTime?> getWorkspaceCacheTimestamp() async {
    final prefs = await SharedPreferences.getInstance();
    try {
      final timestamp = prefs.getInt(_workspaceDataTimestampKey);
      return timestamp != null ? DateTime.fromMillisecondsSinceEpoch(timestamp) : null;
    } catch (e) {
      return null;
    }
  }

  static Future<DateTime?> getMessagesCacheTimestamp() async {
    final prefs = await SharedPreferences.getInstance();
    try {
      final timestamp = prefs.getInt(_messagesDataTimestampKey);
      return timestamp != null ? DateTime.fromMillisecondsSinceEpoch(timestamp) : null;
    } catch (e) {
      return null;
    }
  }

  /// 通用缓存数据保存
  static Future<void> setCachedData(
    String key,
    Map<String, dynamic> data, {
    Duration? expiry,
  }) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(key, jsonEncode(data));

    final expiryTime = expiry != null
        ? DateTime.now().add(expiry).millisecondsSinceEpoch
        : DateTime.now().add(const Duration(hours: 24)).millisecondsSinceEpoch;

    await prefs.setInt('${key}_expiry', expiryTime);
  }

  /// 通用缓存数据获取
  static Future<Map<String, dynamic>?> getCachedData(String key) async {
    final prefs = await SharedPreferences.getInstance();
    final dataString = prefs.getString(key);
    final expiryTime = prefs.getInt('${key}_expiry');

    if (dataString != null && expiryTime != null) {
      if (DateTime.now().millisecondsSinceEpoch < expiryTime) {
        return jsonDecode(dataString);
      } else {
        // 缓存已过期，清除
        await prefs.remove(key);
        await prefs.remove('${key}_expiry');
      }
    }
    return null;
  }

  /// 清除指定缓存
  static Future<void> clearCachedData(String key) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(key);
    await prefs.remove('${key}_expiry');
  }

  /// 清除所有过期缓存
  static Future<void> clearExpiredCache() async {
    final prefs = await SharedPreferences.getInstance();
    final keys = prefs.getKeys();

    for (final key in keys) {
      if (key.endsWith('_timestamp')) {
        try {
          final timestamp = prefs.getInt(key);
          if (timestamp != null && !_isCacheValid(timestamp)) {
            final dataKey = key.replaceAll('_timestamp', '');
            await prefs.remove(dataKey);
            await prefs.remove(key);
          }
        } catch (e) {
          // 如果获取时间戳失败，直接删除这个键
          await prefs.remove(key);
          final dataKey = key.replaceAll('_timestamp', '');
          await prefs.remove(dataKey);
        }
      }
    }
  }

  /// 强制清除所有页面缓存数据（用于重新登录时）
  static Future<void> forceCleanAllPageCache() async {
    try {
      debugPrint('开始强制清除所有页面缓存数据...');

      final prefs = await SharedPreferences.getInstance();

      // 清除所有页面的缓存数据和时间戳
      const cacheKeys = [
        _homeDataKey,
        _homeDataTimestampKey,
        _appsDataKey,
        _appsDataTimestampKey,
        _workspaceDataKey,
        _workspaceDataTimestampKey,
        _messagesDataKey,
        _messagesDataTimestampKey,
      ];

      for (final key in cacheKeys) {
        await prefs.remove(key);
      }

      // 清除所有通用缓存数据（以_expiry结尾的键对应的数据）
      final allKeys = prefs.getKeys();
      final expiryKeys = allKeys.where((key) => key.endsWith('_expiry')).toList();

      for (final expiryKey in expiryKeys) {
        final dataKey = expiryKey.replaceAll('_expiry', '');
        await prefs.remove(dataKey);
        await prefs.remove(expiryKey);
      }

      // 清除其他时间戳相关的缓存键
      final timestampKeys = allKeys.where((key) => key.endsWith('_timestamp')).toList();
      for (final timestampKey in timestampKeys) {
        final dataKey = timestampKey.replaceAll('_timestamp', '');
        await prefs.remove(dataKey);
        await prefs.remove(timestampKey);
      }

      debugPrint('所有页面缓存数据强制清除完成');
    } catch (e) {
      debugPrint('强制清除页面缓存数据失败: $e');
      rethrow;
    }
  }
}
