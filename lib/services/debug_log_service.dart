import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'localization_service.dart';

/// 日志级别枚举
enum LogLevel {
  debug,
  info,
  warning,
  error,
}

/// 日志条目模型
class LogEntry {
  final String id;
  final LogLevel level;
  final String message;
  final DateTime timestamp;
  final String? tag;
  final Map<String, dynamic>? extra;

  LogEntry({
    required this.id,
    required this.level,
    required this.message,
    required this.timestamp,
    this.tag,
    this.extra,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'level': level.name,
      'message': message,
      'timestamp': timestamp.toIso8601String(),
      'tag': tag,
      'extra': extra,
    };
  }

  factory LogEntry.fromJson(Map<String, dynamic> json) {
    return LogEntry(
      id: json['id'],
      level: LogLevel.values.firstWhere((e) => e.name == json['level']),
      message: json['message'],
      timestamp: DateTime.parse(json['timestamp']),
      tag: json['tag'],
      extra: json['extra'],
    );
  }

  String get levelText {
    switch (level) {
      case LogLevel.debug:
        return 'DEBUG';
      case LogLevel.info:
        return 'INFO';
      case LogLevel.warning:
        return 'WARN';
      case LogLevel.error:
        return 'ERROR';
    }
  }

  String get formattedMessage {
    final timeStr = '${timestamp.hour.toString().padLeft(2, '0')}:'
        '${timestamp.minute.toString().padLeft(2, '0')}:'
        '${timestamp.second.toString().padLeft(2, '0')}.'
        '${timestamp.millisecond.toString().padLeft(3, '0')}';
    
    final tagStr = tag != null ? '[$tag] ' : '';
    return '$timeStr [$levelText] $tagStr$message';
  }
}

/// 调试日志管理服务
class DebugLogService {
  static final DebugLogService _instance = DebugLogService._internal();
  factory DebugLogService() => _instance;
  DebugLogService._internal();

  static const String _enabledKey = 'debug_log_enabled';
  static const String _logsKey = 'debug_logs';
  static const int _maxLogCount = 1000; // 最大日志条数

  final List<LogEntry> _logs = [];
  bool _isEnabled = false;
  LogLevel _minLevel = LogLevel.debug;

  /// 初始化日志服务
  Future<void> initialize() async {
    final prefs = await SharedPreferences.getInstance();
    _isEnabled = prefs.getBool(_enabledKey) ?? false;
    
    // 加载已保存的日志
    await _loadLogs();
  }

  /// 是否启用日志
  bool get isEnabled => _isEnabled;

  /// 设置日志启用状态
  Future<void> setEnabled(bool enabled) async {
    _isEnabled = enabled;
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_enabledKey, enabled);
  }

  /// 设置最小日志级别
  void setMinLevel(LogLevel level) {
    _minLevel = level;
  }

  /// 获取所有日志
  List<LogEntry> get logs => List.unmodifiable(_logs);

  /// 记录调试日志
  void debug(String message, {String? tag, Map<String, dynamic>? extra}) {
    _log(LogLevel.debug, message, tag: tag, extra: extra);
  }

  /// 记录信息日志
  void info(String message, {String? tag, Map<String, dynamic>? extra}) {
    _log(LogLevel.info, message, tag: tag, extra: extra);
  }

  /// 记录警告日志
  void warning(String message, {String? tag, Map<String, dynamic>? extra}) {
    _log(LogLevel.warning, message, tag: tag, extra: extra);
  }

  /// 记录错误日志
  void error(String message, {String? tag, Map<String, dynamic>? extra}) {
    _log(LogLevel.error, message, tag: tag, extra: extra);
  }

  /// 记录API请求日志
  void logApiRequest(String method, String url, Map<String, String>? headers, dynamic body) {
    if (!_isEnabled) return;
    
    final message = '''=== API请求开始 ===
方法: $method
URL: $url
请求头: $headers${body != null ? '\n请求体: $body' : ''}
时间: ${DateTime.now()}
==================''';
    
    _log(LogLevel.info, message, tag: 'API_REQUEST');
  }

  /// 记录API响应日志
  void logApiResponse(String url, int statusCode, dynamic responseData, {String? error}) {
    if (!_isEnabled) return;
    
    final message = '''=== API响应开始 ===
URL: $url
状态码: $statusCode${error != null ? '\n错误信息: $error' : '\n响应数据: $responseData'}
时间: ${DateTime.now()}
==================''';
    
    _log(LogLevel.info, message, tag: 'API_RESPONSE');
  }

  /// 内部日志记录方法
  void _log(LogLevel level, String message, {String? tag, Map<String, dynamic>? extra}) {
    // 检查是否启用和级别过滤
    if (!_isEnabled || level.index < _minLevel.index) {
      return;
    }

    final entry = LogEntry(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      level: level,
      message: message,
      timestamp: DateTime.now(),
      tag: tag,
      extra: extra,
    );

    _logs.add(entry);

    // 限制日志数量
    if (_logs.length > _maxLogCount) {
      _logs.removeAt(0);
    }

    // 输出到控制台（开发模式）
    if (kDebugMode) {
      print(entry.formattedMessage);
    }

    // 异步保存到本地存储
    _saveLogs();
  }

  /// 清除所有日志
  Future<void> clearLogs() async {
    _logs.clear();
    await _saveLogs();
  }

  /// 获取日志文本（用于复制）
  String getLogsAsText({LogLevel? filterLevel}) {
    final filteredLogs = filterLevel != null 
        ? _logs.where((log) => log.level == filterLevel).toList()
        : _logs;
    
    return filteredLogs.map((log) => log.formattedMessage).join('\n');
  }

  /// 保存日志到本地存储
  Future<void> _saveLogs() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final logsJson = _logs.map((log) => log.toJson()).toList();
      await prefs.setString(_logsKey, jsonEncode(logsJson));
    } catch (e) {
      if (kDebugMode) {
        print('${LocalizationService.t('debug_save_logs_failed_simple')}: $e');
      }
    }
  }

  /// 从本地存储加载日志
  Future<void> _loadLogs() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final logsString = prefs.getString(_logsKey);
      
      if (logsString != null) {
        final logsJson = jsonDecode(logsString) as List;
        _logs.clear();
        _logs.addAll(logsJson.map((json) => LogEntry.fromJson(json)));
      }
    } catch (e) {
      if (kDebugMode) {
        print('${LocalizationService.t('debug_load_logs_failed_simple')}: $e');
      }
    }
  }

  /// 获取日志统计信息
  Map<LogLevel, int> getLogStats() {
    final stats = <LogLevel, int>{};
    for (final level in LogLevel.values) {
      stats[level] = _logs.where((log) => log.level == level).length;
    }
    return stats;
  }
}
