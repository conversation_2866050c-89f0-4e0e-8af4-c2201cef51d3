import 'package:flutter/material.dart';
import '../constants/app_theme.dart';
import '../screens/login_screen.dart';
import '../services/auth_service.dart';
import '../services/localization_service.dart';

/// Token过期处理服务
/// 提供统一的token过期处理机制，包括友好的用户提示和导航
class TokenExpiryHandler {
  static final TokenExpiryHandler _instance = TokenExpiryHandler._internal();
  factory TokenExpiryHandler() => _instance;
  TokenExpiryHandler._internal();

  // 防止重复显示对话框
  bool _isDialogShowing = false;

  /// 处理token过期
  /// 显示友好的对话框提示，用户点击后跳转到登录页面
  Future<void> handleTokenExpiry(BuildContext context) async {
    // 防止重复显示对话框
    if (_isDialogShowing) {
      return;
    }

    _isDialogShowing = true;

    try {
      // 清除登录信息
      final authService = AuthService();
      await authService.clearLoginInfo();

      if (context.mounted) {
        // 显示友好的对话框
        await showDialog<void>(
          context: context,
          barrierDismissible: false, // 用户必须点击按钮
          builder: (BuildContext dialogContext) {
            return AlertDialog(
              title: Row(
                children: [
                  Icon(
                    Icons.warning_amber_rounded,
                    color: AppTheme.warningColor,
                    size: 24,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    LocalizationService.t('login_expired'),
                    style: TextStyle(
                      color: AppTheme.getTextPrimaryColor(dialogContext),
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
              content: Text(
                LocalizationService.t('token_expired_message'),
                style: TextStyle(
                  color: AppTheme.getTextSecondaryColor(dialogContext),
                  fontSize: 16,
                ),
              ),
              backgroundColor: AppTheme.getSurfaceColor(dialogContext),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              actions: [
                ElevatedButton(
                  onPressed: () {
                    Navigator.of(dialogContext).pop();
                    _navigateToLogin(context);
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.primaryColor,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    padding: const EdgeInsets.symmetric(
                      horizontal: 24,
                      vertical: 12,
                    ),
                  ),
                  child: Text(
                    LocalizationService.t('go_to_login'),
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            );
          },
        );
      }
    } finally {
      _isDialogShowing = false;
    }
  }

  /// 导航到登录页面
  void _navigateToLogin(BuildContext context) {
    if (context.mounted) {
      Navigator.of(context).pushAndRemoveUntil(
        MaterialPageRoute(builder: (context) => const LoginScreen()),
        (route) => false,
      );
    }
  }

  /// 显示简单的SnackBar提示（备用方案）
  void showTokenExpiredSnackBar(BuildContext context) {
    if (context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            LocalizationService.t('token_expired'),
            style: const TextStyle(color: Colors.white),
          ),
          backgroundColor: AppTheme.errorColor, // 统一使用错误颜色
          duration: const Duration(seconds: 3),
          action: SnackBarAction(
            label: LocalizationService.t('go_to_login'),
            textColor: Colors.white,
            onPressed: () => _navigateToLogin(context),
          ),
        ),
      );
    }
  }

  /// 检查错误是否为token过期错误
  bool isTokenExpiredError(dynamic error) {
    final errorString = error.toString().toLowerCase();

    // 检查HTTP状态码
    if (errorString.contains('401') || errorString.contains('403')) {
      return true;
    }

    // 检查关键词
    if (errorString.contains('token') ||
        errorString.contains('unauthorized') ||
        errorString.contains('forbidden') ||
        errorString.contains('expired') ||
        errorString.contains('invalid') ||
        errorString.contains('登录已过期') ||
        errorString.contains('认证失败') ||
        errorString.contains('权限不足')) {
      return true;
    }

    return false;
  }
}
