import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import '../constants/app_constants.dart';
import 'network_status_service.dart';
import 'network_error_handler.dart';
import 'cache_service.dart';
import 'localization_service.dart';

/// HTTP请求方法
enum HttpMethod { get, post, put, delete, patch }

/// HTTP请求配置
class HttpRequestConfig {
  final String url;
  final HttpMethod method;
  final Map<String, String>? headers;
  final dynamic body;
  final Duration? timeout;
  final RetryConfig? retryConfig;
  final bool useCache;
  final Duration? cacheExpiry;
  final String? cacheKey;

  const HttpRequestConfig({
    required this.url,
    required this.method,
    this.headers,
    this.body,
    this.timeout,
    this.retryConfig,
    this.useCache = false,
    this.cacheExpiry,
    this.cacheKey,
  });
}

/// HTTP响应包装
class HttpResponse {
  final int statusCode;
  final Map<String, String> headers;
  final String body;
  final bool fromCache;
  final DateTime timestamp;

  const HttpResponse({
    required this.statusCode,
    required this.headers,
    required this.body,
    this.fromCache = false,
    required this.timestamp,
  });

  /// 解析JSON响应
  Map<String, dynamic> get json {
    try {
      return jsonDecode(body) as Map<String, dynamic>;
    } catch (e) {
      throw FormatException('${LocalizationService.t('json_parse_error')}: $e');
    }
  }

  /// 检查响应是否成功
  bool get isSuccess => statusCode >= 200 && statusCode < 300;
}

/// 统一HTTP客户端
class HttpClient {
  static final HttpClient _instance = HttpClient._internal();
  factory HttpClient() => _instance;
  HttpClient._internal();

  final http.Client _client = http.Client();
  final NetworkStatusService _networkStatus = NetworkStatusService();
  final NetworkErrorHandler _errorHandler = NetworkErrorHandler();
  
  // 默认配置
  static const Duration _defaultTimeout = Duration(seconds: 30);
  static const RetryConfig _defaultRetryConfig = RetryConfig.offlineFirst;
  
  // 请求拦截器
  final List<Function(HttpRequestConfig)> _requestInterceptors = [];
  final List<Function(HttpResponse)> _responseInterceptors = [];

  /// 初始化HTTP客户端
  Future<void> initialize() async {
    debugPrint('HttpClient: 初始化HTTP客户端');
    
    // 初始化依赖服务
    await _networkStatus.initialize();
    _errorHandler.initialize();
    
    // 添加默认请求拦截器
    _addDefaultInterceptors();
    
    debugPrint('HttpClient: 初始化完成');
  }

  /// 执行HTTP请求
  Future<HttpResponse> request(HttpRequestConfig config) async {
    final requestKey = _generateRequestKey(config);
    
    return await _errorHandler.executeWithRetry(
      () => _executeRequest(config),
      requestKey: requestKey,
      config: config.retryConfig ?? _defaultRetryConfig,
      useCache: config.useCache,
    );
  }

  /// GET请求
  Future<HttpResponse> get(
    String url, {
    Map<String, String>? headers,
    Duration? timeout,
    RetryConfig? retryConfig,
    bool useCache = false,
    Duration? cacheExpiry,
    String? cacheKey,
  }) async {
    return await request(HttpRequestConfig(
      url: url,
      method: HttpMethod.get,
      headers: headers,
      timeout: timeout,
      retryConfig: retryConfig,
      useCache: useCache,
      cacheExpiry: cacheExpiry,
      cacheKey: cacheKey,
    ));
  }

  /// POST请求
  Future<HttpResponse> post(
    String url, {
    Map<String, String>? headers,
    dynamic body,
    Duration? timeout,
    RetryConfig? retryConfig,
  }) async {
    return await request(HttpRequestConfig(
      url: url,
      method: HttpMethod.post,
      headers: headers,
      body: body,
      timeout: timeout,
      retryConfig: retryConfig,
    ));
  }

  /// PUT请求
  Future<HttpResponse> put(
    String url, {
    Map<String, String>? headers,
    dynamic body,
    Duration? timeout,
    RetryConfig? retryConfig,
  }) async {
    return await request(HttpRequestConfig(
      url: url,
      method: HttpMethod.put,
      headers: headers,
      body: body,
      timeout: timeout,
      retryConfig: retryConfig,
    ));
  }

  /// DELETE请求
  Future<HttpResponse> delete(
    String url, {
    Map<String, String>? headers,
    Duration? timeout,
    RetryConfig? retryConfig,
  }) async {
    return await request(HttpRequestConfig(
      url: url,
      method: HttpMethod.delete,
      headers: headers,
      timeout: timeout,
      retryConfig: retryConfig,
    ));
  }

  /// 执行实际的HTTP请求
  Future<HttpResponse> _executeRequest(HttpRequestConfig config) async {
    // 检查缓存
    if (config.useCache && config.method == HttpMethod.get) {
      final cachedResponse = await _getCachedResponse(config);
      if (cachedResponse != null) {
        debugPrint('HttpClient: 使用缓存响应 - ${config.url}');
        return cachedResponse;
      }
    }

    // 检查网络状态
    if (!_networkStatus.isConnected) {
      throw NetworkError(
        type: NetworkErrorType.noConnection,
        message: LocalizationService.t('network_connection_error'),
        timestamp: DateTime.now(),
        requestUrl: config.url,
      );
    }

    // 应用请求拦截器
    for (final interceptor in _requestInterceptors) {
      interceptor(config);
    }

    // 准备请求
    final uri = Uri.parse(config.url);
    final headers = _buildHeaders(config.headers);
    final timeout = config.timeout ?? _defaultTimeout;

    http.Response response;

    try {
      // 根据请求方法执行请求
      switch (config.method) {
        case HttpMethod.get:
          response = await _client.get(uri, headers: headers).timeout(timeout);
          break;
        case HttpMethod.post:
          response = await _client.post(
            uri,
            headers: headers,
            body: _prepareBody(config.body, headers),
          ).timeout(timeout);
          break;
        case HttpMethod.put:
          response = await _client.put(
            uri,
            headers: headers,
            body: _prepareBody(config.body, headers),
          ).timeout(timeout);
          break;
        case HttpMethod.delete:
          response = await _client.delete(uri, headers: headers).timeout(timeout);
          break;
        case HttpMethod.patch:
          response = await _client.patch(
            uri,
            headers: headers,
            body: _prepareBody(config.body, headers),
          ).timeout(timeout);
          break;
      }

      // 检查响应状态
      if (response.statusCode >= 400) {
        // 特殊处理401未授权错误和403禁止访问错误（token失效或无权限）
        if (response.statusCode == 401 || response.statusCode == 403) {
          throw TokenExpiredException(
            LocalizationService.t('token_expired'),
            statusCode: response.statusCode,
            uri: uri,
          );
        }

        throw HttpException(
          'HTTP ${response.statusCode}: ${response.reasonPhrase}',
          uri: uri,
        );
      }

      // 创建响应对象
      final httpResponse = HttpResponse(
        statusCode: response.statusCode,
        headers: response.headers,
        body: response.body,
        timestamp: DateTime.now(),
      );

      // 应用响应拦截器
      for (final interceptor in _responseInterceptors) {
        interceptor(httpResponse);
      }

      // 缓存响应
      if (config.useCache && config.method == HttpMethod.get) {
        await _cacheResponse(config, httpResponse);
      }

      debugPrint('HttpClient: 请求成功 - ${config.url} (${response.statusCode})');
      return httpResponse;

    } on TimeoutException {
      throw NetworkError(
        type: NetworkErrorType.timeout,
        message: LocalizationService.t('request_timeout'),
        timestamp: DateTime.now(),
        requestUrl: config.url,
      );
    } on SocketException catch (e) {
      throw NetworkError(
        type: NetworkErrorType.noConnection,
        message: '${LocalizationService.t('network_connection_failed')}: ${e.message}',
        timestamp: DateTime.now(),
        requestUrl: config.url,
      );
    } on HttpException catch (e) {
      throw NetworkError(
        type: NetworkErrorType.serverError,
        message: e.message,
        statusCode: null,
        timestamp: DateTime.now(),
        requestUrl: config.url,
      );
    }
  }

  /// 构建请求头
  Map<String, String> _buildHeaders(Map<String, String>? customHeaders) {
    final headers = <String, String>{
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'User-Agent': 'FlutterApp/${AppConstants.appVersion}',
    };

    if (customHeaders != null) {
      headers.addAll(customHeaders);
    }

    return headers;
  }

  /// 准备请求体
  String? _prepareBody(dynamic body, Map<String, String> headers) {
    if (body == null) return null;

    if (body is String) {
      return body;
    }

    if (body is Map || body is List) {
      return jsonEncode(body);
    }

    return body.toString();
  }

  /// 生成请求键
  String _generateRequestKey(HttpRequestConfig config) {
    return config.cacheKey ?? '${config.method.name}_${config.url}';
  }

  /// 获取缓存响应
  Future<HttpResponse?> _getCachedResponse(HttpRequestConfig config) async {
    try {
      final cacheKey = _generateRequestKey(config);
      final cachedData = await CacheService.getCachedData(cacheKey);
      
      if (cachedData != null) {
        return HttpResponse(
          statusCode: cachedData['statusCode'] ?? 200,
          headers: Map<String, String>.from(cachedData['headers'] ?? {}),
          body: cachedData['body'] ?? '',
          fromCache: true,
          timestamp: DateTime.parse(cachedData['timestamp']),
        );
      }
    } catch (e) {
      debugPrint('HttpClient: 获取缓存失败 - $e');
    }
    return null;
  }

  /// 缓存响应
  Future<void> _cacheResponse(HttpRequestConfig config, HttpResponse response) async {
    try {
      final cacheKey = _generateRequestKey(config);
      final cacheData = {
        'statusCode': response.statusCode,
        'headers': response.headers,
        'body': response.body,
        'timestamp': response.timestamp.toIso8601String(),
      };
      
      await CacheService.setCachedData(
        cacheKey,
        cacheData,
        expiry: config.cacheExpiry ?? const Duration(hours: 1),
      );
    } catch (e) {
      debugPrint('HttpClient: 缓存响应失败 - $e');
    }
  }

  /// 根据状态码获取错误类型
  NetworkErrorType _getErrorTypeFromStatusCode(int statusCode) {
    if (statusCode == 429) {
      return NetworkErrorType.rateLimited;
    } else if (statusCode >= 500) {
      return NetworkErrorType.serverError;
    } else if (statusCode >= 400) {
      return NetworkErrorType.clientError;
    }
    return NetworkErrorType.unknown;
  }

  /// 添加默认拦截器
  void _addDefaultInterceptors() {
    // 请求日志拦截器
    addRequestInterceptor((config) {
      debugPrint('HttpClient: ${config.method.name.toUpperCase()} ${config.url}');
    });

    // 响应日志拦截器
    addResponseInterceptor((response) {
      debugPrint('HttpClient: 响应 ${response.statusCode} (${response.fromCache ? '缓存' : '网络'})');
    });
  }

  /// 添加请求拦截器
  void addRequestInterceptor(Function(HttpRequestConfig) interceptor) {
    _requestInterceptors.add(interceptor);
  }

  /// 添加响应拦截器
  void addResponseInterceptor(Function(HttpResponse) interceptor) {
    _responseInterceptors.add(interceptor);
  }

  /// 清除所有拦截器
  void clearInterceptors() {
    _requestInterceptors.clear();
    _responseInterceptors.clear();
    _addDefaultInterceptors();
  }

  /// 获取错误统计
  Map<String, int> getErrorStats() {
    return _errorHandler.getErrorStats();
  }

  /// 销毁HTTP客户端
  void dispose() {
    debugPrint('HttpClient: 销毁HTTP客户端');
    _client.close();
    _errorHandler.dispose();
    _networkStatus.dispose();
    _requestInterceptors.clear();
    _responseInterceptors.clear();
  }
}
