import 'package:flutter/foundation.dart';
import 'auth_service.dart';
import 'cache_service.dart';

/// 全局应用状态管理器
/// 负责管理应用级别的状态，避免重复检查和不必要的重新加载
class AppStateManager {
  static final AppStateManager _instance = AppStateManager._internal();
  factory AppStateManager() => _instance;
  AppStateManager._internal();

  // 强制刷新相关状态
  bool _needsForceRefresh = false;
  bool _hasCheckedForceRefresh = false;
  bool _isForceRefreshInProgress = false;

  /// 检查是否需要强制刷新（只检查一次）
  Future<bool> shouldForceRefresh() async {
    // 如果已经检查过，直接返回结果
    if (_hasCheckedForceRefresh) {
      debugPrint('AppStateManager: 已检查过强制刷新状态，返回: $_needsForceRefresh');
      return _needsForceRefresh;
    }

    debugPrint('AppStateManager: 首次检查强制刷新状态');
    
    try {
      final authService = AuthService();
      _needsForceRefresh = await authService.shouldForceRefresh();
      _hasCheckedForceRefresh = true;
      
      debugPrint('AppStateManager: 强制刷新检查结果: $_needsForceRefresh');
      
      return _needsForceRefresh;
    } catch (e) {
      debugPrint('AppStateManager: 检查强制刷新状态失败: $e');
      _hasCheckedForceRefresh = true;
      _needsForceRefresh = false;
      return false;
    }
  }

  /// 开始强制刷新流程
  Future<void> startForceRefresh() async {
    if (!_needsForceRefresh || _isForceRefreshInProgress) {
      return;
    }

    debugPrint('AppStateManager: 开始强制刷新流程');
    _isForceRefreshInProgress = true;

    try {
      // 清空所有页面缓存
      await CacheService.forceCleanAllPageCache();
      debugPrint('AppStateManager: 已清空所有页面缓存');
    } catch (e) {
      debugPrint('AppStateManager: 清空缓存失败: $e');
    }
  }

  /// 完成强制刷新流程
  Future<void> completeForceRefresh() async {
    if (!_isForceRefreshInProgress) {
      return;
    }

    debugPrint('AppStateManager: 完成强制刷新流程');
    
    try {
      // 清除强制刷新标记
      final authService = AuthService();
      await authService.clearForceRefreshFlag();
      
      // 重置状态
      _needsForceRefresh = false;
      _isForceRefreshInProgress = false;
      
      debugPrint('AppStateManager: 强制刷新流程已完成，状态已重置');
    } catch (e) {
      debugPrint('AppStateManager: 完成强制刷新流程失败: $e');
      // 即使失败也要重置状态，避免卡住
      _needsForceRefresh = false;
      _isForceRefreshInProgress = false;
    }
  }

  /// 检查是否正在进行强制刷新
  bool get isForceRefreshInProgress => _isForceRefreshInProgress;

  /// 重置所有状态（用于测试或特殊情况）
  void reset() {
    debugPrint('AppStateManager: 重置所有状态');
    _needsForceRefresh = false;
    _hasCheckedForceRefresh = false;
    _isForceRefreshInProgress = false;
  }

  /// 获取当前状态信息（用于调试）
  Map<String, dynamic> getStateInfo() {
    return {
      'needsForceRefresh': _needsForceRefresh,
      'hasCheckedForceRefresh': _hasCheckedForceRefresh,
      'isForceRefreshInProgress': _isForceRefreshInProgress,
    };
  }
}
