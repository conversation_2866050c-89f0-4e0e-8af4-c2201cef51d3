import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../constants/app_constants.dart';
import 'cache_service.dart';
import 'debug_log_service.dart';

/// 数据清理服务
/// 负责在退出登录时清理所有用户相关数据，但保留登录页面的基本信息
class DataCleanupService {
  static final DataCleanupService _instance = DataCleanupService._internal();
  factory DataCleanupService() => _instance;
  DataCleanupService._internal();

  /// 完全清除所有用户数据（退出登录时使用）
  /// 保留登录页面信息：用户名、密码、记住密码设置、服务器配置、主题设置、语言设置
  Future<void> clearAllUserData() async {
    try {
      debugPrint('开始清除所有用户数据...');
      
      final prefs = await SharedPreferences.getInstance();
      
      // 1. 清除认证相关数据
      await _clearAuthData(prefs);
      
      // 2. 清除所有缓存数据
      await _clearAllCacheData(prefs);
      
      // 3. 清除日志数据
      await _clearLogData(prefs);
      
      // 4. 清除API设置（但保留服务器配置）
      await _clearApiSettings(prefs);
      
      // 5. 清除用户个人偏好数据（但保留主题和语言设置）
      await _clearUserPreferences(prefs);
      
      debugPrint('所有用户数据清除完成');
    } catch (e) {
      debugPrint('清除用户数据失败: $e');
      rethrow;
    }
  }

  /// 清除认证相关数据
  Future<void> _clearAuthData(SharedPreferences prefs) async {
    try {
      const authKeys = [
        'user_token',
        'user_info', 
        'login_time',
        'session_valid',
      ];
      
      for (final key in authKeys) {
        await prefs.remove(key);
      }
      
      // 设置会话为无效
      await prefs.setBool('session_valid', false);
      
      debugPrint('认证数据清除完成');
    } catch (e) {
      debugPrint('清除认证数据失败: $e');
    }
  }

  /// 清除所有缓存数据
  Future<void> _clearAllCacheData(SharedPreferences prefs) async {
    try {
      // 使用CacheService的清除方法
      await CacheService.clearAllCache();
      
      // 清除其他缓存数据键
      const additionalCacheKeys = [
        'home_data',
        'home_data_timestamp',
        'apps_data', 
        'apps_data_timestamp',
        'workspace_data',
        'workspace_data_timestamp',
        'messages_data',
        'messages_data_timestamp',
      ];
      
      for (final key in additionalCacheKeys) {
        await prefs.remove(key);
      }
      
      // 清除通用缓存数据（查找所有以_expiry结尾的键）
      final allKeys = prefs.getKeys();
      final expiryKeys = allKeys.where((key) => key.endsWith('_expiry')).toList();
      
      for (final expiryKey in expiryKeys) {
        final dataKey = expiryKey.replaceAll('_expiry', '');
        await prefs.remove(dataKey);
        await prefs.remove(expiryKey);
      }
      
      // 清除时间戳相关的缓存键
      final timestampKeys = allKeys.where((key) => key.endsWith('_timestamp')).toList();
      for (final key in timestampKeys) {
        await prefs.remove(key);
      }
      
      debugPrint('缓存数据清除完成');
    } catch (e) {
      debugPrint('清除缓存数据失败: $e');
    }
  }

  /// 清除日志数据
  Future<void> _clearLogData(SharedPreferences prefs) async {
    try {
      // 使用DebugLogService清除日志
      final logService = DebugLogService();
      await logService.clearLogs();
      
      // 清除日志相关的存储键
      const logKeys = [
        'debug_logs',
        'debug_log_enabled',
      ];
      
      for (final key in logKeys) {
        await prefs.remove(key);
      }
      
      debugPrint('日志数据清除完成');
    } catch (e) {
      debugPrint('清除日志数据失败: $e');
    }
  }

  /// 清除API设置（但保留服务器配置和模拟登录设置）
  Future<void> _clearApiSettings(SharedPreferences prefs) async {
    try {
      // 注意：不清除以下设置
      // - 服务器配置相关的键：AppConstants.keyServerAddress, keyServerPort, keySavedServers
      // - 模拟登录设置：use_mock_data（需要保留给登录页面使用）

      // 目前没有需要清除的API设置，所有相关设置都需要保留
      debugPrint('API设置清除完成（保留服务器配置和模拟登录设置）');
    } catch (e) {
      debugPrint('清除API设置失败: $e');
    }
  }

  /// 清除用户个人偏好数据（但保留主题和语言设置）
  Future<void> _clearUserPreferences(SharedPreferences prefs) async {
    try {
      // 清除用户个人数据，但保留以下设置：
      // - 用户名、密码、记住密码 (AppConstants.keyUsername, keyPassword, keyRememberPassword)
      // - 主题设置 (selected_theme)
      // - 语言设置 (selected_language)
      // - 服务器配置 (keyServerAddress, keyServerPort, keySavedServers)
      
      // 获取所有键值
      final allKeys = prefs.getKeys();
      
      // 定义需要保留的键值（使用AppConstants中定义的实际键名）
      const keysToKeep = {
        // 登录页面信息
        'username',                    // AppConstants.keyUsername
        'password',                    // AppConstants.keyPassword
        'remember_password',           // AppConstants.keyRememberPassword

        // 主题和语言设置
        'selected_theme',              // 主题设置
        'theme_mode',                  // AppConstants.keyThemeMode
        'selected_language',           // 语言设置

        // 服务器配置
        'server_address',              // AppConstants.keyServerAddress
        'server_port',                 // AppConstants.keyServerPort
        'saved_servers',               // AppConstants.keySavedServers
        'selected_server_name',        // 当前选中的服务器名称

        // 模拟登录设置（需要保留）
        'use_mock_data',               // 全局模拟数据设置
      };
      
      // 清除不在保留列表中的其他用户数据
      for (final key in allKeys) {
        if (!keysToKeep.contains(key) &&
            !key.startsWith('flutter.') && // 不清除Flutter框架的键
            !key.endsWith('_expiry') && // 缓存键已在其他方法中处理
            !key.endsWith('_timestamp') && // 时间戳键已在其他方法中处理
            !['user_token', 'user_info', 'login_time', 'session_valid', 'debug_logs', 'debug_log_enabled', 'force_refresh_on_login'].contains(key)) { // 已在其他方法中处理的键

          await prefs.remove(key);
          debugPrint('清除用户偏好键: $key');
        }
      }
      
      debugPrint('用户偏好数据清除完成');
    } catch (e) {
      debugPrint('清除用户偏好数据失败: $e');
    }
  }

  /// 获取将要保留的数据信息（用于调试）
  Future<Map<String, dynamic>> getRetainedDataInfo() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      return {
        'username': prefs.getString(AppConstants.keyUsername) ?? '',
        'rememberPassword': prefs.getBool(AppConstants.keyRememberPassword) ?? false,
        'serverAddress': prefs.getString(AppConstants.keyServerAddress) ?? '',
        'serverPort': prefs.getString(AppConstants.keyServerPort) ?? '',
        'theme': prefs.getString('selected_theme') ?? 'light',
        'language': prefs.getString('selected_language') ?? 'zh',
        'hasSavedServers': prefs.getString(AppConstants.keySavedServers) != null,
      };
    } catch (e) {
      debugPrint('获取保留数据信息失败: $e');
      return {};
    }
  }

  /// 获取清除的数据统计信息（用于调试）
  Future<Map<String, int>> getClearedDataStats() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final allKeys = prefs.getKeys();
      
      int cacheKeys = 0;
      int authKeys = 0;
      int logKeys = 0;
      int otherKeys = 0;
      
      for (final key in allKeys) {
        if (key.endsWith('_expiry') || key.endsWith('_timestamp') || key.contains('_data')) {
          cacheKeys++;
        } else if (['user_token', 'user_info', 'login_time', 'session_valid'].contains(key)) {
          authKeys++;
        } else if (key.contains('log')) {
          logKeys++;
        } else {
          otherKeys++;
        }
      }
      
      return {
        'totalKeys': allKeys.length,
        'cacheKeys': cacheKeys,
        'authKeys': authKeys,
        'logKeys': logKeys,
        'otherKeys': otherKeys,
      };
    } catch (e) {
      debugPrint('获取清除数据统计失败: $e');
      return {};
    }
  }
}
