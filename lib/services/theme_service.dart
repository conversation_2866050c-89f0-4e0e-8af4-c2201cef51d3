import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../constants/app_theme.dart';
import 'localization_service.dart';

class ThemeService {
  static const String _themeKey = 'selected_theme';
  static const String defaultTheme = 'light';

  static String _currentTheme = defaultTheme;
  static VoidCallback? _onThemeChanged;

  static String get currentTheme => _currentTheme;

  static void setThemeChangeCallback(VoidCallback callback) {
    _onThemeChanged = callback;
  }

  static Future<void> loadTheme() async {
    final prefs = await SharedPreferences.getInstance();
    _currentTheme = prefs.getString(_themeKey) ?? defaultTheme;
  }

  static Future<void> setTheme(String themeMode) async {
    _currentTheme = themeMode;
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_themeKey, themeMode);

    // Notify the app to rebuild with new theme
    _onThemeChanged?.call();
  }

  static ThemeData get currentThemeData {
    switch (_currentTheme) {
      case 'dark':
        return AppTheme.darkTheme;
      case 'light':
      default:
        return AppTheme.lightTheme;
    }
  }

  static ThemeMode get currentThemeMode {
    switch (_currentTheme) {
      case 'dark':
        return ThemeMode.dark;
      case 'light':
      default:
        return ThemeMode.light;
    }
  }

  static String getThemeDisplayName(String themeCode) {
    switch (themeCode) {
      case 'dark':
        return LocalizationService.t('dark_theme');
      case 'light':
      default:
        return LocalizationService.t('light_theme');
    }
  }

  static bool get isDarkMode => _currentTheme == 'dark';
}
