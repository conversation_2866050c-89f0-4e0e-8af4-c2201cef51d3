import 'package:flutter/foundation.dart';

/// API延迟管理器
/// 统一管理所有API调用的最小延迟，确保用户能看到加载状态
class ApiDelayManager {
  // 延迟配置
  static const Map<String, int> _delayConfig = {
    'login': 800,        // 登录接口延迟
    'data': 600,         // 数据接口延迟
    'quick': 300,        // 快速操作延迟
  };

  /// 为API调用添加最小延迟
  /// 
  /// [future] 要执行的API调用
  /// [type] 延迟类型，对应 _delayConfig 中的键
  /// [customDelay] 自定义延迟时间（毫秒），会覆盖配置中的值
  static Future<T> withMinDelay<T>(
    Future<T> future, {
    String type = 'data',
    int? customDelay,
  }) async {
    final delay = customDelay ?? _delayConfig[type] ?? 600;
    
    debugPrint('ApiDelayManager: 执行API调用，类型: $type, 延迟: ${delay}ms');
    
    try {
      final results = await Future.wait([
        future,
        Future.delayed(Duration(milliseconds: delay)),
      ]);
      
      debugPrint('ApiDelayManager: API调用完成，类型: $type');
      return results[0] as T;
    } catch (e) {
      debugPrint('ApiDelayManager: API调用失败，类型: $type, 错误: $e');
      rethrow;
    }
  }

  /// 获取指定类型的延迟时间
  static int getDelay(String type) {
    return _delayConfig[type] ?? 600;
  }

  /// 获取所有延迟配置
  static Map<String, int> getAllDelays() {
    return Map.from(_delayConfig);
  }

  /// 检查是否需要延迟（用于条件延迟）
  /// 
  /// [condition] 是否需要延迟的条件
  /// [type] 延迟类型
  static Future<T> withConditionalDelay<T>(
    Future<T> future, {
    required bool condition,
    String type = 'data',
    int? customDelay,
  }) async {
    if (condition) {
      return withMinDelay(future, type: type, customDelay: customDelay);
    } else {
      return future;
    }
  }
}
