# 鸿蒙企业移动办公系统开发文档 v2.0 (续)

## 6. 数据架构与API设计 (续)

### 6.1.3 消息相关模型
```typescript
// 消息信息模型
interface MessageInfo {
  id: string;
  title: string;
  content: string;
  type: 'system' | 'work' | 'notification' | 'announcement';
  priority: 'low' | 'normal' | 'high' | 'urgent';
  isRead: boolean;
  timestamp: number;
  sender?: {
    id: string;
    name: string;
    avatar?: string;
  };
  attachments?: AttachmentInfo[];
  actions?: MessageAction[];
}

// 消息操作模型
interface MessageAction {
  id: string;
  label: string;
  type: 'link' | 'action' | 'download';
  url?: string;
  actionType?: string;
}

// 附件信息模型
interface AttachmentInfo {
  id: string;
  name: string;
  type: string;
  size: number;
  url: string;
}
```

### 6.1.4 首页相关模型
```typescript
// 轮播图模型
interface BannerInfo {
  id: string;
  title: string;
  imageUrl: string;
  linkUrl?: string;
  linkType: 'none' | 'web' | 'app' | 'page';
  sortOrder: number;
  isActive: boolean;
}

// 新闻资讯模型
interface NewsInfo {
  id: string;
  title: string;
  content: string;
  summary: string;
  imageUrl?: string;
  publishTime: number;
  author: string;
  category: string;
  readCount: number;
  isTop: boolean;
}
```

### 6.2 API接口设计

#### 6.2.1 认证相关接口
```typescript
// 登录接口
POST /api/auth/login
Request: {
  username: string;
  password: string;
  deviceInfo?: {
    deviceId: string;
    deviceName: string;
    osVersion: string;
    appVersion: string;
  };
}
Response: {
  success: boolean;
  message: string;
  data?: {
    token: string;
    refreshToken: string;
    user: UserInfo;
    expiresIn: number;
  };
}

// 刷新Token接口
POST /api/auth/refresh
Request: {
  refreshToken: string;
}
Response: {
  success: boolean;
  data?: {
    token: string;
    expiresIn: number;
  };
}

// 登出接口
POST /api/auth/logout
Request: {
  token: string;
}
Response: {
  success: boolean;
  message: string;
}

// 服务器连接测试接口
GET /api/system/ping
Response: {
  success: boolean;
  message: string;
  serverTime: number;
  version: string;
}
```

#### 6.2.2 用户相关接口
```typescript
// 获取用户信息
GET /api/user/profile
Headers: {
  Authorization: "Bearer {token}"
}
Response: {
  success: boolean;
  data?: UserInfo;
}

// 更新用户信息
PUT /api/user/profile
Request: {
  name?: string;
  avatar?: string;
  phone?: string;
  email?: string;
}
Response: {
  success: boolean;
  message: string;
  data?: UserInfo;
}

// 修改密码
POST /api/user/change-password
Request: {
  oldPassword: string;
  newPassword: string;
}
Response: {
  success: boolean;
  message: string;
}
```

#### 6.2.3 应用相关接口
```typescript
// 获取应用列表
GET /api/apps/list
Query: {
  category?: string;
  keyword?: string;
  page?: number;
  pageSize?: number;
}
Response: {
  success: boolean;
  data?: {
    apps: AppInfo[];
    total: number;
    categories: AppCategory[];
  };
}

// 获取常用应用
GET /api/apps/favorites
Response: {
  success: boolean;
  data?: AppInfo[];
}

// 设置常用应用
POST /api/apps/favorites
Request: {
  appIds: string[];
}
Response: {
  success: boolean;
  message: string;
}

// 应用排序
POST /api/apps/sort
Request: {
  apps: Array<{
    id: string;
    sortOrder: number;
  }>;
}
Response: {
  success: boolean;
  message: string;
}
```

#### 6.2.4 首页相关接口
```typescript
// 获取首页数据
GET /api/home/<USER>
Response: {
  success: boolean;
  data?: {
    banners: BannerInfo[];
    news: NewsInfo[];
    quickActions: AppInfo[];
    statistics?: {
      totalApps: number;
      unreadMessages: number;
      pendingTasks: number;
    };
  };
}

// 获取轮播图
GET /api/home/<USER>
Response: {
  success: boolean;
  data?: BannerInfo[];
}

// 获取新闻列表
GET /api/home/<USER>
Query: {
  page?: number;
  pageSize?: number;
  category?: string;
}
Response: {
  success: boolean;
  data?: {
    news: NewsInfo[];
    total: number;
  };
}

// 获取新闻详情
GET /api/home/<USER>/{id}
Response: {
  success: boolean;
  data?: NewsInfo;
}
```

#### 6.2.5 消息相关接口
```typescript
// 获取消息列表
GET /api/messages/list
Query: {
  type?: 'all' | 'unread' | 'system' | 'work';
  page?: number;
  pageSize?: number;
}
Response: {
  success: boolean;
  data?: {
    messages: MessageInfo[];
    total: number;
    unreadCount: number;
  };
}

// 标记消息已读
POST /api/messages/mark-read
Request: {
  messageIds: string[];
}
Response: {
  success: boolean;
  message: string;
}

// 删除消息
DELETE /api/messages/{id}
Response: {
  success: boolean;
  message: string;
}

// 获取未读消息数量
GET /api/messages/unread-count
Response: {
  success: boolean;
  data?: {
    total: number;
    system: number;
    work: number;
  };
}
```

### 6.3 数据存储设计

#### 6.3.1 本地存储策略
**Preferences存储**:
- 用户设置信息
- 登录状态和Token
- 应用配置信息
- 主题和语言设置

**关系型数据库存储**:
- 应用信息缓存
- 消息数据缓存
- 新闻数据缓存
- 用户操作日志

**文件存储**:
- 图片缓存
- 文档缓存
- 日志文件
- 临时文件

#### 6.3.2 缓存策略设计
**内存缓存**:
- 用户信息：应用运行期间
- 应用列表：30分钟
- 消息列表：15分钟
- 图片缓存：LRU策略，最大100MB

**磁盘缓存**:
- 新闻数据：24小时
- 轮播图：12小时
- 应用图标：7天
- 用户头像：7天

**缓存更新策略**:
- 主动刷新：用户下拉刷新
- 被动刷新：数据过期自动刷新
- 增量更新：支持增量数据同步
- 离线模式：显示缓存数据

### 6.4 数据同步机制

#### 6.4.1 实时同步
**WebSocket连接**:
- 消息推送
- 状态同步
- 在线状态
- 系统通知

**推送通知**:
- 新消息提醒
- 系统公告
- 应用更新
- 安全提醒

#### 6.4.2 离线支持
**离线数据**:
- 基本用户信息
- 常用应用列表
- 最近消息记录
- 缓存的新闻内容

**数据同步**:
- 网络恢复时自动同步
- 冲突解决策略
- 增量同步机制
- 同步状态提示

---

## 7. 状态管理深度设计

### 7.1 状态管理架构

#### 7.1.1 状态分层设计
```typescript
// 全局状态
interface GlobalState {
  user: UserState;
  app: AppState;
  theme: ThemeState;
  network: NetworkState;
}

// 用户状态
interface UserState {
  isLoggedIn: boolean;
  userInfo: UserInfo | null;
  token: string | null;
  permissions: string[];
}

// 应用状态
interface AppState {
  isLoading: boolean;
  error: string | null;
  currentRoute: string;
  unreadCount: number;
}

// 主题状态
interface ThemeState {
  isDarkMode: boolean;
  primaryColor: string;
  language: string;
}

// 网络状态
interface NetworkState {
  isOnline: boolean;
  networkType: string;
  isSlowNetwork: boolean;
}
```

#### 7.1.2 状态管理实现
```typescript
// 状态管理器基类
abstract class StateManager<T> {
  protected state: T;
  protected listeners: Set<(state: T) => void> = new Set();

  constructor(initialState: T) {
    this.state = initialState;
  }

  getState(): T {
    return this.state;
  }

  setState(newState: Partial<T>): void {
    this.state = { ...this.state, ...newState };
    this.notifyListeners();
  }

  subscribe(listener: (state: T) => void): () => void {
    this.listeners.add(listener);
    return () => this.listeners.delete(listener);
  }

  private notifyListeners(): void {
    this.listeners.forEach(listener => listener(this.state));
  }
}

// 用户状态管理器
class UserStateManager extends StateManager<UserState> {
  constructor() {
    super({
      isLoggedIn: false,
      userInfo: null,
      token: null,
      permissions: []
    });
  }

  async login(credentials: LoginRequest): Promise<void> {
    this.setState({ isLoading: true });
    try {
      const response = await AuthService.login(credentials);
      this.setState({
        isLoggedIn: true,
        userInfo: response.data.user,
        token: response.data.token,
        isLoading: false
      });
    } catch (error) {
      this.setState({
        isLoading: false,
        error: error.message
      });
      throw error;
    }
  }

  logout(): void {
    this.setState({
      isLoggedIn: false,
      userInfo: null,
      token: null,
      permissions: []
    });
  }
}
```

### 7.2 页面级状态管理

#### 7.2.1 ViewModel模式
```typescript
// ViewModel基类
abstract class BaseViewModel {
  protected isLoading: boolean = false;
  protected error: string | null = null;
  protected listeners: Set<() => void> = new Set();

  protected setLoading(loading: boolean): void {
    this.isLoading = loading;
    this.notifyChange();
  }

  protected setError(error: string | null): void {
    this.error = error;
    this.notifyChange();
  }

  subscribe(listener: () => void): () => void {
    this.listeners.add(listener);
    return () => this.listeners.delete(listener);
  }

  private notifyChange(): void {
    this.listeners.forEach(listener => listener());
  }

  abstract dispose(): void;
}

// 首页ViewModel
class HomeViewModel extends BaseViewModel {
  private banners: BannerInfo[] = [];
  private news: NewsInfo[] = [];
  private currentBannerIndex: number = 0;

  async loadData(): Promise<void> {
    this.setLoading(true);
    try {
      const response = await HomeService.getHomeData();
      this.banners = response.data.banners;
      this.news = response.data.news;
      this.setError(null);
    } catch (error) {
      this.setError(error.message);
    } finally {
      this.setLoading(false);
    }
  }

  async refreshData(): Promise<void> {
    try {
      const response = await HomeService.getHomeData();
      this.banners = response.data.banners;
      this.news = response.data.news;
      this.setError(null);
    } catch (error) {
      this.setError(error.message);
    }
  }

  getBanners(): BannerInfo[] {
    return this.banners;
  }

  getNews(): NewsInfo[] {
    return this.news;
  }

  getCurrentBannerIndex(): number {
    return this.currentBannerIndex;
  }

  setCurrentBannerIndex(index: number): void {
    this.currentBannerIndex = index;
    this.notifyChange();
  }

  dispose(): void {
    this.listeners.clear();
  }
}
```

### 7.3 组件状态管理

#### 7.3.1 组件状态装饰器
```typescript
// 状态装饰器使用示例
@Component
struct HomePage {
  @State private isLoading: boolean = false;
  @State private banners: BannerInfo[] = [];
  @State private news: NewsInfo[] = [];
  @State private currentBannerIndex: number = 0;

  private homeViewModel: HomeViewModel = new HomeViewModel();

  aboutToAppear() {
    this.homeViewModel.subscribe(() => {
      this.isLoading = this.homeViewModel.isLoading;
      this.banners = this.homeViewModel.getBanners();
      this.news = this.homeViewModel.getNews();
      this.currentBannerIndex = this.homeViewModel.getCurrentBannerIndex();
    });

    this.homeViewModel.loadData();
  }

  aboutToDisappear() {
    this.homeViewModel.dispose();
  }

  build() {
    Column() {
      if (this.isLoading) {
        LoadingComponent()
      } else {
        BannerCarousel({
          banners: this.banners,
          currentIndex: this.currentBannerIndex,
          onIndexChange: (index: number) => {
            this.homeViewModel.setCurrentBannerIndex(index);
          }
        })

        NewsSection({
          news: this.news,
          onRefresh: () => {
            this.homeViewModel.refreshData();
          }
        })
      }
    }
  }
}
```

---

## 8. 性能优化与监控

### 8.1 渲染性能优化

#### 8.1.1 列表性能优化
**LazyForEach优化**:
- 使用LazyForEach进行列表虚拟化
- 合理设置缓存数量
- 避免在itemGenerator中进行复杂计算
- 使用稳定的keyGenerator

**图片加载优化**:
- 图片懒加载机制
- 图片尺寸适配
- WebP格式支持
- 图片缓存策略

**布局优化**:
- 避免深层嵌套布局
- 使用Flex布局替代复杂嵌套
- 合理使用@Builder装饰器
- 避免不必要的重新渲染

#### 8.1.2 动画性能优化
**动画实现策略**:
- 使用硬件加速动画
- 避免在动画中修改布局
- 使用transform替代位置变化
- 合理设置动画时长和缓动函数

**交互动画优化**:
- 点击反馈动画优化
- 页面切换动画优化
- 滚动动画性能优化
- 减少动画层级

### 8.2 内存管理优化

#### 8.2.1 内存泄漏防护
**资源管理**:
- 及时释放不用的资源
- 正确管理事件监听器
- 避免循环引用
- 合理使用WeakRef

**图片内存优化**:
- 图片内存池管理
- 大图片分块加载
- 及时回收图片资源
- 内存警告处理

#### 8.2.2 缓存策略优化
**内存缓存**:
- LRU缓存算法
- 缓存大小限制
- 缓存命中率监控
- 内存压力自适应

**磁盘缓存**:
- 缓存文件管理
- 过期数据清理
- 缓存空间限制
- 缓存压缩策略

### 8.3 网络性能优化

#### 8.3.1 请求优化
**请求合并**:
- 批量请求接口
- 请求去重机制
- 请求优先级管理
- 并发请求控制

**数据压缩**:
- Gzip压缩支持
- 图片压缩传输
- JSON数据压缩
- 增量数据传输

#### 8.3.2 离线策略
**离线缓存**:
- 关键数据离线存储
- 离线操作队列
- 网络恢复同步
- 离线状态提示

### 8.4 启动性能优化

#### 8.4.1 冷启动优化
**启动流程优化**:
- 减少启动时的初始化工作
- 异步加载非关键资源
- 启动页面优化
- 预加载关键数据

**资源预加载**:
- 关键图片预加载
- 字体资源预加载
- 配置数据预加载
- 用户数据预加载

#### 8.4.2 热启动优化
**状态恢复**:
- 页面状态保持
- 用户操作状态恢复
- 网络请求状态恢复
- 滚动位置恢复

### 8.5 性能监控体系

#### 8.5.1 性能指标监控
**关键指标**:
- 应用启动时间
- 页面渲染时间
- 网络请求耗时
- 内存使用情况
- CPU使用率
- 帧率监控

**监控实现**:
```typescript
class PerformanceMonitor {
  private static instance: PerformanceMonitor;

  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor();
    }
    return PerformanceMonitor.instance;
  }

  // 启动时间监控
  recordStartupTime(startTime: number, endTime: number): void {
    const duration = endTime - startTime;
    this.reportMetric('startup_time', duration);
  }

  // 页面渲染时间监控
  recordPageRenderTime(pageName: string, renderTime: number): void {
    this.reportMetric('page_render_time', renderTime, { page: pageName });
  }

  // 网络请求监控
  recordNetworkRequest(url: string, duration: number, success: boolean): void {
    this.reportMetric('network_request', duration, {
      url,
      success: success.toString()
    });
  }

  // 内存使用监控
  recordMemoryUsage(usage: number): void {
    this.reportMetric('memory_usage', usage);
  }

  private reportMetric(name: string, value: number, tags?: Record<string, string>): void {
    // 上报性能数据到监控系统
    console.log(`Performance Metric: ${name} = ${value}`, tags);
  }
}
```

#### 8.5.2 异常监控
**异常捕获**:
- 全局异常处理
- 网络异常监控
- 渲染异常监控
- 业务异常监控

**异常上报**:
- 异常信息收集
- 异常堆栈分析
- 异常频率统计
- 异常影响评估

---

## 9. 安全架构设计

### 9.1 数据安全

#### 9.1.1 敏感数据保护
**数据加密**:
- 用户密码加密存储
- Token安全管理
- 本地数据库加密
- 网络传输加密

**加密实现**:
```typescript
class EncryptionUtil {
  private static readonly AES_KEY_SIZE = 256;
  private static readonly IV_SIZE = 16;

  // AES加密
  static encrypt(data: string, key: string): string {
    // 实现AES加密逻辑
    return encryptedData;
  }

  // AES解密
  static decrypt(encryptedData: string, key: string): string {
    // 实现AES解密逻辑
    return decryptedData;
  }

  // 生成随机密钥
  static generateKey(): string {
    // 生成随机密钥
    return randomKey;
  }

  // 密码哈希
  static hashPassword(password: string, salt: string): string {
    // 使用PBKDF2或bcrypt进行密码哈希
    return hashedPassword;
  }
}
```

#### 9.1.2 Token管理
**Token安全策略**:
- JWT Token使用
- Token过期机制
- Refresh Token轮换
- Token撤销机制

**Token管理实现**:
```typescript
class TokenManager {
  private static readonly TOKEN_KEY = 'access_token';
  private static readonly REFRESH_TOKEN_KEY = 'refresh_token';

  static async saveToken(token: string, refreshToken: string): Promise<void> {
    // 加密存储Token
    const encryptedToken = EncryptionUtil.encrypt(token, this.getDeviceKey());
    const encryptedRefreshToken = EncryptionUtil.encrypt(refreshToken, this.getDeviceKey());

    await PreferencesManager.setString(this.TOKEN_KEY, encryptedToken);
    await PreferencesManager.setString(this.REFRESH_TOKEN_KEY, encryptedRefreshToken);
  }

  static async getToken(): Promise<string | null> {
    const encryptedToken = await PreferencesManager.getString(this.TOKEN_KEY);
    if (!encryptedToken) return null;

    return EncryptionUtil.decrypt(encryptedToken, this.getDeviceKey());
  }

  static async refreshToken(): Promise<string | null> {
    const refreshToken = await this.getRefreshToken();
    if (!refreshToken) return null;

    try {
      const response = await AuthService.refreshToken(refreshToken);
      await this.saveToken(response.token, response.refreshToken);
      return response.token;
    } catch (error) {
      await this.clearTokens();
      throw error;
    }
  }

  static async clearTokens(): Promise<void> {
    await PreferencesManager.remove(this.TOKEN_KEY);
    await PreferencesManager.remove(this.REFRESH_TOKEN_KEY);
  }

  private static getDeviceKey(): string {
    // 生成设备相关的密钥
    return deviceKey;
  }
}
```