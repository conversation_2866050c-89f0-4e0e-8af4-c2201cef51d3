import 'package:flutter/material.dart';
import 'lib/services/auth_service.dart';
import 'lib/services/cache_service.dart';
import 'lib/services/data_cleanup_service.dart';

/// 测试强制刷新功能的脚本
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  print('=== 开始测试强制刷新功能 ===');
  
  // 1. 测试AuthService的强制刷新标记功能
  await testAuthServiceForceRefresh();
  
  // 2. 测试CacheService的强制清理功能
  await testCacheServiceForceClean();
  
  // 3. 测试DataCleanupService的数据清理功能
  await testDataCleanupService();
  
  // 4. 测试完整的退出登录流程
  await testCompleteLogoutFlow();
  
  print('=== 所有测试完成 ===');
}

/// 测试AuthService的强制刷新标记功能
Future<void> testAuthServiceForceRefresh() async {
  print('\n--- 测试AuthService强制刷新标记 ---');
  
  final authService = AuthService();
  
  // 测试初始状态
  bool shouldRefresh = await authService.shouldForceRefresh();
  print('初始强制刷新状态: $shouldRefresh');
  
  // 测试设置强制刷新标记（通过clearAllUserData触发）
  print('执行clearAllUserData...');
  await authService.clearAllUserData();
  
  // 检查强制刷新标记是否被设置
  shouldRefresh = await authService.shouldForceRefresh();
  print('清理后强制刷新状态: $shouldRefresh');
  
  if (shouldRefresh) {
    print('✅ 强制刷新标记设置成功');
  } else {
    print('❌ 强制刷新标记设置失败');
  }
  
  // 测试清除强制刷新标记
  await authService.clearForceRefreshFlag();
  shouldRefresh = await authService.shouldForceRefresh();
  print('清除标记后状态: $shouldRefresh');
  
  if (!shouldRefresh) {
    print('✅ 强制刷新标记清除成功');
  } else {
    print('❌ 强制刷新标记清除失败');
  }
}

/// 测试CacheService的强制清理功能
Future<void> testCacheServiceForceClean() async {
  print('\n--- 测试CacheService强制清理功能 ---');
  
  // 先添加一些测试缓存数据
  await CacheService.saveHomeData({
    'banners': [{'title': 'test banner'}],
    'news': [{'title': 'test news'}]
  });
  
  await CacheService.saveAppsData({
    'categories': [{'title': 'test category'}]
  });
  
  // 检查缓存数据是否存在
  var homeData = await CacheService.getHomeData();
  var appsData = await CacheService.getAppsData();
  
  print('添加缓存后 - 首页数据: ${homeData != null ? '存在' : '不存在'}');
  print('添加缓存后 - 应用数据: ${appsData != null ? '存在' : '不存在'}');
  
  // 执行强制清理
  print('执行强制清理所有页面缓存...');
  await CacheService.forceCleanAllPageCache();
  
  // 检查缓存是否被清理
  homeData = await CacheService.getHomeData();
  appsData = await CacheService.getAppsData();
  
  print('强制清理后 - 首页数据: ${homeData != null ? '存在' : '不存在'}');
  print('强制清理后 - 应用数据: ${appsData != null ? '存在' : '不存在'}');
  
  if (homeData == null && appsData == null) {
    print('✅ 缓存强制清理成功');
  } else {
    print('❌ 缓存强制清理失败');
  }
}

/// 测试DataCleanupService的数据清理功能
Future<void> testDataCleanupService() async {
  print('\n--- 测试DataCleanupService数据清理功能 ---');
  
  final cleanupService = DataCleanupService();
  
  // 模拟一些需要保留的登录页面数据
  // 这里只是测试，实际数据会在真实使用中设置
  print('执行完整数据清理...');
  await cleanupService.clearAllUserData();
  
  print('✅ 数据清理服务执行完成');
}

/// 测试完整的退出登录流程
Future<void> testCompleteLogoutFlow() async {
  print('\n--- 测试完整退出登录流程 ---');
  
  final authService = AuthService();
  
  // 模拟登录状态
  await authService.saveLoginInfo(
    token: 'test_token',
    userInfo: '{"id": 1, "name": "test"}',
    username: 'testuser',
    password: 'testpass',
    rememberPassword: true,
  );
  
  // 检查登录状态
  bool isLoggedIn = await authService.isLoggedIn();
  print('模拟登录后状态: ${isLoggedIn ? '已登录' : '未登录'}');
  
  // 执行完整的退出登录流程
  print('执行完整退出登录流程...');
  await authService.clearAllUserData();
  
  // 检查登录状态
  isLoggedIn = await authService.isLoggedIn();
  print('退出登录后状态: ${isLoggedIn ? '已登录' : '未登录'}');
  
  // 检查强制刷新标记
  bool shouldRefresh = await authService.shouldForceRefresh();
  print('退出登录后强制刷新标记: $shouldRefresh');
  
  if (!isLoggedIn && shouldRefresh) {
    print('✅ 完整退出登录流程测试成功');
    print('  - 用户已退出登录');
    print('  - 强制刷新标记已设置');
    print('  - 下次登录时将强制刷新所有数据');
  } else {
    print('❌ 完整退出登录流程测试失败');
  }
}
