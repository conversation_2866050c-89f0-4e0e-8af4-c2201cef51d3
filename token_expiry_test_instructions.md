# Token过期自动退出登录测试说明

## 问题现象
用户反馈：切换底部标签菜单时，显示"无效的凭据"提示，但没有自动退出登录。

## 修复内容
已在所有API接口中增加业务层token过期检测，确保即使后端返回200状态码但包含token相关错误信息时，也能正确触发token过期处理。

## 测试方法

### 方法1: 模拟后端返回token过期错误
1. 临时修改后端API，让其返回包含"invalid_credentials"或"unauthorized"的错误信息
2. 切换到工作台页面
3. 观察是否显示token过期对话框并自动跳转到登录页面

### 方法2: 使用调试代码测试
在`lib/services/api_service.dart`的`getWorkspaceApps`方法中临时添加测试代码：

```dart
// 在第1026行左右，真实接口请求之前添加：
if (shouldUseMockData == false) {
  // 临时测试：模拟token过期错误
  final tokenHandler = TokenExpiryHandler();
  if (context != null && context.mounted) {
    await tokenHandler.handleTokenExpiry(context);
  }
  return {
    'success': false,
    'message': LocalizationService.t('token_expired'),
    'isTokenExpired': true,
  };
}
```

### 方法3: 修改TokenExpiryHandler检测逻辑
临时修改`lib/services/token_expiry_handler.dart`的`isTokenExpiredError`方法，让其对所有错误都返回true：

```dart
bool isTokenExpiredError(dynamic error) {
  // 临时测试：对所有错误都认为是token过期
  return true;
}
```

## 预期结果
1. **显示友好对话框**: 出现"登录已过期"对话框
2. **自动清理数据**: 清除所有用户数据和缓存
3. **跳转到登录页**: 点击对话框按钮后跳转到登录页面
4. **不显示重复错误**: 不会再显示"无效的凭据"等其他错误信息

## 验证步骤
1. 启动应用并登录
2. 切换到工作台页面
3. 触发token过期情况（使用上述测试方法之一）
4. 验证是否按预期显示对话框并跳转

## 回滚测试代码
测试完成后，记得移除或注释掉临时添加的测试代码，恢复正常功能。

## 关键改进点
- **双层检测**: HTTP状态码 + 业务层错误信息
- **统一处理**: 所有API接口都支持业务层token过期检测
- **用户友好**: 统一的错误提示和处理流程
- **防重复**: 避免显示多个错误信息

## 涉及的文件
- `lib/services/api_service.dart` - 增加业务层token检测
- `lib/services/token_expiry_handler.dart` - 改进错误检测逻辑
- `lib/services/http_client.dart` - 支持401/403状态码检测
- 所有页面文件 - 正确处理isTokenExpired标记
