# Token 认证修复说明

## 问题描述

在实现模拟/真实接口切换功能时，发现部分接口在调用真实后端API时缺少正确的认证Token，导致查询失败。

## 问题原因

1. **Token存储键不一致**：ApiService中使用的token存储键与AuthService不一致
2. **认证头获取方式错误**：直接使用SharedPreferences而不是通过AuthService获取token

## 修复内容

### 1. 修复认证头获取方法

**修复前**：
```dart
static Future<Map<String, String>> _getAuthHeaders() async {
  final prefs = await SharedPreferences.getInstance();
  final token = prefs.getString('auth_token') ?? ''; // 错误的存储键
  
  return {
    'Authorization': 'Bearer $token',
    'Content-Type': 'application/json',
  };
}
```

**修复后**：
```dart
static Future<Map<String, String>> _getAuthHeaders() async {
  // 使用 AuthService 获取 token，确保使用正确的存储键
  final authService = AuthService();
  final token = await authService.getUserToken() ?? '';
  
  // 调试信息：打印 token 状态
  print('ApiService: 获取认证头 - Token: ${token.isEmpty ? "空" : "已获取(${token.length}字符)"}');
  
  return {
    'Authorization': 'Bearer $token',
    'Content-Type': 'application/json',
  };
}
```

### 2. 添加调试信息

为了便于排查问题，在真实接口调用时添加了调试信息：

```dart
// 真实接口请求
final baseUrl = await getBaseUrl();
final headers = await _getAuthHeaders();
print('ApiService: 调用首页接口 - URL: $baseUrl/home/<USER>');
print('ApiService: 请求头: $headers');

final response = await _httpClient.get(
  '$baseUrl/home/<USER>',
  headers: headers,
);
```

### 3. 确保所有需要认证的接口都正确添加认证头

根据后端API文档，以下接口需要认证：

✅ **已修复的接口**：
- `getHomeInfo()` - 首页信息
- `getAppList()` - 应用列表  
- `getMessages()` - 消息列表
- `getWorkspaceApps()` - 工作台应用
- `saveFavoriteApps()` - 保存常用应用
- `saveAppSort()` - 保存应用排序

❌ **不需要认证的接口**：
- `login()` - 登录接口
- `testConnection()` - 系统ping接口

## 验证方法

### 1. 模拟登录测试
1. 启动应用，保持"使用模拟登录"勾选状态
2. 使用 admin/123456 登录
3. 查看控制台输出，应该看到token获取成功的信息
4. 进入主页面，验证各个功能正常

### 2. 真实接口测试
1. 取消勾选"使用模拟登录"
2. 配置正确的服务器地址
3. 使用真实用户名密码登录
4. 查看控制台输出：
   - 应该看到token获取成功的信息
   - 应该看到真实接口调用的URL和请求头信息
5. 进入主页面，验证各个功能是否正常调用后端API

### 3. 调试信息说明

**正常情况下的控制台输出**：
```
ApiService: 获取认证头 - Token: 已获取(36字符)
ApiService: 调用首页接口 - URL: http://localhost:8080/api/home/<USER>
ApiService: 请求头: {Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..., Content-Type: application/json}
```

**异常情况下的控制台输出**：
```
ApiService: 获取认证头 - Token: 空
ApiService: 调用首页接口 - URL: http://localhost:8080/api/home/<USER>
ApiService: 请求头: {Authorization: Bearer , Content-Type: application/json}
```

## 后端API要求

### 认证头格式
```
Authorization: Bearer <JWT_TOKEN>
Content-Type: application/json
```

### 需要认证的端点
- `GET /api/home/<USER>
- `GET /api/apps/list` - 应用列表
- `GET /api/messages/list` - 消息列表
- `GET /api/workspace/apps` - 工作台应用
- `POST /api/apps/favorites` - 保存常用应用
- `POST /api/apps/sort` - 保存应用排序

### 不需要认证的端点
- `POST /api/auth/login` - 用户登录
- `GET /api/system/ping` - 系统状态检查

## 注意事项

1. **Token有效期**：确保后端返回的JWT token有足够的有效期
2. **Token刷新**：如果token过期，需要重新登录获取新token
3. **错误处理**：401错误通常表示token无效或过期
4. **调试模式**：生产环境应该移除调试信息的print语句
5. **网络环境**：确保客户端能够访问配置的服务器地址

## 测试建议

1. **先测试模拟模式**：确保UI和逻辑正常
2. **再测试真实模式**：验证与后端的集成
3. **检查网络连接**：确保服务器地址配置正确
4. **查看控制台日志**：通过调试信息排查问题
5. **测试不同场景**：登录、数据获取、操作等各个环节

## 常见问题排查

### 问题1：Token为空
- 检查是否成功登录
- 检查AuthService的token存储逻辑
- 确认登录接口返回了正确的token

### 问题2：401认证失败
- 检查token格式是否正确
- 检查token是否过期
- 检查后端API的认证逻辑

### 问题3：网络连接失败
- 检查服务器地址配置
- 检查网络连接状态
- 检查防火墙设置

### 问题4：接口返回错误
- 检查请求URL是否正确
- 检查请求参数格式
- 检查后端API实现
