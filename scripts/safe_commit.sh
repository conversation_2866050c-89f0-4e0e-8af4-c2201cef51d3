#!/bin/bash

# 安全提交脚本 - 在提交前执行检查

# 检查是否提供了提交信息
if [ -z "$1" ]; then
    echo "❌ 错误：请提供提交信息"
    echo "用法: ./scripts/safe_commit.sh \"提交信息\""
    echo "例如: ./scripts/safe_commit.sh \"feat(ui): 修复设置页面显示问题\""
    exit 1
fi

commit_msg="$1"

echo "🚀 开始安全提交流程..."
echo "提交信息: $commit_msg"
echo ""

# 显示当前状态
echo "📋 当前Git状态:"
git status --short
echo ""

# 执行提交前检查
echo "🔍 执行提交前检查..."
if ! ./scripts/pre_commit_check.sh "$commit_msg"; then
    echo "❌ 提交前检查失败，请修复问题后重试"
    exit 1
fi

echo ""
echo "📝 准备提交的文件:"
git diff --cached --name-only
echo ""

# 确认提交
read -p "确认提交这些更改？(y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ 提交已取消"
    exit 1
fi

# 执行提交
echo "💾 执行提交..."
if git commit -m "$commit_msg"; then
    echo "✅ 提交成功！"
    echo ""
    echo "📤 是否推送到远程仓库？"
    read -p "推送到远程？(y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo "🌐 推送到远程仓库..."
        if git push; then
            echo "✅ 推送成功！"
        else
            echo "❌ 推送失败，请检查网络连接和权限"
            exit 1
        fi
    else
        echo "ℹ️  提交已保存在本地，稍后可以运行 'git push' 推送"
    fi
else
    echo "❌ 提交失败"
    exit 1
fi

echo ""
echo "🎉 操作完成！"
