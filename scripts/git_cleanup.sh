#!/bin/bash

# Git 清理脚本 - 移除不应该被跟踪的文件
# 运行此脚本前请确保已经备份重要文件

echo "开始清理 Git 仓库中不应该被跟踪的文件..."

# 移除构建文件
echo "移除构建文件..."
git rm -r --cached build/ 2>/dev/null || echo "build/ 目录未被跟踪"
git rm -r --cached .dart_tool/ 2>/dev/null || echo ".dart_tool/ 目录未被跟踪"

# 移除 Flutter 生成的文件
echo "移除 Flutter 生成的文件..."
git rm --cached .flutter-plugins 2>/dev/null || echo ".flutter-plugins 未被跟踪"
git rm --cached .flutter-plugins-dependencies 2>/dev/null || echo ".flutter-plugins-dependencies 未被跟踪"
git rm --cached .packages 2>/dev/null || echo ".packages 未被跟踪"
git rm --cached pubspec.lock 2>/dev/null || echo "pubspec.lock 未被跟踪"

# 移除 IDE 文件
echo "移除 IDE 文件..."
git rm --cached flutter_hybrid_app.iml 2>/dev/null || echo "flutter_hybrid_app.iml 未被跟踪"
git rm --cached android/flutter_hybrid_app_android.iml 2>/dev/null || echo "android/flutter_hybrid_app_android.iml 未被跟踪"
git rm -r --cached .vscode/ 2>/dev/null || echo ".vscode/ 目录未被跟踪"
git rm -r --cached .idea/ 2>/dev/null || echo ".idea/ 目录未被跟踪"

# 移除操作系统生成的文件
echo "移除操作系统生成的文件..."
git rm --cached .DS_Store 2>/dev/null || echo ".DS_Store 未被跟踪"
find . -name ".DS_Store" -exec git rm --cached {} \; 2>/dev/null || echo "没有找到其他 .DS_Store 文件"

# 移除 iOS 构建文件
echo "移除 iOS 构建文件..."
git rm --cached ios/Podfile.lock 2>/dev/null || echo "ios/Podfile.lock 未被跟踪"
git rm -r --cached ios/Pods/ 2>/dev/null || echo "ios/Pods/ 目录未被跟踪"

# 移除 Android 本地配置文件
echo "移除 Android 本地配置文件..."
git rm --cached android/local.properties 2>/dev/null || echo "android/local.properties 未被跟踪"

# 移除日志文件
echo "移除日志文件..."
find . -name "*.log" -exec git rm --cached {} \; 2>/dev/null || echo "没有找到日志文件"

echo "清理完成！"
echo ""
echo "请运行以下命令来提交更改："
echo "git add .gitignore"
echo "git commit -m 'Update .gitignore and remove tracked build files'"
echo ""
echo "注意：如果某些文件仍然显示在 git status 中，可能需要手动移除："
echo "git rm --cached <文件名>"
