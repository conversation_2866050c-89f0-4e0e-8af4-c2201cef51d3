#!/bin/bash

# 提交前检查脚本
# 检查是否有不应该提交的文件

echo "🔍 执行提交前检查..."

# 检查是否有构建文件被暂存
echo "检查构建文件..."
if git diff --cached --name-only | grep -E "(build/|\.dart_tool/)" > /dev/null; then
    echo "❌ 错误：检测到构建文件被暂存，请移除："
    git diff --cached --name-only | grep -E "(build/|\.dart_tool/)"
    echo "运行: git reset HEAD <文件名> 来取消暂存"
    exit 1
fi

# 检查是否有IDE文件被暂存
echo "检查IDE文件..."
if git diff --cached --name-only | grep -E "(\.iml$|\.vscode/|\.idea/)" > /dev/null; then
    echo "❌ 错误：检测到IDE文件被暂存，请移除："
    git diff --cached --name-only | grep -E "(\.iml$|\.vscode/|\.idea/)"
    echo "运行: git reset HEAD <文件名> 来取消暂存"
    exit 1
fi

# 检查是否有日志文件被暂存
echo "检查日志文件..."
if git diff --cached --name-only | grep -E "\.log$" > /dev/null; then
    echo "❌ 错误：检测到日志文件被暂存，请移除："
    git diff --cached --name-only | grep -E "\.log$"
    echo "运行: git reset HEAD <文件名> 来取消暂存"
    exit 1
fi

# 检查是否有操作系统文件被暂存
echo "检查操作系统文件..."
if git diff --cached --name-only | grep -E "(\.DS_Store|Thumbs\.db)" > /dev/null; then
    echo "❌ 错误：检测到操作系统文件被暂存，请移除："
    git diff --cached --name-only | grep -E "(\.DS_Store|Thumbs\.db)"
    echo "运行: git reset HEAD <文件名> 来取消暂存"
    exit 1
fi

# 检查是否有大文件（超过10MB）
echo "检查大文件..."
large_files=$(git diff --cached --name-only | xargs -I {} sh -c 'if [ -f "{}" ] && [ $(stat -f%z "{}" 2>/dev/null || stat -c%s "{}" 2>/dev/null || echo 0) -gt 10485760 ]; then echo "{}"; fi')
if [ ! -z "$large_files" ]; then
    echo "⚠️  警告：检测到大文件（>10MB）："
    echo "$large_files"
    echo "考虑使用 Git LFS 或将文件添加到 .gitignore"
    read -p "是否继续提交？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# 检查是否有敏感文件模式
echo "检查敏感文件..."
if git diff --cached --name-only | grep -E "(\.env|\.key|\.pem|\.keystore|\.jks|secrets)" > /dev/null; then
    echo "⚠️  警告：检测到可能包含敏感信息的文件："
    git diff --cached --name-only | grep -E "(\.env|\.key|\.pem|\.keystore|\.jks|secrets)"
    read -p "确认这些文件不包含敏感信息？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# 检查提交信息格式（如果提供了提交信息）
if [ ! -z "$1" ]; then
    commit_msg="$1"
    if ! echo "$commit_msg" | grep -E "^(feat|fix|docs|style|refactor|test|chore)(\(.+\))?: .+" > /dev/null; then
        echo "⚠️  警告：提交信息格式不符合规范"
        echo "建议格式: <类型>(<范围>): <描述>"
        echo "例如: feat(auth): 添加用户登录功能"
        read -p "是否继续提交？(y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
fi

echo "✅ 检查通过！可以安全提交。"
exit 0
