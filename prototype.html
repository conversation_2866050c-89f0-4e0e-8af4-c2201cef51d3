<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>企业移动办公App原型</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #3366CC;
            --secondary-color: #FF9900;
            --bg-color: #F5F7FA;
            --success-color: #52C41A;
            --warning-color: #FAAD14;
            --error-color: #F5222D;
            --text-primary: #333333;
            --text-secondary: #666666;
            --text-tertiary: #999999;
            --border-color: #E8E8E8;
            --white: #FFFFFF;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: -apple-system, BlinkMacSystemFont, "SF Pro Text", "Segoe UI", <PERSON><PERSON>, sans-serif;
        }
        
        body {
            background-color: #f0f0f0;
            color: var(--text-primary);
            line-height: 1.6;
            padding: 20px;
        }
        
        h1 {
            text-align: center;
            margin-bottom: 30px;
            color: var(--primary-color);
        }
        
        .prototype-container {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 30px;
            margin-bottom: 50px;
        }
        
        .screen {
            width: 375px;
            height: 812px;
            background-color: var(--white);
            border-radius: 40px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            position: relative;
            border: 10px solid #111;
            border-width: 55px 10px;
            display: flex;
            flex-direction: column;
        }
        
        .screen::before {
            content: '';
            position: absolute;
            width: 150px;
            height: 30px;
            background-color: #111;
            top: -42px;
            left: 50%;
            transform: translateX(-50%);
            border-radius: 20px;
            z-index: 1;
        }
        
        .screen-title {
            text-align: center;
            background-color: #eee;
            padding: 10px;
            font-weight: bold;
            font-size: 14px;
            color: #555;
        }
        
        .status-bar {
            height: 44px;
            background-color: var(--white);
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 15px;
            color: var(--text-primary);
            font-size: 14px;
            border-bottom: 1px solid var(--border-color);
        }
        
        .status-bar .time {
            font-weight: bold;
        }
        
        .status-bar .icons {
            display: flex;
            gap: 5px;
        }
        
        .content {
            flex: 1;
            overflow-y: auto;
            background-color: var(--bg-color);
        }
        
        .tab-bar {
            height: 83px;
            background-color: var(--white);
            display: flex;
            justify-content: space-between;
            border-top: 1px solid var(--border-color);
            padding-bottom: 30px;
        }
        
        .tab-item {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 10px 0;
            color: var(--text-secondary);
            font-size: 10px;
            text-decoration: none;
        }
        
        .tab-item.active {
            color: var(--primary-color);
        }
        
        .tab-item i {
            font-size: 20px;
            margin-bottom: 5px;
        }
        
        .header {
            padding: 15px;
            background-color: var(--white);
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid var(--border-color);
        }
        
        .header-title {
            font-size: 18px;
            font-weight: bold;
            flex: 1;
            text-align: center;
        }
        
        .btn {
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 5px;
            padding: 12px 20px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            width: 100%;
            text-align: center;
            margin: 10px 0;
        }
        
        .btn-secondary {
            background-color: var(--white);
            color: var(--primary-color);
            border: 1px solid var(--primary-color);
        }
        
        .input-group {
            margin-bottom: 20px;
        }
        
        .input-group label {
            display: block;
            margin-bottom: 8px;
            font-size: 14px;
            color: var(--text-secondary);
        }
        
        .input-group input, .input-group select {
            width: 100%;
            padding: 12px 15px;
            border: 1px solid var(--border-color);
            border-radius: 5px;
            font-size: 16px;
            background-color: var(--white);
        }
        
        .card {
            background-color: var(--white);
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 15px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }
        
        .card-header {
            padding: 15px;
            border-bottom: 1px solid var(--border-color);
            font-weight: 600;
            font-size: 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .card-content {
            padding: 15px;
        }
        
        .list-item {
            display: flex;
            padding: 15px;
            border-bottom: 1px solid var(--border-color);
            text-decoration: none;
            color: var(--text-primary);
            align-items: center;
        }
        
        .list-item:last-child {
            border-bottom: none;
        }
        
        .list-item .icon {
            width: 40px;
            height: 40px;
            background-color: var(--primary-color);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            margin-right: 15px;
            font-size: 20px;
        }
        
        .list-item .content {
            flex: 1;
        }
        
        .list-item .title {
            font-weight: 600;
            font-size: 16px;
            margin-bottom: 5px;
        }
        
        .list-item .subtitle {
            font-size: 14px;
            color: var(--text-tertiary);
        }
        
        .badge {
            background-color: var(--error-color);
            color: white;
            border-radius: 20px;
            padding: 2px 8px;
            font-size: 12px;
            margin-left: 5px;
        }

        /* Screen-specific styles */
        
        /* Login Screen */
        .login-screen .logo {
            text-align: center;
            padding: 40px 0;
        }
        
        .login-screen .logo img {
            width: 120px;
            height: 120px;
            object-fit: contain;
        }
        
        .login-screen .company-name {
            text-align: center;
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 30px;
            color: var(--primary-color);
        }
        
        .login-form {
            padding: 0 20px;
        }
        
        .login-options {
            display: flex;
            justify-content: space-between;
            margin: 20px 0;
            color: var(--text-secondary);
            font-size: 14px;
        }
        
        .login-footer {
            text-align: center;
            margin-top: 50px;
            font-size: 12px;
            color: var(--text-tertiary);
        }
        
        /* Server Config Screen */
        .config-form {
            padding: 20px;
        }
        
        .switch {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .switch input {
            width: auto;
        }
        
        .qr-scan {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            padding: 15px;
            border: 1px dashed var(--primary-color);
            border-radius: 5px;
            color: var(--primary-color);
            margin: 20px 0;
        }
        
        /* Home Screen */
        .search-bar {
            display: flex;
            align-items: center;
            padding: 10px 15px;
            background-color: var(--white);
        }
        
        .search-bar input {
            flex: 1;
            border: none;
            padding: 10px 15px;
            border-radius: 20px;
            background-color: var(--bg-color);
            font-size: 14px;
            margin-left: 10px;
        }
        
        .carousel {
            height: 180px;
            position: relative;
            overflow: hidden;
            margin-bottom: 15px;
        }
        
        .carousel-item {
            position: absolute;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            background-size: cover;
            background-position: center;
            opacity: 0;
            transition: opacity 0.5s ease;
        }
        
        .carousel-item.active {
            opacity: 1;
        }
        
        .carousel-indicators {
            position: absolute;
            bottom: 10px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 5px;
        }
        
        .indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.5);
        }
        
        .indicator.active {
            background-color: white;
        }
        
        .quick-actions {
            display: flex;
            flex-wrap: wrap;
            padding: 10px;
            background-color: var(--white);
            margin-bottom: 15px;
        }
        
        .quick-action {
            width: 25%;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 10px 0;
            color: var(--text-primary);
            text-decoration: none;
        }
        
        .quick-action i {
            font-size: 24px;
            color: var(--primary-color);
            margin-bottom: 5px;
        }
        
        .quick-action span {
            font-size: 12px;
        }
        
        .webview {
            height: 300px;
            background-color: var(--white);
            border: 1px solid var(--border-color);
            padding: 10px;
            font-size: 14px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>企业移动办公App原型设计</h1>
    
    <div class="prototype-container">
        <!-- 登录界面 -->
        <div class="screen login-screen">
            <div class="screen-title">登录界面</div>
            <div class="status-bar">
                <div class="time">9:41</div>
                <div class="icons">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <i class="fas fa-battery-full"></i>
                </div>
            </div>
            <div class="content">
                <div class="logo">
                    <div style="width: 120px; height: 120px; margin: 0 auto; background-color: var(--primary-color); border-radius: 20px; display: flex; align-items: center; justify-content: center; color: white;">
                        <i class="fas fa-building" style="font-size: 60px;"></i>
                    </div>
                </div>
                <div class="company-name">企业移动办公系统</div>
                <div class="login-form">
                    <!-- <div class="input-group">
                        <label for="enterpriseId">企业ID</label>
                        <input type="text" id="enterpriseId" placeholder="请输入企业ID">
                    </div> -->
                    <div class="input-group">
                        <label for="username">用户名</label>
                        <input type="text" id="username" placeholder="请输入用户名">
                    </div>
                    <div class="input-group">
                        <label for="password">密码</label>
                        <input type="password" id="password" placeholder="请输入密码">
                    </div>
                    <div class="login-options">
                        <div>
                            <input type="checkbox" id="remember">
                            <label for="remember">记住密码</label>
                        </div>
                        <div style="color: var(--primary-color);">
                            <i class="fas fa-server"></i> 服务器配置
                        </div>
                    </div>
                    <button class="btn">登录</button>
                   
                </div>
                <div class="login-footer">
                    版权所有 © 2023 企业移动办公系统<br>
                    版本 1.0.0
                </div>
            </div>
        </div>

        <!-- 服务器配置界面 -->
        <div class="screen">
            <div class="screen-title">服务器配置界面</div>
            <div class="status-bar">
                <div class="time">9:41</div>
                <div class="icons">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <i class="fas fa-battery-full"></i>
                </div>
            </div>
            <div class="header">
                <div style="width: 24px;">
                    <i class="fas fa-chevron-left"></i>
                </div>
                <div class="header-title">服务器配置</div>
                <div style="width: 24px;"></div>
            </div>
            <div class="content">
                <div class="config-form">
                    <div class="qr-scan">
                        <i class="fas fa-qrcode" style="font-size: 24px;"></i>
                        <span>扫描配置二维码</span>
                    </div>
                    <div class="input-group">
                        <label for="server-address">服务器地址</label>
                        <input type="text" id="server-address" placeholder="例如: company.example.com">
                    </div>
                    <div class="input-group">
                        <label for="server-port">服务器端口</label>
                        <input type="text" id="server-port" placeholder="例如: 8080">
                    </div>
                    <div class="input-group">
                        <button class="btn btn-secondary server-list-btn" style="margin:0">
                            <i class="fas fa-server"></i> 已保存的服务器配置
                        </button>
                    </div>

                    <!-- 底部弹出的服务器列表 -->
                    <div class="server-list-modal" style="display:none; position:fixed; bottom:0; left:0; right:0; background:white; border-radius:20px 20px 0 0; padding:20px; box-shadow:0 -2px 10px rgba(0,0,0,0.1);">
                        <div style="display:flex; justify-content:space-between; align-items:center; margin-bottom:15px;">
                            <h3 style="font-size:18px; margin:0;">已保存的服务器</h3>
                            <i class="fas fa-times close-modal" style="font-size:20px; color:var(--text-tertiary);"></i>
                        </div>
                        <div class="server-list">
                            <div class="server-item" style="display:flex; justify-content:space-between; align-items:center; padding:15px; border-bottom:1px solid var(--border-color);">
                                <div>
                                    <div style="font-weight:600;">测试服务器</div>
                                    <div style="font-size:12px; color:var(--text-tertiary);">test.example.com:8080</div>
                                </div>
                                <div>
                                    <i class="fas fa-edit" style="color:var(--primary-color); margin-right:10px;"></i>
                                    <i class="fas fa-trash-alt" style="color:var(--error-color);"></i>
                                </div>
                            </div>
                            <div class="server-item" style="display:flex; justify-content:space-between; align-items:center; padding:15px; border-bottom:1px solid var(--border-color);">
                                <div>
                                    <div style="font-weight:600;">生产服务器</div>
                                    <div style="font-size:12px; color:var(--text-tertiary);">prod.example.com:443</div>
                                </div>
                                <div>
                                    <i class="fas fa-edit" style="color:var(--primary-color); margin-right:10px;"></i>
                                    <i class="fas fa-trash-alt" style="color:var(--error-color);"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <button class="btn btn-secondary" style="margin-top: 20px;">测试连接</button>
                    <button class="btn">保存配置</button>
                </div>
            </div>
        </div>

        <!-- 首页界面 -->
        <div class="screen">
            <div class="screen-title">首页界面</div>
            <div class="status-bar">
                <div class="time">9:41</div>
                <div class="icons">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <i class="fas fa-battery-full"></i>
                </div>
            </div>
          
            <div class="content">
                <div class="carousel">
                    <div class="carousel-item active" style="background-color: #3366cc; color: white; display: flex; align-items: center; justify-content: center;">
                        <div style="text-align: center;">
                            <div style="font-size: 24px; font-weight: bold; margin-bottom: 10px;">企业战略规划会议</div>
                            <div>2023年度第三季度企业战略规划会议将于下周三举行</div>
                        </div>
                    </div>
                    <div class="carousel-item" style="background-color: #FF9900; color: white; display: flex; align-items: center; justify-content: center;">
                        <div style="text-align: center;">
                            <div style="font-size: 24px; font-weight: bold; margin-bottom: 10px;">新版应用上线</div>
                            <div>企业数据分析平台V2.0现已上线，欢迎体验</div>
                        </div>
                    </div>
                    <div class="carousel-item" style="background-color: #52C41A; color: white; display: flex; align-items: center; justify-content: center;">
                        <div style="text-align: center;">
                            <div style="font-size: 24px; font-weight: bold; margin-bottom: 10px;">年度优秀员工评选</div>
                            <div>2023年度优秀员工评选活动开始，请各部门提交推荐名单</div>
                        </div>
                    </div>
                    <div class="carousel-indicators">
                        <div class="indicator active"></div>
                        <div class="indicator"></div>
                        <div class="indicator"></div>
                    </div>
                </div>
                
              
                
                <div style="padding: 15px; background-color: var(--white); margin-bottom: 15px;">
                    <div style="font-weight: 600; margin-bottom: 10px;">企业资讯</div>
                    <div class="webview">
                        <div style="font-weight: bold; font-size: 18px; margin-bottom: 10px;">公司新闻</div>
                        <div style="margin-bottom: 15px;">
                            <div style="font-weight: 600; margin-bottom: 5px;">我司成功签约国际大型合作项目</div>
                            <div style="color: var(--text-tertiary); font-size: 12px;">2023-10-15</div>
                            <div style="margin-top: 5px;">近日，我公司与国际知名企业成功签署战略合作协议，双方将在多个领域展开深入合作...</div>
                        </div>
                        <div style="margin-bottom: 15px;">
                            <div style="font-weight: 600; margin-bottom: 5px;">技术部门完成年度系统升级</div>
                            <div style="color: var(--text-tertiary); font-size: 12px;">2023-10-10</div>
                            <div style="margin-top: 5px;">经过技术团队三个月的努力，公司核心系统成功完成升级，性能提升30%，并新增多项功能...</div>
                        </div>
                        <div>
                            <div style="font-weight: 600; margin-bottom: 5px;">企业社会责任：公益捐赠活动圆满完成</div>
                            <div style="color: var(--text-tertiary); font-size: 12px;">2023-10-05</div>
                            <div style="margin-top: 5px;">我公司组织的年度公益捐赠活动已圆满结束，共为山区学校捐赠教学设备和图书...</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="tab-bar">
                <a href="#" class="tab-item active">
                    <i class="fas fa-home"></i>
                    <span>首页</span>
                </a>
                <a href="#" class="tab-item">
                    <i class="fas fa-th-large"></i>
                    <span>应用</span>
                </a>
                <a href="#" class="tab-item">
                    <i class="fas fa-desktop"></i>
                    <span>工作台</span>
                </a>
                <a href="#" class="tab-item">
                    <i class="fas fa-comment"></i>
                    <span>消息</span>
                </a>
                <a href="#" class="tab-item">
                    <i class="fas fa-user"></i>
                    <span>我的</span>
                </a>
            </div>
        </div>

        <!-- 应用中心界面 -->
        <div class="screen">
            <div class="screen-title">应用中心界面</div>
            <div class="status-bar">
                <div class="time">9:41</div>
                <div class="icons">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <i class="fas fa-battery-full"></i>
                </div>
            </div>
            <div class="header">
                <div style="width: 24px;"></div>
                <div class="header-title">应用</div>
                <div style="width: 24px;">
                    <i class="fas fa-ellipsis-v"></i>
                </div>
            </div>
            <div class="search-bar">
                <input type="text" placeholder="搜索应用" style="margin-left: 0;">
                <i class="fas fa-search" style="margin-left: -30px; color: var(--text-tertiary);"></i>
            </div>
            <div class="content">
                <div style="margin-top: 15px;">
                    <div class="app-section" style="background-color: var(--white); margin-bottom: 15px;">
                        <div class="section-header" style="padding: 15px; font-weight: 600; display: flex; justify-content: space-between; align-items: center; cursor: pointer;">
                            <span>办公应用</span>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="section-content" style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 15px; padding: 0 15px 15px;">
                            <div style="display: flex; flex-direction: column; align-items: center; text-align: center;">
                                <div style="width: 50px; height: 50px; background-color: var(--primary-color); border-radius: 10px; display: flex; align-items: center; justify-content: center; color: white; margin-bottom: 8px;">
                                    <i class="fas fa-calendar-alt"></i>
                                </div>
                                <div style="font-size: 12px;">日程管理</div>
                            </div>
                            <div style="display: flex; flex-direction: column; align-items: center; text-align: center;">
                                <div style="width: 50px; height: 50px; background-color: var(--secondary-color); border-radius: 10px; display: flex; align-items: center; justify-content: center; color: white; margin-bottom: 8px;">
                                    <i class="fas fa-tasks"></i>
                                </div>
                                <div style="font-size: 12px;">任务协作</div>
                            </div>
                            <div style="display: flex; flex-direction: column; align-items: center; text-align: center;">
                                <div style="width: 50px; height: 50px; background-color: #52C41A; border-radius: 10px; display: flex; align-items: center; justify-content: center; color: white; margin-bottom: 8px;">
                                    <i class="fas fa-file-alt"></i>
                                </div>
                                <div style="font-size: 12px;">文档中心</div>
                            </div>
                        </div>
                    </div>

                    <div class="app-section" style="background-color: var(--white);">
                        <div class="section-header" style="padding: 15px; font-weight: 600; display: flex; justify-content: space-between; align-items: center; cursor: pointer;">
                            <span>财务应用</span>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="section-content" style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 15px; padding: 0 15px 15px;">
                            <div style="display: flex; flex-direction: column; align-items: center; text-align: center;">
                                <div style="width: 50px; height: 50px; background-color: #F5222D; border-radius: 10px; display: flex; align-items: center; justify-content: center; color: white; margin-bottom: 8px;">
                                    <i class="fas fa-money-bill-wave"></i>
                                </div>
                                <div style="font-size: 12px;">费用报销</div>
                            </div>
                            <div style="display: flex; flex-direction: column; align-items: center; text-align: center;">
                                <div style="width: 50px; height: 50px; background-color: #FAAD14; border-radius: 10px; display: flex; align-items: center; justify-content: center; color: white; margin-bottom: 8px;">
                                    <i class="fas fa-chart-pie"></i>
                                </div>
                                <div style="font-size: 12px;">财务报表</div>
                            </div>
                        </div>
                    </div>
                </div>

                <script>
                    // 为所有section-header添加点击事件
                    document.querySelectorAll('.section-header').forEach(header => {
                        header.addEventListener('click', () => {
                            const content = header.nextElementSibling;
                            const icon = header.querySelector('i');
                            
                            // 切换内容显示/隐藏
                            if(content.style.display === 'none') {
                                content.style.display = 'grid';
                                icon.className = 'fas fa-chevron-down';
                            } else {
                                content.style.display = 'none';
                                icon.className = 'fas fa-chevron-right';
                            }
                        });
                    });
                </script>
                
            </div>
            <div class="tab-bar">
                <a href="#" class="tab-item">
                    <i class="fas fa-home"></i>
                    <span>首页</span>
                </a>
                <a href="#" class="tab-item active">
                    <i class="fas fa-th-large"></i>
                    <span>应用</span>
                </a>
                <a href="#" class="tab-item">
                    <i class="fas fa-desktop"></i>
                    <span>工作台</span>
                </a>
                <a href="#" class="tab-item">
                    <i class="fas fa-comment"></i>
                    <span>消息</span>
                </a>
                <a href="#" class="tab-item">
                    <i class="fas fa-user"></i>
                    <span>我的</span>
                </a>
            </div>
        </div>

        <!-- 工作台界面 -->
        <div class="screen">
            <div class="screen-title">工作台界面</div>
            <div class="status-bar">
                <div class="time">9:41</div>
                <div class="icons">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <i class="fas fa-battery-full"></i>
                </div>
            </div>
            <div class="header">
                <div style="width: 24px;"></div>
                <div class="header-title">工作台</div>
                <div style="width: 24px;">
                    <i class="fas fa-ellipsis-v"></i>
                </div>
            </div>
            <div class="search-bar">
                <input type="text" placeholder="搜索应用" style="margin-left: 0;">
                <i class="fas fa-search" style="margin-left: -30px; color: var(--text-tertiary);"></i>
            </div>
            <div class="content">
                <div style="margin-top: 15px;">
                    <div class="app-section" style="background-color: var(--white); margin-bottom: 15px;">
                        <div class="section-header" style="padding: 15px; font-weight: 600; display: flex; justify-content: space-between; align-items: center; cursor: pointer;">
                            <span>常用应用</span>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="section-content" style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 15px; padding: 0 15px 15px;">
                            <div style="display: flex; flex-direction: column; align-items: center; text-align: center;">
                                <div style="width: 50px; height: 50px; background-color: var(--primary-color); border-radius: 10px; display: flex; align-items: center; justify-content: center; color: white; margin-bottom: 8px;">
                                    <i class="fas fa-calendar-alt"></i>
                                </div>
                                <div style="font-size: 12px;">日程管理</div>
                            </div>
                            <div style="display: flex; flex-direction: column; align-items: center; text-align: center;">
                                <div style="width: 50px; height: 50px; background-color: var(--secondary-color); border-radius: 10px; display: flex; align-items: center; justify-content: center; color: white; margin-bottom: 8px;">
                                    <i class="fas fa-tasks"></i>
                                </div>
                                <div style="font-size: 12px;">任务协作</div>
                            </div>
                            <div style="display: flex; flex-direction: column; align-items: center; text-align: center;">
                                <div style="width: 50px; height: 50px; background-color: #52C41A; border-radius: 10px; display: flex; align-items: center; justify-content: center; color: white; margin-bottom: 8px;">
                                    <i class="fas fa-file-alt"></i>
                                </div>
                                <div style="font-size: 12px;">文档中心</div>
                            </div>
                            
                        </div>
                    </div>

                  
                </div>

                <script>
                    // 为所有section-header添加点击事件
                    document.querySelectorAll('.section-header').forEach(header => {
                        header.addEventListener('click', () => {
                            const content = header.nextElementSibling;
                            const icon = header.querySelector('i');
                            
                            // 切换内容显示/隐藏
                            if(content.style.display === 'none') {
                                content.style.display = 'grid';
                                icon.className = 'fas fa-chevron-down';
                            } else {
                                content.style.display = 'none';
                                icon.className = 'fas fa-chevron-right';
                            }
                        });
                    });
                </script>
                
            </div>
            <div class="tab-bar">
                <a href="#" class="tab-item">
                    <i class="fas fa-home"></i>
                    <span>首页</span>
                </a>
                <a href="#" class="tab-item">
                    <i class="fas fa-th-large"></i>
                    <span>应用</span>
                </a>
                <a href="#" class="tab-item active">
                    <i class="fas fa-desktop"></i>
                    <span>工作台</span>
                </a>
                <a href="#" class="tab-item">
                    <i class="fas fa-comment"></i>
                    <span>消息</span>
                </a>
                <a href="#" class="tab-item">
                    <i class="fas fa-user"></i>
                    <span>我的</span>
                </a>
            </div>
        </div>

        <!-- 消息界面 -->
        <div class="screen">
            <div class="screen-title">消息界面</div>
            <div class="status-bar">
                <div class="time">9:41</div>
                <div class="icons">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <i class="fas fa-battery-full"></i>
                </div>
            </div>
            <div class="header">
                <div style="width: 24px;"></div>
                <div class="header-title">消息</div>
                <div style="width: 24px;">
                    <i class="fas fa-ellipsis-v"></i>
                </div>
            </div>
            <div style="display: flex; background-color: var(--white); border-bottom: 1px solid var(--border-color);">
                <div style="flex: 1; text-align: center; padding: 15px; font-weight: 600; color: var(--primary-color); border-bottom: 2px solid var(--primary-color);">全部</div>
                <div style="flex: 1; text-align: center; padding: 15px; color: var(--text-secondary);">未读</div>
                <div style="flex: 1; text-align: center; padding: 15px; color: var(--text-secondary);">系统</div>
                <div style="flex: 1; text-align: center; padding: 15px; color: var(--text-secondary);">工作</div>
            </div>
            <div class="content">
                <div class="list-item" style="background-color: var(--white);">
                    <div class="icon" style="background-color: var(--primary-color);">
                        <i class="fas fa-bell"></i>
                    </div>
                    <div style="flex: 1;">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div style="font-weight: 600; font-size: 16px;">系统通知</div>
                            <div style="font-size: 12px; color: var(--text-tertiary);">09:15</div>
                        </div>
                        <div style="font-size: 14px; color: var(--text-secondary);">您有一个新的系统更新，请及时查看</div>
                    </div>
                </div>
                <div class="list-item" style="background-color: var(--white);">
                    <div class="icon" style="background-color: #FF9900;">
                        <i class="fas fa-file-invoice"></i>
                    </div>
                    <div style="flex: 1;">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div style="font-weight: 600; font-size: 16px;">审批提醒</div>
                            <div style="font-size: 12px; color: var(--text-tertiary);">昨天</div>
                        </div>
                        <div style="font-size: 14px; color: var(--text-secondary);">您有2个待审批事项需要处理</div>
                    </div>
                    <div class="badge">2</div>
                </div>
                <div class="list-item" style="background-color: var(--white);">
                    <div class="icon" style="background-color: #13C2C2;">
                        <i class="fas fa-user-friends"></i>
                    </div>
                    <div style="flex: 1;">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div style="font-weight: 600; font-size: 16px;">团队消息</div>
                            <div style="font-size: 12px; color: var(--text-tertiary);">昨天</div>
                        </div>
                        <div style="font-size: 14px; color: var(--text-secondary);">产品部：周会会议纪要已上传</div>
                    </div>
                </div>
                <div class="list-item" style="background-color: var(--white);">
                    <div class="icon" style="background-color: #722ED1;">
                        <i class="fas fa-calendar-alt"></i>
                    </div>
                    <div style="flex: 1;">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div style="font-weight: 600; font-size: 16px;">日程提醒</div>
                            <div style="font-size: 12px; color: var(--text-tertiary);">3天前</div>
                        </div>
                        <div style="font-size: 14px; color: var(--text-secondary);">明天10:00有产品评审会议</div>
                    </div>
                    <div class="badge">1</div>
                </div>
                <div class="list-item" style="background-color: var(--white);">
                    <div class="icon" style="background-color: #F5222D;">
                        <i class="fas fa-exclamation-circle"></i>
                    </div>
                    <div style="flex: 1;">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div style="font-weight: 600; font-size: 16px;">紧急通知</div>
                            <div style="font-size: 12px; color: var(--text-tertiary);">10-15</div>
                        </div>
                        <div style="font-size: 14px; color: var(--text-secondary);">服务器维护通知：本周六凌晨2:00-4:00系统将进行例行维护</div>
                    </div>
                </div>
                <div class="list-item" style="background-color: var(--white);">
                    <div class="icon" style="background-color: #1890FF;">
                        <i class="fas fa-comment-alt"></i>
                    </div>
                    <div style="flex: 1;">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div style="font-weight: 600; font-size: 16px;">王经理</div>
                            <div style="font-size: 12px; color: var(--text-tertiary);">10-10</div>
                        </div>
                        <div style="font-size: 14px; color: var(--text-secondary);">请将上周的工作报告发给我，谢谢</div>
                    </div>
                </div>
                <div class="list-item" style="background-color: var(--white);">
                    <div class="icon" style="background-color: #52C41A;">
                        <i class="fas fa-users"></i>
                    </div>
                    <div style="flex: 1;">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div style="font-weight: 600; font-size: 16px;">产品讨论组</div>
                            <div style="font-size: 12px; color: var(--text-tertiary);">10-08</div>
                        </div>
                        <div style="font-size: 14px; color: var(--text-secondary);">李设计：新版原型已上传，请大家查看反馈</div>
                    </div>
                </div>
            </div>
            <div class="tab-bar">
                <a href="#" class="tab-item">
                    <i class="fas fa-home"></i>
                    <span>首页</span>
                </a>
                <a href="#" class="tab-item">
                    <i class="fas fa-th-large"></i>
                    <span>应用</span>
                </a>
                <a href="#" class="tab-item">
                    <i class="fas fa-desktop"></i>
                    <span>工作台</span>
                </a>
                <a href="#" class="tab-item active">
                    <i class="fas fa-comment"></i>
                    <span>消息</span>
                </a>
                <a href="#" class="tab-item">
                    <i class="fas fa-user"></i>
                    <span>我的</span>
                </a>
            </div>
        </div>

        <!-- 设置界面 -->
        <div class="screen">
            <div class="screen-title">设置界面</div>
            <div class="status-bar">
                <div class="time">9:41</div>
                <div class="icons">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <i class="fas fa-battery-full"></i>
                </div>
            </div>
            <div class="header">
                <div style="width: 24px;"></div>
                <div class="header-title">设置</div>
                <div style="width: 24px;"></div>
            </div>
            <div class="content">
                <div style="background-color: var(--white); padding: 20px; margin-bottom: 15px; display: flex; align-items: center;">
                    <div style="width: 70px; height: 70px; border-radius: 35px; background-color: var(--primary-color); display: flex; align-items: center; justify-content: center; color: white; margin-right: 15px;">
                        <i class="fas fa-user" style="font-size: 36px;"></i>
                    </div>
                    <div>
                        <div style="font-weight: 600; font-size: 20px;">张经理</div>
                        <div style="color: var(--text-secondary); font-size: 14px;">产品部 · 产品经理</div>
                    </div>
                    <div style="margin-left: auto;">
                        <i class="fas fa-chevron-right" style="color: var(--text-tertiary);"></i>
                    </div>
                </div>
                
                <div style="margin-bottom: 15px;">
                    <div style="padding: 15px; font-size: 14px; color: var(--text-tertiary); background-color: var(--bg-color);">账号设置</div>
                    <div class="list-item" style="background-color: var(--white);">
                        <div class="icon">
                            <i class="fas fa-lock"></i>
                        </div>
                        <div style="flex: 1;">
                            <div style="font-weight: 500;">账号安全</div>
                        </div>
                        <i class="fas fa-chevron-right" style="color: var(--text-tertiary);"></i>
                    </div>
                    <div class="list-item" style="background-color: var(--white);">
                        <div class="icon" style="background-color: #FF9900;">
                            <i class="fas fa-bell"></i>
                        </div>
                        <div style="flex: 1;">
                            <div style="font-weight: 500;">消息通知</div>
                        </div>
                        <i class="fas fa-chevron-right" style="color: var(--text-tertiary);"></i>
                    </div>
                    <div class="list-item" style="background-color: var(--white);">
                        <div class="icon" style="background-color: #13C2C2;">
                            <i class="fas fa-user-shield"></i>
                        </div>
                        <div style="flex: 1;">
                            <div style="font-weight: 500;">隐私设置</div>
                        </div>
                        <i class="fas fa-chevron-right" style="color: var(--text-tertiary);"></i>
                    </div>
                </div>
                
                <div style="margin-bottom: 15px;">
                    <div style="padding: 15px; font-size: 14px; color: var(--text-tertiary); background-color: var(--bg-color);">企业设置</div>
                    <div class="list-item" style="background-color: var(--white);">
                        <div class="icon" style="background-color: #1890FF;">
                            <i class="fas fa-building"></i>
                        </div>
                        <div style="flex: 1;">
                            <div style="font-weight: 500;">企业信息</div>
                        </div>
                        <i class="fas fa-chevron-right" style="color: var(--text-tertiary);"></i>
                    </div>
                    <div class="list-item" style="background-color: var(--white);">
                        <div class="icon" style="background-color: #722ED1;">
                            <i class="fas fa-users-cog"></i>
                        </div>
                        <div style="flex: 1;">
                            <div style="font-weight: 500;">部门管理</div>
                        </div>
                        <i class="fas fa-chevron-right" style="color: var(--text-tertiary);"></i>
                    </div>
                </div>
                
                <div style="margin-bottom: 15px;">
                    <div style="padding: 15px; font-size: 14px; color: var(--text-tertiary); background-color: var(--bg-color);">应用偏好</div>
                    <div class="list-item" style="background-color: var(--white);">
                        <div class="icon" style="background-color: #52C41A;">
                            <i class="fas fa-th-large"></i>
                        </div>
                        <div style="flex: 1;">
                            <div style="font-weight: 500;">应用排序</div>
                        </div>
                        <i class="fas fa-chevron-right" style="color: var(--text-tertiary);"></i>
                    </div>
                    <div class="list-item" style="background-color: var(--white);">
                        <div class="icon" style="background-color: var(--secondary-color);">
                            <i class="fas fa-paint-brush"></i>
                        </div>
                        <div style="flex: 1;">
                            <div style="font-weight: 500;">外观设置</div>
                        </div>
                        <div style="display: flex; align-items: center;">
                            <span style="color: var(--text-tertiary); font-size: 14px; margin-right: 5px;">浅色</span>
                            <i class="fas fa-chevron-right" style="color: var(--text-tertiary);"></i>
                        </div>
                    </div>
                </div>
                
                <div style="margin-bottom: 15px;">
                    <div style="padding: 15px; font-size: 14px; color: var(--text-tertiary); background-color: var(--bg-color);">关于与帮助</div>
                    <div class="list-item" style="background-color: var(--white);">
                        <div class="icon" style="background-color: #F5222D;">
                            <i class="fas fa-info-circle"></i>
                        </div>
                        <div style="flex: 1;">
                            <div style="font-weight: 500;">关于我们</div>
                        </div>
                        <i class="fas fa-chevron-right" style="color: var(--text-tertiary);"></i>
                    </div>
                    <div class="list-item" style="background-color: var(--white);">
                        <div class="icon" style="background-color: #FAAD14;">
                            <i class="fas fa-question-circle"></i>
                        </div>
                        <div style="flex: 1;">
                            <div style="font-weight: 500;">帮助中心</div>
                        </div>
                        <i class="fas fa-chevron-right" style="color: var(--text-tertiary);"></i>
                    </div>
                    <div class="list-item" style="background-color: var(--white);">
                        <div class="icon" style="background-color: #1890FF;">
                            <i class="fas fa-comment-dots"></i>
                        </div>
                        <div style="flex: 1;">
                            <div style="font-weight: 500;">意见反馈</div>
                        </div>
                        <i class="fas fa-chevron-right" style="color: var(--text-tertiary);"></i>
                    </div>
                </div>
                
                <div style="padding: 20px;">
                    <button class="btn" style="background-color: #F5222D;">退出登录</button>
                </div>
            </div>
            <div class="tab-bar">
                <a href="#" class="tab-item">
                    <i class="fas fa-home"></i>
                    <span>首页</span>
                </a>
                <a href="#" class="tab-item">
                    <i class="fas fa-th-large"></i>
                    <span>应用</span>
                </a>
                <a href="#" class="tab-item">
                    <i class="fas fa-desktop"></i>
                    <span>工作台</span>
                </a>
                <a href="#" class="tab-item">
                    <i class="fas fa-comment"></i>
                    <span>消息</span>
                </a>
                <a href="#" class="tab-item active">
                    <i class="fas fa-user"></i>
                    <span>我的</span>
                </a>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 轮播图自动切换
            setInterval(function() {
                const carousels = document.querySelectorAll('.carousel');
                carousels.forEach(carousel => {
                    const items = carousel.querySelectorAll('.carousel-item');
                    const indicators = carousel.querySelectorAll('.indicator');
                    
                    let activeIndex = 0;
                    for (let i = 0; i < items.length; i++) {
                        if (items[i].classList.contains('active')) {
                            activeIndex = i;
                            items[i].classList.remove('active');
                            indicators[i].classList.remove('active');
                            break;
                        }
                    }
                    
                    activeIndex = (activeIndex + 1) % items.length;
                    items[activeIndex].classList.add('active');
                    indicators[activeIndex].classList.add('active');
                });
            }, 3000);
        });
    </script>
</body>
</html> 