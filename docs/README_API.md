# 企业混合App后端API开发文档

## 📋 文档概述

本文档集合为企业混合App的后端开发提供完整的API接口规范和开发指南。该App是一个基于Flutter开发的企业移动办公平台，支持WebView混合开发模式。

## 📁 文档结构

### 1. 核心文档

| 文档名称 | 文件路径 | 说明 |
|---------|----------|------|
| **完整API规范** | `backend_api_specification.md` | 详细的API接口文档，包含所有接口的请求/响应格式 |
| **快速参考** | `api_quick_reference.md` | API接口快速查询手册，便于开发时快速查找 |
| **数据库设计** | `database_design.md` | 完整的数据库表结构设计和初始化脚本 |

### 2. 测试工具

| 文件名称 | 文件路径 | 说明 |
|---------|----------|------|
| **Postman集合** | `Enterprise_App_API.postman_collection.json` | 可导入Postman的API测试集合 |

## 🚀 快速开始

### 1. 环境要求

- **后端框架**: Spring Boot / Node.js / Python Django (推荐)
- **数据库**: MySQL 8.0+ / PostgreSQL 12+
- **缓存**: Redis 6.0+
- **Java版本**: JDK 11+ (如使用Spring Boot)
- **Node.js版本**: 16+ (如使用Node.js)

### 2. 基础配置

```bash
# 基础URL
BASE_URL=http://localhost:8080/api

# 测试账号
USERNAME=admin
PASSWORD=123456
```

### 3. 核心功能模块

#### 🔐 认证模块
- 用户登录/登出
- Token刷新机制
- 会话管理

#### 🏠 首页模块
- 轮播图管理
- 企业新闻/公告
- 缓存策略支持

#### 📱 应用模块
- 应用分类管理
- 常用应用设置
- 应用排序功能

#### 💼 工作台模块
- 个人常用应用
- 快速访问入口

#### 💬 消息模块
- 系统消息
- 工作消息
- 消息已读状态

#### 👤 用户模块
- 用户信息管理
- 个人设置
- 头像上传

## 📊 数据库设计

### 核心表结构

```
users                 # 用户表
├── app_categories    # 应用分类表
├── applications      # 应用表
├── user_app_favorites # 用户常用应用表
├── messages          # 消息表
├── user_message_reads # 消息读取状态表
├── banners           # 轮播图表
├── news              # 新闻表
├── user_settings     # 用户设置表
├── error_logs        # 错误日志表
└── system_configs    # 系统配置表
```

### 关键关系

- 用户 ↔ 常用应用 (多对多)
- 用户 ↔ 消息读取状态 (一对多)
- 应用 ↔ 应用分类 (多对一)

## 🔧 API接口概览

### 认证接口 (3个)
- `POST /auth/login` - 用户登录
- `POST /auth/refresh` - 刷新Token
- `POST /auth/logout` - 用户登出

### 系统接口 (2个)
- `GET /system/ping` - 连接测试
- `GET /system/info` - 服务器信息

### 业务接口 (12个)
- `GET /home/<USER>
- `GET /apps/list` - 应用列表
- `POST /apps/favorites` - 保存常用应用
- `POST /apps/sort` - 应用排序
- `GET /workspace/apps` - 工作台应用
- `GET /messages/list` - 消息列表
- `POST /messages/read` - 标记已读
- `GET /user/profile` - 用户信息
- `PUT /user/profile` - 更新用户信息
- `GET /settings/user` - 用户设置
- `PUT /settings/user` - 更新设置
- `POST /upload/avatar` - 头像上传

## 🧪 测试指南

### 1. 导入Postman集合

1. 打开Postman
2. 点击Import按钮
3. 选择`Enterprise_App_API.postman_collection.json`文件
4. 设置环境变量：
   - `baseUrl`: `http://localhost:8080/api`
   - `token`: (登录后获取)

### 2. 测试流程

```bash
# 1. 测试服务器连接
GET /system/ping

# 2. 用户登录获取Token
POST /auth/login
{
  "username": "admin",
  "password": "123456"
}

# 3. 使用Token访问业务接口
GET /home/<USER>
Header: Authorization: Bearer {token}

# 4. 测试其他功能接口...
```

### 3. 测试数据

系统提供完整的测试数据，包括：
- 测试用户账号
- 应用分类和应用数据
- 模拟消息数据
- 轮播图和新闻数据

## 🔒 安全考虑

### 1. 认证安全
- JWT Token机制
- Token过期时间控制(2小时)
- 刷新Token机制
- 密码BCrypt加密

### 2. 接口安全
- 请求参数验证
- SQL注入防护
- XSS攻击防护
- CORS跨域配置

### 3. 数据安全
- 敏感数据加密存储
- 定期数据备份
- 访问日志记录
- 错误日志监控

## 📈 性能优化

### 1. 缓存策略
- Redis缓存热点数据
- 首页信息缓存
- 应用列表缓存
- 消息列表缓存

### 2. 数据库优化
- 合理的索引设计
- 分页查询优化
- 慢查询监控
- 定期数据清理

### 3. 接口优化
- 响应数据压缩
- 批量操作支持
- 异步处理机制
- 接口限流控制

## 🚀 部署指南

### 1. 开发环境

```bash
# 克隆项目
git clone <repository-url>

# 安装依赖
npm install  # 或 mvn install

# 配置数据库
# 执行 database_design.md 中的SQL脚本

# 启动服务
npm start  # 或 mvn spring-boot:run
```

### 2. 生产环境

```bash
# 使用Docker部署
docker-compose up -d

# 或使用传统部署
# 1. 配置Nginx反向代理
# 2. 配置SSL证书
# 3. 配置数据库连接池
# 4. 配置Redis集群
# 5. 配置日志收集
```

## 📞 技术支持

### 开发团队联系方式
- **技术负责人**: [联系方式]
- **后端开发**: [联系方式]
- **数据库管理**: [联系方式]

### 问题反馈
- **Bug报告**: 请提供详细的错误日志和复现步骤
- **功能建议**: 请描述具体的业务场景和需求
- **性能问题**: 请提供相关的性能监控数据

## 📝 更新日志

### v2.1.0 (2024-12-20)
- ✅ 完成所有核心API接口设计
- ✅ 完成数据库表结构设计
- ✅ 完成Postman测试集合
- ✅ 完成API文档编写
- ✅ 完成安全和性能优化建议

### 下一版本计划
- 🔄 WebSocket实时消息推送
- 🔄 文件上传进度显示
- 🔄 多租户支持
- 🔄 API版本控制
- 🔄 GraphQL接口支持

## 📚 相关资源

### 技术文档
- [Flutter官方文档](https://flutter.dev/docs)
- [WebView集成指南](https://pub.dev/packages/webview_flutter)
- [JWT认证最佳实践](https://jwt.io/introduction)

### 开发工具
- [Postman](https://www.postman.com/) - API测试工具
- [MySQL Workbench](https://www.mysql.com/products/workbench/) - 数据库管理工具
- [Redis Desktop Manager](https://rdm.dev/) - Redis管理工具

---

**注意**: 本文档基于Flutter应用的实际需求编写，所有接口都经过前端代码验证，确保前后端数据格式完全匹配。在实际开发中，请严格按照本文档的接口规范进行实现。
