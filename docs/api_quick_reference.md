# API接口快速参考

## 基础信息
- **基础URL**: `http://localhost:8080/api`
- **认证方式**: Bearer Token
- **数据格式**: JSON

## 核心接口列表

### 1. 认证接口
| 方法 | 路径 | 说明 | 认证 |
|------|------|------|------|
| POST | `/auth/login` | 用户登录 | ❌ |
| POST | `/auth/refresh` | 刷新Token | ❌ |
| POST | `/auth/logout` | 用户登出 | ✅ |

### 2. 系统接口
| 方法 | 路径 | 说明 | 认证 |
|------|------|------|------|
| GET | `/system/ping` | 服务器连接测试 | ❌ |
| GET | `/system/info` | 获取服务器信息 | ❌ |

### 3. 首页接口
| 方法 | 路径 | 说明 | 认证 |
|------|------|------|------|
| GET | `/home/<USER>

### 4. 应用接口
| 方法 | 路径 | 说明 | 认证 |
|------|------|------|------|
| GET | `/apps/list` | 获取应用列表 | ✅ |
| POST | `/apps/favorites` | 保存常用应用 | ✅ |
| POST | `/apps/sort` | 保存应用排序 | ✅ |

### 5. 工作台接口
| 方法 | 路径 | 说明 | 认证 |
|------|------|------|------|
| GET | `/workspace/apps` | 获取工作台应用 | ✅ |

### 6. 消息接口
| 方法 | 路径 | 说明 | 认证 |
|------|------|------|------|
| GET | `/messages/list` | 获取消息列表 | ✅ |
| POST | `/messages/read` | 标记消息已读 | ✅ |

### 7. 用户接口
| 方法 | 路径 | 说明 | 认证 |
|------|------|------|------|
| GET | `/user/profile` | 获取用户信息 | ✅ |
| PUT | `/user/profile` | 更新用户信息 | ✅ |

### 8. 设置接口
| 方法 | 路径 | 说明 | 认证 |
|------|------|------|------|
| GET | `/settings/user` | 获取用户设置 | ✅ |
| PUT | `/settings/user` | 更新用户设置 | ✅ |

### 9. 文件上传接口
| 方法 | 路径 | 说明 | 认证 |
|------|------|------|------|
| POST | `/upload/avatar` | 上传头像 | ✅ |
| POST | `/upload/file` | 上传文件 | ✅ |

### 10. 其他接口
| 方法 | 路径 | 说明 | 认证 |
|------|------|------|------|
| GET | `/search` | 全局搜索 | ✅ |
| GET | `/stats/user` | 用户统计 | ✅ |
| POST | `/logs/error` | 错误日志上报 | ✅ |
| GET | `/version/check` | 检查更新 | ❌ |

## 重要数据结构

### 用户信息 (User)
```json
{
  "id": 1,
  "username": "admin",
  "name": "管理员",
  "avatar": "https://example.com/avatar.jpg",
  "department": "信息技术部",
  "email": "<EMAIL>",
  "phone": "13800138000"
}
```

### 应用信息 (App)
```json
{
  "id": 1,
  "name": "日程管理",
  "icon": "calendar_today",
  "color": "0xFF3366CC",
  "url": "https://calendar.example.com",
  "description": "管理个人和团队日程安排",
  "category": "办公协作",
  "sort": 1,
  "isFavorite": false
}
```

### 消息信息 (Message)
```json
{
  "id": 1,
  "title": "系统维护通知",
  "content": "系统将于今晚22:00-24:00进行维护升级...",
  "time": "10:30",
  "icon": "notifications",
  "color": "0xFF3366CC",
  "unreadCount": 1,
  "type": "system",
  "isRead": false
}
```

### 轮播图信息 (Banner)
```json
{
  "id": 1,
  "title": "企业数字化转型",
  "image": "https://example.com/banner1.jpg",
  "url": "https://example.com/news/1",
  "sort": 1
}
```

### 新闻信息 (News)
```json
{
  "id": 1,
  "title": "公司Q4季度总结会议通知",
  "summary": "定于本月底召开Q4季度总结会议...",
  "publishTime": "2024-12-20 10:00:00",
  "category": "公司公告",
  "author": "人事部",
  "readCount": 156
}
```

## 常用请求示例

### 登录请求
```bash
curl -X POST http://localhost:8080/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "123456"
  }'
```

### 获取首页信息
```bash
curl -X GET http://localhost:8080/api/home/<USER>
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 保存常用应用
```bash
curl -X POST http://localhost:8080/api/apps/favorites \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "appIds": [1, 2, 3]
  }'
```

### 获取消息列表
```bash
curl -X GET "http://localhost:8080/api/messages/list?type=all&page=1" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 错误处理

### 常见错误码
- `400`: 请求参数错误
- `401`: 未授权或Token过期
- `403`: 权限不足
- `404`: 资源不存在
- `500`: 服务器内部错误

### 错误响应格式
```json
{
  "success": false,
  "message": "错误描述",
  "error": "详细错误信息",
  "timestamp": 1703123456789
}
```

## 开发注意事项

1. **Token管理**: Token有效期2小时，需要实现自动刷新机制
2. **缓存策略**: 首页、应用列表、消息列表支持缓存，通过`forceRefresh`参数控制
3. **分页处理**: 消息列表等接口支持分页，注意处理分页参数
4. **文件上传**: 头像最大2MB，其他文件最大10MB
5. **搜索功能**: 支持全局搜索，可按类型筛选结果
6. **多语言**: 响应消息支持中英文，根据请求头`Accept-Language`返回
7. **日志上报**: 客户端错误建议上报到服务器便于问题排查

## 测试数据

### 测试账号
- 用户名: `admin`
- 密码: `123456`

### 服务器配置测试
- 服务器地址: `localhost:8080`
- 服务器名称: `测试服务器`

### 二维码测试数据
```
*************:8080,测试服务器
localhost:8080,本地服务器
```

## 部署清单

### 必需的环境变量
```bash
# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_NAME=enterprise_app
DB_USER=root
DB_PASSWORD=password

# JWT配置
JWT_SECRET=your_jwt_secret_key
JWT_EXPIRES_IN=7200

# 文件上传配置
UPLOAD_PATH=/uploads
MAX_FILE_SIZE=10485760

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# 服务器配置
SERVER_PORT=8080
SERVER_HOST=0.0.0.0
```

### 数据库初始化脚本
需要创建以下表：
- `users` - 用户表
- `applications` - 应用表
- `app_categories` - 应用分类表
- `messages` - 消息表
- `user_app_favorites` - 用户常用应用表
- `banners` - 轮播图表
- `news` - 新闻表
- `user_settings` - 用户设置表
- `error_logs` - 错误日志表

详细的数据库设计请参考完整的API规范文档。
