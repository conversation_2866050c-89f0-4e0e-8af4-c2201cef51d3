# 免责声明功能文档

## 概述

在设置页面添加了免责声明功能，用于告知用户本软件的性质和使用条款。

## 功能特性

### 1. 多语言支持
- **中文**: 免责声明
- **英文**: Disclaimer

### 2. 内容说明
免责声明明确说明：
- 本软件仅为应用程序外壳
- 不包含任何实际业务内容
- 所有数据来自API接口
- 软件不承担数据准确性责任
- 用户需自行承担使用风险

### 3. UI设计
- 位置：设置页面，退出登录按钮之前
- 样式：卡片式设计，带有信息图标
- 主题支持：自动适配浅色/深色主题
- 响应式：适配不同屏幕尺寸

## 技术实现

### 1. 多语言翻译
在 `lib/services/localization_service.dart` 中添加了翻译键值：

```dart
// 中文翻译
'disclaimer_title': '免责声明',
'disclaimer_content': '本软件仅为应用程序外壳，不包含任何实际业务内容。所有显示的数据均来自API接口，本软件不对数据的准确性、完整性或可靠性承担任何责任。使用本软件即表示您同意自行承担使用风险。',

// 英文翻译
'disclaimer_title': 'Disclaimer',
'disclaimer_content': 'This software is only an application shell and does not contain any actual business content. All displayed data comes from API interfaces. This software does not assume any responsibility for the accuracy, completeness, or reliability of the data. By using this software, you agree to assume the risks of use at your own discretion.',
```

### 2. UI组件
在 `lib/screens/settings_screen.dart` 中添加了 `_buildDisclaimer()` 方法：

```dart
Widget _buildDisclaimer() {
  return Container(
    margin: const EdgeInsets.symmetric(horizontal: AppTheme.paddingMedium),
    padding: const EdgeInsets.all(AppTheme.paddingMedium),
    decoration: BoxDecoration(
      color: AppTheme.getCardColor(context),
      borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
      border: Border.all(
        color: AppTheme.getBorderColor(context),
        width: 1,
      ),
    ),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              Icons.info_outline,
              color: AppTheme.getTextSecondaryColor(context),
              size: 20,
            ),
            const SizedBox(width: AppTheme.paddingSmall),
            Text(
              LocalizationService.t('disclaimer_title'),
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: AppTheme.getTextPrimaryColor(context),
              ),
            ),
          ],
        ),
        const SizedBox(height: AppTheme.paddingSmall),
        Text(
          LocalizationService.t('disclaimer_content'),
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: AppTheme.getTextSecondaryColor(context),
            height: 1.5,
          ),
        ),
      ],
    ),
  );
}
```

### 3. 主题适配
- 使用 `AppTheme.getCardColor(context)` 获取卡片背景色
- 使用 `AppTheme.getBorderColor(context)` 获取边框颜色
- 使用 `AppTheme.getTextPrimaryColor(context)` 和 `AppTheme.getTextSecondaryColor(context)` 获取文本颜色
- 自动适配浅色和深色主题

### 4. 语言切换
- 免责声明内容会根据当前语言设置自动切换
- 支持中文和英文两种语言
- 与应用的整体语言设置保持一致

## 测试

创建了 `test/disclaimer_test.dart` 测试文件，包含：
- 中文翻译存在性测试
- 英文翻译存在性测试
- 翻译差异性测试

运行测试：
```bash
flutter test test/disclaimer_test.dart
```

## 使用方法

1. 打开应用
2. 导航到设置页面
3. 滚动到页面底部
4. 查看免责声明内容
5. 可以通过语言设置切换查看不同语言版本

## 注意事项

- 免责声明内容应根据实际法律要求进行调整
- 建议定期审查免责声明内容的准确性
- 如需修改内容，请同时更新中英文版本
- 确保在不同主题下的可读性
