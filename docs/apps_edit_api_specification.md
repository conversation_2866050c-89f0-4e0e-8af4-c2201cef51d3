# 应用页面编辑功能API接口文档

## 概述

应用页面的编辑功能允许用户管理常用应用，包括添加和移除常用应用。本文档描述了支持此功能所需的后端API接口。

## 接口列表

### 1. 获取用户常用应用列表

**接口地址：** `GET /api/user/favorite-apps`

**请求头：**
```
Authorization: Bearer {token}
Content-Type: application/json
```

**请求参数：** 无

**响应格式：**
```json
{
  "success": true,
  "message": "获取成功",
  "data": {
    "favoriteApps": [
      {
        "id": "app_001",
        "name": "日程管理",
        "category": "办公工具",
        "icon": "schedule",
        "url": "https://example.com/schedule",
        "addedAt": "2024-01-15T10:30:00Z"
      },
      {
        "id": "app_002", 
        "name": "任务协作",
        "category": "办公工具",
        "icon": "task",
        "url": "https://example.com/task",
        "addedAt": "2024-01-16T14:20:00Z"
      }
    ]
  }
}
```

**错误响应：**
```json
{
  "success": false,
  "message": "获取失败",
  "errorCode": "FETCH_FAVORITE_APPS_FAILED"
}
```

### 2. 添加常用应用

**接口地址：** `POST /api/user/favorite-apps`

**请求头：**
```
Authorization: Bearer {token}
Content-Type: application/json
```

**请求参数：**
```json
{
  "appId": "app_003",
  "appName": "文档管理"
}
```

**参数说明：**
- `appId` (string, 必填): 应用ID
- `appName` (string, 必填): 应用名称

**响应格式：**
```json
{
  "success": true,
  "message": "添加成功",
  "data": {
    "appId": "app_003",
    "appName": "文档管理",
    "addedAt": "2024-01-17T09:15:00Z"
  }
}
```

**错误响应：**
```json
{
  "success": false,
  "message": "添加失败",
  "errorCode": "ADD_FAVORITE_APP_FAILED",
  "details": "应用已存在于常用应用列表中"
}
```

### 3. 移除常用应用

**接口地址：** `DELETE /api/user/favorite-apps/{appId}`

**请求头：**
```
Authorization: Bearer {token}
Content-Type: application/json
```

**路径参数：**
- `appId` (string, 必填): 要移除的应用ID

**响应格式：**
```json
{
  "success": true,
  "message": "移除成功",
  "data": {
    "appId": "app_003",
    "removedAt": "2024-01-17T15:30:00Z"
  }
}
```

**错误响应：**
```json
{
  "success": false,
  "message": "移除失败",
  "errorCode": "REMOVE_FAVORITE_APP_FAILED",
  "details": "应用不存在于常用应用列表中"
}
```

### 4. 批量更新常用应用

**接口地址：** `PUT /api/user/favorite-apps`

**请求头：**
```
Authorization: Bearer {token}
Content-Type: application/json
```

**请求参数：**
```json
{
  "favoriteApps": [
    {
      "appId": "app_001",
      "appName": "日程管理",
      "order": 1
    },
    {
      "appId": "app_002",
      "appName": "任务协作", 
      "order": 2
    }
  ]
}
```

**参数说明：**
- `favoriteApps` (array, 必填): 常用应用列表
  - `appId` (string, 必填): 应用ID
  - `appName` (string, 必填): 应用名称
  - `order` (number, 可选): 排序顺序

**响应格式：**
```json
{
  "success": true,
  "message": "更新成功",
  "data": {
    "updatedCount": 2,
    "updatedAt": "2024-01-17T16:45:00Z"
  }
}
```

## 错误码说明

| 错误码 | 说明 | HTTP状态码 |
|--------|------|-----------|
| FETCH_FAVORITE_APPS_FAILED | 获取常用应用失败 | 500 |
| ADD_FAVORITE_APP_FAILED | 添加常用应用失败 | 400 |
| REMOVE_FAVORITE_APP_FAILED | 移除常用应用失败 | 400 |
| UPDATE_FAVORITE_APPS_FAILED | 批量更新常用应用失败 | 400 |
| APP_NOT_FOUND | 应用不存在 | 404 |
| DUPLICATE_FAVORITE_APP | 重复的常用应用 | 409 |
| UNAUTHORIZED | 未授权访问 | 401 |
| TOKEN_EXPIRED | 令牌已过期 | 401 |

## 前端实现说明

### 编辑模式切换
- 用户点击右上角编辑按钮进入编辑模式
- 编辑模式下，每个应用图标右上角显示选择框
- 用户可以点击选择框来添加/移除常用应用
- 点击保存按钮退出编辑模式并保存更改

### 数据同步
- 进入编辑模式时，从接口1获取当前用户的常用应用列表
- 用户操作时，实时更新本地状态
- 保存时，调用接口4批量更新所有更改

### 缓存策略
- 常用应用数据应缓存到本地存储
- 每次进入应用页面时，优先显示缓存数据
- 后台异步更新数据，如有变化则刷新UI

## 注意事项

1. 所有接口都需要用户认证，请在请求头中包含有效的Bearer token
2. 常用应用的数量建议限制在20个以内，以保证良好的用户体验
3. 应用ID应该是全局唯一的标识符
4. 建议实现乐观锁机制，避免并发更新冲突
5. 接口响应时间应控制在2秒以内
