# 工作台页面API需求文档

## 概述
工作台页面需要获取用户的常用应用列表，用于在工作台首页显示快捷入口。

## 接口信息

### 接口地址
```
GET /api/workspace/apps
```

### 请求头
```
Authorization: Bearer {token}
Content-Type: application/json
```

### 前端期望的响应格式

#### 成功响应
```json
{
  "success": true,
  "message": "获取成功",
  "data": {
    "favoriteApps": [
      {
        "id": 1,
        "name": "日程管理",
        "icon": "calendar_today",
        "color": "0xFF3366CC",
        "url": "https://calendar.example.com",
        "description": "管理日程安排",
        "sort": 1
      },
      {
        "id": 2,
        "name": "任务协作",
        "icon": "assignment",
        "color": "0xFFFF9900",
        "url": "https://tasks.example.com",
        "description": "团队任务协作",
        "sort": 2
      },
      {
        "id": 3,
        "name": "文档中心",
        "icon": "description",
        "color": "0xFF52C41A",
        "url": "https://docs.example.com",
        "description": "文档管理中心",
        "sort": 3
      }
    ]
  }
}
```

#### 失败响应
```json
{
  "success": false,
  "message": "获取失败",
  "error": "具体错误信息",
  "timestamp": 1751817585
}
```

## 字段说明

### favoriteApps 数组中每个应用对象的字段：

| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | number | 是 | 应用唯一标识 |
| name | string | 是 | 应用名称，用于显示 |
| icon | string | 是 | 图标名称，支持Material Design图标 |
| color | string | 是 | 应用颜色，格式为"0xFFRRGGBB" |
| url | string | 否 | 应用访问地址 |
| description | string | 否 | 应用描述 |
| sort | number | 否 | 排序权重，数字越小越靠前 |

### 支持的图标名称（icon字段）：
- calendar_today (日历)
- assignment (任务)
- description (文档)
- business (客户管理)
- folder_open (项目管理)
- pie_chart (报表)
- email (邮件)
- video_call (视频会议)
- people (人员管理)
- access_time (考勤)
- trending_up (销售)
- chat (聊天)
- receipt (报销)
- account_balance (预算)
- apps (默认图标)

### 颜色格式说明：
- 使用Android/Flutter颜色格式："0xFFRRGGBB"
- 其中FF表示不透明度，RRGGBB为RGB颜色值
- 示例：
  - 蓝色：0xFF3366CC
  - 橙色：0xFFFF9900
  - 绿色：0xFF52C41A

## 前端处理逻辑

1. **数据验证**：前端会验证必填字段，如果缺失会使用默认值
2. **排序**：按照sort字段升序排列，如果没有sort字段则按照数组顺序
3. **图标处理**：如果icon字段不在支持列表中，会使用默认的"apps"图标
4. **颜色处理**：如果color字段格式不正确，会根据应用名称生成默认颜色
5. **错误处理**：如果接口返回失败或异常，前端会使用本地默认数据

## 当前状态

**接口状态**：❌ 未实现
**错误信息**：接口不存在 (Not Found)
**前端处理**：使用本地默认数据作为备用

## 建议实现步骤

1. 创建工作台应用数据表
2. 实现用户常用应用的增删改查
3. 实现GET /api/workspace/apps接口
4. 添加用户权限验证
5. 支持应用排序功能

## 测试建议

建议后端实现时返回3-6个测试应用数据，包含不同类型的图标和颜色，以便前端测试显示效果。
