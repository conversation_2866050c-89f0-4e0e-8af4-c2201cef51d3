# 网络错误处理和离线优先策略

本文档介绍了为离线优先应用实现的多层网络错误处理机制，包括网络状态检测、智能重试、错误分类和缓存优先策略。

## 核心特性

### 1. 多层错误处理
- **网络状态检测**: 实时监控网络连接状态
- **智能重试机制**: 根据错误类型和网络状态智能重试
- **请求去重**: 防止短时间内重复请求
- **缓存优先**: 离线时优先使用缓存数据

### 2. 错误分类
- `NoConnection`: 无网络连接
- `Timeout`: 请求超时
- `ServerError`: 服务器错误 (5xx)
- `ClientError`: 客户端错误 (4xx)
- `RateLimited`: 请求频率限制
- `Unknown`: 未知错误

### 3. 重试策略
- `None`: 不重试
- `Immediate`: 立即重试
- `Exponential`: 指数退避
- `Linear`: 线性退避
- `Custom`: 自定义策略

## 使用方法

### 1. 初始化服务

```dart
import 'package:your_app/services/app_initializer.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // 初始化所有网络相关服务
  await AppInitializer.initialize();
  
  runApp(MyApp());
}
```

### 2. 使用API服务

```dart
// 基本用法 - 自动使用缓存
final result = await ApiService.getHomeInfo();

// 强制刷新 - 跳过缓存
final result = await ApiService.getHomeInfo(forceRefresh: true);

// 处理响应
if (result['success']) {
  final data = result['data'];
  final fromCache = result['fromCache'] ?? false;
  
  if (fromCache) {
    // 显示缓存数据提示
    showSnackBar('显示缓存数据');
  }
} else {
  // 处理错误
  final errorMessage = result['message'] ?? '请求失败';
  showSnackBar(errorMessage);
}
```

### 3. 使用HTTP客户端

```dart
import 'package:your_app/services/http_client.dart';

final httpClient = HttpClient();

// GET请求with缓存
final response = await httpClient.get(
  'https://api.example.com/data',
  useCache: true,
  cacheExpiry: Duration(hours: 1),
  retryConfig: RetryConfig.offlineFirst,
);

// POST请求
final response = await httpClient.post(
  'https://api.example.com/submit',
  body: {'key': 'value'},
  retryConfig: RetryConfig.realtime,
);
```

### 4. 网络状态监控

```dart
import 'package:your_app/services/network_status_service.dart';

final networkStatus = NetworkStatusService();

// 获取当前状态
final isConnected = networkStatus.isConnected;
final isWiFi = networkStatus.isWiFi;
final isMetered = networkStatus.isMetered;

// 监听状态变化
networkStatus.statusStream.listen((status) {
  print('网络状态: ${status.isConnected ? '已连接' : '已断开'}');
  print('连接类型: ${status.type}');
});
```

### 5. 网络状态指示器

```dart
import 'package:your_app/widgets/network_status_indicator.dart';

// 在页面中使用
@override
Widget build(BuildContext context) {
  return Scaffold(
    body: NetworkStatusBanner(
      showWhenConnected: true, // 连接时也显示状态
      child: YourPageContent(),
    ),
  );
}
```

## 配置选项

### 重试配置

```dart
// 离线优先配置
const offlineFirstConfig = RetryConfig(
  maxRetries: 2,
  initialDelay: Duration(seconds: 2),
  maxDelay: Duration(seconds: 10),
  strategy: RetryStrategy.linear,
  retryableErrors: [
    NetworkErrorType.noConnection,
    NetworkErrorType.timeout,
  ],
);

// 实时数据配置
const realtimeConfig = RetryConfig(
  maxRetries: 5,
  initialDelay: Duration(milliseconds: 500),
  maxDelay: Duration(seconds: 5),
  strategy: RetryStrategy.exponential,
  retryableErrors: [
    NetworkErrorType.noConnection,
    NetworkErrorType.timeout,
    NetworkErrorType.serverError,
  ],
);
```

### 缓存配置

```dart
// 设置缓存数据
await CacheService.setCachedData(
  'my_data_key',
  {'key': 'value'},
  expiry: Duration(hours: 24),
);

// 获取缓存数据
final cachedData = await CacheService.getCachedData('my_data_key');

// 清理过期缓存
await CacheService.clearExpiredCache();
```

## 最佳实践

### 1. 离线优先策略
- 首次加载时优先显示缓存数据
- 后台异步更新数据
- 仅在用户主动刷新时强制请求网络

### 2. 错误处理
- 区分网络错误和业务错误
- 为用户提供清晰的错误信息
- 在离线状态下提供有意义的功能

### 3. 性能优化
- 使用请求去重避免重复请求
- 合理设置缓存过期时间
- 在计费网络下减少不必要的请求

### 4. 用户体验
- 显示网络状态指示器
- 提供离线模式提示
- 支持手动重试功能

## 错误处理流程

```
请求开始
    ↓
检查网络状态
    ↓
网络可用? → 否 → 返回缓存数据或错误
    ↓ 是
执行网络请求
    ↓
请求成功? → 是 → 更新缓存并返回数据
    ↓ 否
分析错误类型
    ↓
可重试错误? → 否 → 返回缓存数据或错误
    ↓ 是
达到重试上限? → 是 → 返回缓存数据或错误
    ↓ 否
等待重试延迟
    ↓
重新执行请求
```

## 监控和调试

### 错误统计
```dart
final errorStats = HttpClient().getErrorStats();
print('错误统计: $errorStats');
```

### 网络状态日志
```dart
// 启用调试日志
debugPrint('当前网络状态: ${networkStatus.currentStatus}');
```

### 缓存状态检查
```dart
final hasCache = await CacheService.hasHomeCache();
final cacheTime = await CacheService.getHomeCacheTimestamp();
print('缓存状态: $hasCache, 更新时间: $cacheTime');
```

这个多层错误处理机制确保了应用在各种网络条件下都能提供良好的用户体验，特别适合主要离线使用的企业应用场景。
