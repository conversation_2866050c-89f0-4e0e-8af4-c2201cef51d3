# 工作台页面编辑功能API接口文档

## 概述

工作台页面的编辑功能允许用户管理常用应用的排序。用户可以通过编辑模式调整应用在工作台中的显示顺序。本文档描述了支持此功能所需的后端API接口。

## 接口列表

### 1. 获取用户工作台应用列表

**接口地址：** `GET /api/user/workspace-apps`

**请求头：**
```
Authorization: Bearer {token}
Content-Type: application/json
```

**请求参数：** 无

**响应格式：**
```json
{
  "success": true,
  "message": "获取成功",
  "data": {
    "favoriteApps": [
      {
        "id": "app_001",
        "name": "日程管理",
        "category": "办公工具",
        "icon": "schedule",
        "color": "0xFF2196F3",
        "url": "https://example.com/schedule",
        "order": 1,
        "addedAt": "2024-01-15T10:30:00Z",
        "lastUsedAt": "2024-01-20T14:25:00Z"
      },
      {
        "id": "app_002",
        "name": "任务协作",
        "category": "办公工具", 
        "icon": "task",
        "color": "0xFF4CAF50",
        "url": "https://example.com/task",
        "order": 2,
        "addedAt": "2024-01-16T14:20:00Z",
        "lastUsedAt": "2024-01-19T09:15:00Z"
      }
    ]
  }
}
```

**字段说明：**
- `order` (number): 应用在工作台中的排序位置，数值越小排序越靠前
- `color` (string): 应用图标背景色，格式为十六进制颜色值
- `lastUsedAt` (string): 最后使用时间，用于统计和排序参考

**错误响应：**
```json
{
  "success": false,
  "message": "获取失败",
  "errorCode": "FETCH_WORKSPACE_APPS_FAILED"
}
```

### 2. 更新应用排序

**接口地址：** `PUT /api/user/workspace-apps/order`

**请求头：**
```
Authorization: Bearer {token}
Content-Type: application/json
```

**请求参数：**
```json
{
  "appOrders": [
    {
      "appId": "app_002",
      "order": 1
    },
    {
      "appId": "app_001", 
      "order": 2
    },
    {
      "appId": "app_003",
      "order": 3
    }
  ]
}
```

**参数说明：**
- `appOrders` (array, 必填): 应用排序列表
  - `appId` (string, 必填): 应用ID
  - `order` (number, 必填): 新的排序位置

**响应格式：**
```json
{
  "success": true,
  "message": "排序更新成功",
  "data": {
    "updatedCount": 3,
    "updatedAt": "2024-01-20T16:45:00Z"
  }
}
```

**错误响应：**
```json
{
  "success": false,
  "message": "排序更新失败",
  "errorCode": "UPDATE_APP_ORDER_FAILED",
  "details": "应用ID不存在或排序位置无效"
}
```

### 3. 移动单个应用位置

**接口地址：** `PATCH /api/user/workspace-apps/{appId}/move`

**请求头：**
```
Authorization: Bearer {token}
Content-Type: application/json
```

**路径参数：**
- `appId` (string, 必填): 要移动的应用ID

**请求参数：**
```json
{
  "direction": "forward",
  "steps": 1
}
```

**参数说明：**
- `direction` (string, 必填): 移动方向，可选值：
  - `forward`: 向前移动（排序数值减小）
  - `backward`: 向后移动（排序数值增大）
- `steps` (number, 可选): 移动步数，默认为1

**响应格式：**
```json
{
  "success": true,
  "message": "应用位置移动成功",
  "data": {
    "appId": "app_001",
    "appName": "日程管理",
    "oldOrder": 3,
    "newOrder": 2,
    "movedAt": "2024-01-20T17:30:00Z"
  }
}
```

**错误响应：**
```json
{
  "success": false,
  "message": "移动失败",
  "errorCode": "MOVE_APP_FAILED",
  "details": "应用已在最前/最后位置"
}
```

### 4. 重置工作台应用排序

**接口地址：** `POST /api/user/workspace-apps/reset-order`

**请求头：**
```
Authorization: Bearer {token}
Content-Type: application/json
```

**请求参数：**
```json
{
  "sortBy": "lastUsed"
}
```

**参数说明：**
- `sortBy` (string, 可选): 重置排序的依据，可选值：
  - `lastUsed`: 按最后使用时间排序（默认）
  - `name`: 按应用名称排序
  - `addedTime`: 按添加时间排序

**响应格式：**
```json
{
  "success": true,
  "message": "排序重置成功",
  "data": {
    "resetCount": 5,
    "sortBy": "lastUsed",
    "resetAt": "2024-01-20T18:00:00Z"
  }
}
```

## 错误码说明

| 错误码 | 说明 | HTTP状态码 |
|--------|------|-----------|
| FETCH_WORKSPACE_APPS_FAILED | 获取工作台应用失败 | 500 |
| UPDATE_APP_ORDER_FAILED | 更新应用排序失败 | 400 |
| MOVE_APP_FAILED | 移动应用位置失败 | 400 |
| RESET_ORDER_FAILED | 重置排序失败 | 400 |
| APP_NOT_FOUND | 应用不存在 | 404 |
| INVALID_ORDER_POSITION | 无效的排序位置 | 400 |
| UNAUTHORIZED | 未授权访问 | 401 |
| TOKEN_EXPIRED | 令牌已过期 | 401 |

## 前端实现说明

### 编辑模式切换
- 用户点击右上角编辑按钮进入编辑模式
- 编辑模式下，每个应用右上角显示编辑图标
- 用户点击应用弹出排序对话框，可选择向前/向后移动
- 点击保存按钮退出编辑模式并保存更改

### 排序逻辑
- 应用按order字段升序排列显示
- 向前移动：order值减1，其他应用相应调整
- 向后移动：order值加1，其他应用相应调整
- 确保order值连续且唯一

### 数据同步
- 进入编辑模式时，从接口1获取当前排序
- 用户操作时，实时更新本地状态
- 保存时，调用接口2批量更新排序

### 缓存策略
- 工作台应用数据应缓存到本地存储
- 每次进入工作台页面时，优先显示缓存数据
- 后台异步更新数据，如有变化则刷新UI

## 注意事项

1. 所有接口都需要用户认证，请在请求头中包含有效的Bearer token
2. 排序位置应该是连续的正整数，从1开始
3. 移动应用时需要重新计算其他应用的排序位置
4. 建议实现乐观锁机制，避免并发更新冲突
5. 接口响应时间应控制在2秒以内
6. 工作台应用数量建议限制在20个以内
