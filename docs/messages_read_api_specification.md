# 消息页面已读功能API接口文档

## 概述

消息页面的已读功能允许用户标记消息为已读状态，管理未读消息计数。用户点击消息时会自动标记为已读，同时支持批量标记已读操作。本文档描述了支持此功能所需的后端API接口。

## 接口列表

### 1. 获取用户消息列表

**接口地址：** `GET /api/user/messages`

**请求头：**
```
Authorization: Bearer {token}
Content-Type: application/json
```

**请求参数：**
```
type (string, 可选): 消息类型过滤，可选值：all, unread, system, work
page (number, 可选): 页码，默认为1
pageSize (number, 可选): 每页数量，默认为20
```

**响应格式：**
```json
{
  "success": true,
  "message": "获取成功",
  "data": {
    "messages": [
      {
        "id": "msg_001",
        "title": "系统通知",
        "content": "系统将于今晚22:00-24:00进行维护升级",
        "type": "system",
        "priority": "normal",
        "icon": "notifications",
        "color": "0xFF3366CC",
        "unreadCount": 0,
        "isRead": true,
        "createdAt": "2024-01-20T09:15:00Z",
        "readAt": "2024-01-20T10:30:00Z",
        "url": "https://example.com/notice/001"
      },
      {
        "id": "msg_002",
        "title": "审批提醒",
        "content": "您有2个待审批的申请需要处理",
        "type": "work",
        "priority": "high",
        "icon": "assignment_turned_in",
        "color": "0xFFFF9900",
        "unreadCount": 2,
        "isRead": false,
        "createdAt": "2024-01-19T14:20:00Z",
        "readAt": null,
        "url": "https://example.com/approval/pending"
      }
    ],
    "pagination": {
      "currentPage": 1,
      "pageSize": 20,
      "totalCount": 45,
      "totalPages": 3
    },
    "summary": {
      "totalUnread": 8,
      "systemUnread": 2,
      "workUnread": 6
    }
  }
}
```

**字段说明：**
- `unreadCount` (number): 该消息的未读数量（对于聚合消息）
- `isRead` (boolean): 消息是否已读
- `priority` (string): 消息优先级：normal, high, urgent
- `readAt` (string|null): 已读时间，未读时为null

### 2. 标记单个消息为已读

**接口地址：** `PATCH /api/user/messages/{messageId}/read`

**请求头：**
```
Authorization: Bearer {token}
Content-Type: application/json
```

**路径参数：**
- `messageId` (string, 必填): 消息ID

**请求参数：** 无

**响应格式：**
```json
{
  "success": true,
  "message": "消息已标记为已读",
  "data": {
    "messageId": "msg_002",
    "readAt": "2024-01-20T15:30:00Z",
    "previousUnreadCount": 2,
    "currentUnreadCount": 0
  }
}
```

**错误响应：**
```json
{
  "success": false,
  "message": "标记失败",
  "errorCode": "MARK_READ_FAILED",
  "details": "消息不存在或已被删除"
}
```

### 3. 批量标记消息为已读

**接口地址：** `PATCH /api/user/messages/batch-read`

**请求头：**
```
Authorization: Bearer {token}
Content-Type: application/json
```

**请求参数：**
```json
{
  "messageIds": ["msg_001", "msg_002", "msg_003"],
  "markAll": false,
  "filter": {
    "type": "work",
    "beforeDate": "2024-01-20T00:00:00Z"
  }
}
```

**参数说明：**
- `messageIds` (array, 可选): 指定消息ID列表
- `markAll` (boolean, 可选): 是否标记所有消息为已读
- `filter` (object, 可选): 过滤条件，当markAll为true时生效
  - `type` (string, 可选): 消息类型
  - `beforeDate` (string, 可选): 标记此日期之前的消息

**响应格式：**
```json
{
  "success": true,
  "message": "批量标记成功",
  "data": {
    "markedCount": 3,
    "failedCount": 0,
    "markedAt": "2024-01-20T16:00:00Z",
    "failedMessages": []
  }
}
```

### 4. 标记消息为未读

**接口地址：** `PATCH /api/user/messages/{messageId}/unread`

**请求头：**
```
Authorization: Bearer {token}
Content-Type: application/json
```

**路径参数：**
- `messageId` (string, 必填): 消息ID

**响应格式：**
```json
{
  "success": true,
  "message": "消息已标记为未读",
  "data": {
    "messageId": "msg_001",
    "markedUnreadAt": "2024-01-20T16:30:00Z"
  }
}
```

### 5. 获取未读消息统计

**接口地址：** `GET /api/user/messages/unread-summary`

**请求头：**
```
Authorization: Bearer {token}
Content-Type: application/json
```

**响应格式：**
```json
{
  "success": true,
  "message": "获取成功",
  "data": {
    "totalUnread": 8,
    "byType": {
      "system": 2,
      "work": 6
    },
    "byPriority": {
      "normal": 3,
      "high": 4,
      "urgent": 1
    },
    "latestUnreadMessage": {
      "id": "msg_005",
      "title": "紧急通知",
      "createdAt": "2024-01-20T16:45:00Z"
    }
  }
}
```

### 6. 删除已读消息

**接口地址：** `DELETE /api/user/messages/read-messages`

**请求头：**
```
Authorization: Bearer {token}
Content-Type: application/json
```

**请求参数：**
```json
{
  "beforeDate": "2024-01-15T00:00:00Z",
  "messageType": "system",
  "keepDays": 30
}
```

**参数说明：**
- `beforeDate` (string, 可选): 删除此日期之前的已读消息
- `messageType` (string, 可选): 指定消息类型
- `keepDays` (number, 可选): 保留最近N天的消息

**响应格式：**
```json
{
  "success": true,
  "message": "已读消息删除成功",
  "data": {
    "deletedCount": 15,
    "deletedAt": "2024-01-20T17:00:00Z"
  }
}
```

## 错误码说明

| 错误码 | 说明 | HTTP状态码 |
|--------|------|-----------|
| FETCH_MESSAGES_FAILED | 获取消息列表失败 | 500 |
| MARK_READ_FAILED | 标记已读失败 | 400 |
| MARK_UNREAD_FAILED | 标记未读失败 | 400 |
| BATCH_MARK_FAILED | 批量标记失败 | 400 |
| DELETE_MESSAGES_FAILED | 删除消息失败 | 400 |
| MESSAGE_NOT_FOUND | 消息不存在 | 404 |
| INVALID_MESSAGE_TYPE | 无效的消息类型 | 400 |
| UNAUTHORIZED | 未授权访问 | 401 |
| TOKEN_EXPIRED | 令牌已过期 | 401 |

## 前端实现说明

### 自动标记已读
- 用户点击消息时自动调用标记已读接口
- 标记成功后更新本地消息状态
- 更新未读计数显示

### 未读消息显示
- 未读消息显示红色数字徽章
- 支持按消息类型过滤未读消息
- 实时更新未读消息统计

### 批量操作
- 提供"全部标记为已读"功能
- 支持按类型批量标记
- 提供清理已读消息功能

### 数据同步
- 消息列表数据应缓存到本地存储
- 定期同步未读消息统计
- 支持离线查看已缓存的消息

## 注意事项

1. 所有接口都需要用户认证，请在请求头中包含有效的Bearer token
2. 消息ID应该是全局唯一的标识符
3. 未读计数应该实时更新，避免数据不一致
4. 建议实现消息推送机制，及时通知用户新消息
5. 接口响应时间应控制在2秒以内
6. 支持消息分页加载，避免一次性加载过多数据
7. 已读状态变更应该记录操作时间，便于审计和统计
