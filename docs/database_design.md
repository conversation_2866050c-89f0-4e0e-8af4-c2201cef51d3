# 企业混合App数据库设计文档

## 概述

本文档描述了企业混合App的数据库设计，包括表结构、字段说明、索引设计和示例数据。

## 数据库信息

- **数据库类型**: MySQL 8.0+
- **字符集**: utf8mb4
- **排序规则**: utf8mb4_unicode_ci
- **存储引擎**: InnoDB

## 表结构设计

### 1. 用户表 (users)

```sql
CREATE TABLE users (
  id INT PRIMARY KEY AUTO_INCREMENT COMMENT '用户ID',
  username VARCHAR(50) UNIQUE NOT NULL COMMENT '用户名',
  password VARCHAR(255) NOT NULL COMMENT '密码(加密)',
  name VARCHAR(100) NOT NULL COMMENT '真实姓名',
  email VARCHAR(100) COMMENT '邮箱',
  phone VARCHAR(20) COMMENT '手机号',
  avatar VARCHAR(255) COMMENT '头像URL',
  department VARCHAR(100) COMMENT '部门',
  position VARCHAR(100) COMMENT '职位',
  status TINYINT DEFAULT 1 COMMENT '状态(1:正常 0:禁用)',
  last_login_time TIMESTAMP NULL COMMENT '最后登录时间',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  INDEX idx_username (username),
  INDEX idx_status (status),
  INDEX idx_department (department)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';
```

### 2. 应用分类表 (app_categories)

```sql
CREATE TABLE app_categories (
  id INT PRIMARY KEY AUTO_INCREMENT COMMENT '分类ID',
  title VARCHAR(100) NOT NULL COMMENT '分类名称',
  sort INT DEFAULT 0 COMMENT '排序',
  status TINYINT DEFAULT 1 COMMENT '状态(1:正常 0:禁用)',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  INDEX idx_sort (sort),
  INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='应用分类表';
```

### 3. 应用表 (applications)

```sql
CREATE TABLE applications (
  id INT PRIMARY KEY AUTO_INCREMENT COMMENT '应用ID',
  name VARCHAR(100) NOT NULL COMMENT '应用名称',
  icon VARCHAR(100) COMMENT '图标名称',
  color VARCHAR(20) COMMENT '颜色值',
  url VARCHAR(255) COMMENT '应用URL',
  description TEXT COMMENT '应用描述',
  category_id INT NOT NULL COMMENT '分类ID',
  sort INT DEFAULT 0 COMMENT '排序',
  status TINYINT DEFAULT 1 COMMENT '状态(1:正常 0:禁用)',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  INDEX idx_category_id (category_id),
  INDEX idx_sort (sort),
  INDEX idx_status (status),
  INDEX idx_name (name),
  FOREIGN KEY (category_id) REFERENCES app_categories(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='应用表';
```

### 4. 用户常用应用表 (user_app_favorites)

```sql
CREATE TABLE user_app_favorites (
  id INT PRIMARY KEY AUTO_INCREMENT COMMENT 'ID',
  user_id INT NOT NULL COMMENT '用户ID',
  app_id INT NOT NULL COMMENT '应用ID',
  sort INT DEFAULT 0 COMMENT '排序',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  
  UNIQUE KEY uk_user_app (user_id, app_id),
  INDEX idx_user_id (user_id),
  INDEX idx_sort (sort),
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  FOREIGN KEY (app_id) REFERENCES applications(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户常用应用表';
```

### 5. 消息表 (messages)

```sql
CREATE TABLE messages (
  id INT PRIMARY KEY AUTO_INCREMENT COMMENT '消息ID',
  title VARCHAR(200) NOT NULL COMMENT '消息标题',
  content TEXT COMMENT '消息内容',
  type VARCHAR(20) DEFAULT 'system' COMMENT '消息类型(system/work)',
  icon VARCHAR(50) COMMENT '图标',
  color VARCHAR(20) COMMENT '颜色',
  sender_id INT COMMENT '发送者ID',
  status TINYINT DEFAULT 1 COMMENT '状态(1:正常 0:删除)',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  INDEX idx_type (type),
  INDEX idx_status (status),
  INDEX idx_created_at (created_at),
  INDEX idx_sender_id (sender_id),
  FOREIGN KEY (sender_id) REFERENCES users(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='消息表';
```

### 6. 用户消息读取状态表 (user_message_reads)

```sql
CREATE TABLE user_message_reads (
  id INT PRIMARY KEY AUTO_INCREMENT COMMENT 'ID',
  user_id INT NOT NULL COMMENT '用户ID',
  message_id INT NOT NULL COMMENT '消息ID',
  is_read TINYINT DEFAULT 0 COMMENT '是否已读(1:已读 0:未读)',
  read_time TIMESTAMP NULL COMMENT '阅读时间',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  
  UNIQUE KEY uk_user_message (user_id, message_id),
  INDEX idx_user_id (user_id),
  INDEX idx_is_read (is_read),
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  FOREIGN KEY (message_id) REFERENCES messages(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户消息读取状态表';
```

### 7. 轮播图表 (banners)

```sql
CREATE TABLE banners (
  id INT PRIMARY KEY AUTO_INCREMENT COMMENT '轮播图ID',
  title VARCHAR(200) NOT NULL COMMENT '标题',
  image VARCHAR(255) NOT NULL COMMENT '图片URL',
  url VARCHAR(255) COMMENT '跳转链接',
  sort INT DEFAULT 0 COMMENT '排序',
  status TINYINT DEFAULT 1 COMMENT '状态(1:正常 0:禁用)',
  start_time TIMESTAMP NULL COMMENT '开始时间',
  end_time TIMESTAMP NULL COMMENT '结束时间',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  INDEX idx_sort (sort),
  INDEX idx_status (status),
  INDEX idx_time (start_time, end_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='轮播图表';
```

### 8. 新闻表 (news)

```sql
CREATE TABLE news (
  id INT PRIMARY KEY AUTO_INCREMENT COMMENT '新闻ID',
  title VARCHAR(200) NOT NULL COMMENT '新闻标题',
  summary TEXT COMMENT '新闻摘要',
  content LONGTEXT COMMENT '新闻内容',
  category VARCHAR(50) COMMENT '新闻分类',
  author VARCHAR(100) COMMENT '作者',
  read_count INT DEFAULT 0 COMMENT '阅读次数',
  url VARCHAR(255) COMMENT '外部链接',
  status TINYINT DEFAULT 1 COMMENT '状态(1:发布 0:草稿)',
  publish_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '发布时间',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  INDEX idx_category (category),
  INDEX idx_status (status),
  INDEX idx_publish_time (publish_time),
  INDEX idx_title (title)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='新闻表';
```

### 9. 用户设置表 (user_settings)

```sql
CREATE TABLE user_settings (
  id INT PRIMARY KEY AUTO_INCREMENT COMMENT 'ID',
  user_id INT NOT NULL COMMENT '用户ID',
  theme VARCHAR(20) DEFAULT 'light' COMMENT '主题(light/dark)',
  language VARCHAR(10) DEFAULT 'zh' COMMENT '语言(zh/en)',
  push_enabled TINYINT DEFAULT 1 COMMENT '推送通知(1:开启 0:关闭)',
  email_enabled TINYINT DEFAULT 0 COMMENT '邮件通知(1:开启 0:关闭)',
  system_notifications TINYINT DEFAULT 1 COMMENT '系统通知(1:开启 0:关闭)',
  work_notifications TINYINT DEFAULT 1 COMMENT '工作通知(1:开启 0:关闭)',
  quiet_hours_enabled TINYINT DEFAULT 0 COMMENT '免打扰时间(1:开启 0:关闭)',
  quiet_start_time TIME COMMENT '免打扰开始时间',
  quiet_end_time TIME COMMENT '免打扰结束时间',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  UNIQUE KEY uk_user_id (user_id),
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户设置表';
```

### 10. 错误日志表 (error_logs)

```sql
CREATE TABLE error_logs (
  id INT PRIMARY KEY AUTO_INCREMENT COMMENT '日志ID',
  user_id INT COMMENT '用户ID',
  type VARCHAR(50) NOT NULL COMMENT '错误类型',
  message TEXT NOT NULL COMMENT '错误消息',
  stack_trace LONGTEXT COMMENT '堆栈信息',
  device_info JSON COMMENT '设备信息',
  app_version VARCHAR(20) COMMENT '应用版本',
  ip_address VARCHAR(45) COMMENT 'IP地址',
  user_agent TEXT COMMENT '用户代理',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  
  INDEX idx_user_id (user_id),
  INDEX idx_type (type),
  INDEX idx_created_at (created_at),
  FOREIGN KEY (user_id) REFERENCES users(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='错误日志表';
```

### 11. 用户操作日志表 (user_action_logs)

```sql
CREATE TABLE user_action_logs (
  id INT PRIMARY KEY AUTO_INCREMENT COMMENT '日志ID',
  user_id INT NOT NULL COMMENT '用户ID',
  action VARCHAR(50) NOT NULL COMMENT '操作类型',
  description VARCHAR(255) COMMENT '操作描述',
  ip_address VARCHAR(45) COMMENT 'IP地址',
  user_agent TEXT COMMENT '用户代理',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  
  INDEX idx_user_id (user_id),
  INDEX idx_action (action),
  INDEX idx_created_at (created_at),
  FOREIGN KEY (user_id) REFERENCES users(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户操作日志表';
```

### 12. 系统配置表 (system_configs)

```sql
CREATE TABLE system_configs (
  id INT PRIMARY KEY AUTO_INCREMENT COMMENT '配置ID',
  config_key VARCHAR(100) UNIQUE NOT NULL COMMENT '配置键',
  config_value TEXT COMMENT '配置值',
  description VARCHAR(255) COMMENT '配置描述',
  type VARCHAR(20) DEFAULT 'string' COMMENT '值类型(string/number/boolean/json)',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  INDEX idx_config_key (config_key)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统配置表';
```

## 初始化数据

### 1. 应用分类数据

```sql
INSERT INTO app_categories (id, title, sort) VALUES
(1, '办公协作', 1),
(2, '业务管理', 2),
(3, '人事管理', 3);
```

### 2. 应用数据

```sql
INSERT INTO applications (id, name, icon, color, url, description, category_id, sort) VALUES
(1, '日程管理', 'calendar_today', '0xFF3366CC', 'https://calendar.example.com', '管理个人和团队日程安排', 1, 1),
(2, '任务协作', 'assignment', '0xFFFF9900', 'https://tasks.example.com', '团队任务分配和协作', 1, 2),
(3, '文档中心', 'description', '0xFF52C41A', 'https://docs.example.com', '企业文档管理和共享', 1, 3),
(4, '客户管理', 'people', '0xFF9C27B0', 'https://crm.example.com', '客户关系管理系统', 2, 1),
(5, '销售统计', 'bar_chart', '0xFF00BCD4', 'https://sales.example.com', '销售数据统计分析', 2, 2),
(6, '考勤打卡', 'access_time', '0xFFFF5722', 'https://attendance.example.com', '员工考勤管理', 3, 1),
(7, '请假申请', 'event_busy', '0xFF795548', 'https://leave.example.com', '在线请假申请系统', 3, 2);
```

### 3. 测试用户数据

```sql
INSERT INTO users (id, username, password, name, email, phone, department, position) VALUES
(1, 'admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVEFDi', '管理员', '<EMAIL>', '13800138000', '信息技术部', '系统管理员');
-- 密码: 123456 (BCrypt加密)
```

### 4. 系统配置数据

```sql
INSERT INTO system_configs (config_key, config_value, description, type) VALUES
('system.name', '企业办公移动系统', '系统名称', 'string'),
('system.version', '2.1.0', '系统版本', 'string'),
('system.logo', 'https://picsum.photos/100/100?random=logo', '系统Logo', 'string'),
('system.description', '高效协作，智能办公', '系统描述', 'string'),
('system.company', '科技有限公司', '公司名称', 'string'),
('system.copyright', '© 2024 科技有限公司 版权所有', '版权信息', 'string');
```

## 索引优化建议

1. **用户表**: 在username、status、department字段上建立索引
2. **应用表**: 在category_id、sort、status、name字段上建立索引
3. **消息表**: 在type、status、created_at字段上建立索引
4. **日志表**: 在user_id、created_at字段上建立索引，定期清理旧数据

## 数据维护

### 1. 定期清理策略

```sql
-- 清理30天前的错误日志
DELETE FROM error_logs WHERE created_at < DATE_SUB(NOW(), INTERVAL 30 DAY);

-- 清理90天前的用户操作日志
DELETE FROM user_action_logs WHERE created_at < DATE_SUB(NOW(), INTERVAL 90 DAY);
```

### 2. 备份策略

- 每日增量备份
- 每周全量备份
- 重要数据实时同步到备库

### 3. 性能监控

- 监控慢查询日志
- 定期分析表空间使用情况
- 监控索引使用效率
