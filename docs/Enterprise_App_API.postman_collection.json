{"info": {"name": "企业混合App API", "description": "企业混合App后端API接口集合", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseUrl", "value": "http://localhost:8080/api", "type": "string"}, {"key": "token", "value": "", "type": "string"}], "item": [{"name": "认证接口", "item": [{"name": "用户登录", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"admin\",\n  \"password\": \"123456\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/login", "host": ["{{baseUrl}}"], "path": ["auth", "login"]}}, "response": []}, {"name": "刷新Token", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"refreshToken\": \"refresh_token_string\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/refresh", "host": ["{{baseUrl}}"], "path": ["auth", "refresh"]}}, "response": []}, {"name": "用户登出", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/auth/logout", "host": ["{{baseUrl}}"], "path": ["auth", "logout"]}}, "response": []}]}, {"name": "系统接口", "item": [{"name": "服务器连接测试", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/system/ping", "host": ["{{baseUrl}}"], "path": ["system", "ping"]}}, "response": []}, {"name": "获取服务器信息", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/system/info", "host": ["{{baseUrl}}"], "path": ["system", "info"]}}, "response": []}]}, {"name": "首页接口", "item": [{"name": "获取首页信息", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/home/<USER>", "host": ["{{baseUrl}}"], "path": ["home", "info"], "query": [{"key": "forceRefresh", "value": "false"}]}}, "response": []}, {"name": "获取首页信息(强制刷新)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/home/<USER>", "host": ["{{baseUrl}}"], "path": ["home", "info"], "query": [{"key": "forceRefresh", "value": "true"}]}}, "response": []}]}, {"name": "应用接口", "item": [{"name": "获取应用列表", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/apps/list?forceRefresh=false", "host": ["{{baseUrl}}"], "path": ["apps", "list"], "query": [{"key": "forceRefresh", "value": "false"}]}}, "response": []}, {"name": "保存常用应用", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"appIds\": [1, 2, 3]\n}"}, "url": {"raw": "{{baseUrl}}/apps/favorites", "host": ["{{baseUrl}}"], "path": ["apps", "favorites"]}}, "response": []}, {"name": "保存应用排序", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"apps\": [\n    {\n      \"id\": 1,\n      \"sort\": 1\n    },\n    {\n      \"id\": 2,\n      \"sort\": 2\n    }\n  ]\n}"}, "url": {"raw": "{{baseUrl}}/apps/sort", "host": ["{{baseUrl}}"], "path": ["apps", "sort"]}}, "response": []}]}, {"name": "工作台接口", "item": [{"name": "获取工作台应用", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/workspace/apps", "host": ["{{baseUrl}}"], "path": ["workspace", "apps"]}}, "response": []}]}, {"name": "消息接口", "item": [{"name": "获取消息列表", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/messages/list?type=all&page=1&pageSize=20", "host": ["{{baseUrl}}"], "path": ["messages", "list"], "query": [{"key": "type", "value": "all"}, {"key": "page", "value": "1"}, {"key": "pageSize", "value": "20"}]}}, "response": []}, {"name": "标记消息已读", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"messageIds\": [1, 2, 3]\n}"}, "url": {"raw": "{{baseUrl}}/messages/read", "host": ["{{baseUrl}}"], "path": ["messages", "read"]}}, "response": []}]}, {"name": "用户接口", "item": [{"name": "获取用户信息", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/user/profile", "host": ["{{baseUrl}}"], "path": ["user", "profile"]}}, "response": []}, {"name": "更新用户信息", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"新名称\",\n  \"email\": \"<EMAIL>\",\n  \"phone\": \"13800138001\"\n}"}, "url": {"raw": "{{baseUrl}}/user/profile", "host": ["{{baseUrl}}"], "path": ["user", "profile"]}}, "response": []}]}]}