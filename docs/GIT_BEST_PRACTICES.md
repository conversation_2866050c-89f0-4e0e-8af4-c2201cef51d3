# Git 最佳实践指南

## 📁 文件管理

### 应该提交的文件
- ✅ 源代码文件 (`lib/`, `test/`)
- ✅ 配置文件 (`pubspec.yaml`, `analysis_options.yaml`)
- ✅ 资源文件 (`assets/`)
- ✅ 平台配置文件 (`android/app/build.gradle`, `ios/Runner/Info.plist`)
- ✅ 文档文件 (`README.md`, `docs/`)
- ✅ Git 配置 (`.gitignore`)

### 不应该提交的文件
- ❌ 构建输出 (`build/`, `**/build/`)
- ❌ 依赖缓存 (`.dart_tool/`, `.pub-cache/`)
- ❌ IDE 文件 (`.vscode/`, `.idea/`, `*.iml`)
- ❌ 操作系统文件 (`.DS_Store`, `Thumbs.db`)
- ❌ 日志文件 (`*.log`)
- ❌ 临时文件 (`*.tmp`, `*~`)
- ❌ 编译产物 (`*.apk`, `*.ipa`, `*.app`)
- ❌ 密钥文件 (`*.keystore`, `*.jks`)
- ❌ 环境配置 (`.env`, 包含敏感信息的配置)

## 🔧 清理已跟踪的不必要文件

如果之前已经提交了不应该跟踪的文件，运行清理脚本：

```bash
# 给脚本执行权限
chmod +x scripts/git_cleanup.sh

# 运行清理脚本
./scripts/git_cleanup.sh

# 提交更改
git add .gitignore
git commit -m "Update .gitignore and remove tracked build files"
```

## 📝 提交规范

### 提交信息格式
```
<类型>(<范围>): <描述>

[可选的正文]

[可选的脚注]
```

### 类型说明
- `feat`: 新功能
- `fix`: 修复bug
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 重构代码
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

### 示例
```bash
git commit -m "feat(auth): 添加用户登录功能"
git commit -m "fix(ui): 修复设置页面语言显示问题"
git commit -m "docs: 更新README文档"
```

## 🚀 推送到Gitee

### 首次推送
```bash
# 添加远程仓库
git remote add origin https://gitee.com/your-username/your-repo.git

# 推送到主分支
git push -u origin main
```

### 日常推送
```bash
# 检查状态
git status

# 添加文件
git add .

# 提交
git commit -m "your commit message"

# 推送
git push
```

## 📊 仓库大小优化

### 检查仓库大小
```bash
# 查看仓库大小
du -sh .git

# 查看最大的文件
git rev-list --objects --all | git cat-file --batch-check='%(objecttype) %(objectname) %(objectsize) %(rest)' | sed -n 's/^blob //p' | sort -nk2 | tail -10
```

### 如果仓库过大
```bash
# 清理历史中的大文件（谨慎使用）
git filter-branch --tree-filter 'rm -rf build' HEAD
git push --force
```

## ⚠️ 注意事项

1. **敏感信息**: 永远不要提交包含密码、API密钥、证书等敏感信息的文件
2. **大文件**: 避免提交大于100MB的文件，考虑使用Git LFS
3. **构建文件**: 构建文件会频繁变化且体积大，不应该提交
4. **IDE配置**: 个人IDE配置不应该影响其他开发者
5. **平台差异**: 操作系统生成的文件会在不同平台间造成冲突

## 🔍 常用命令

```bash
# 查看忽略的文件
git status --ignored

# 强制添加被忽略的文件（不推荐）
git add -f <file>

# 移除已跟踪但现在被忽略的文件
git rm --cached <file>

# 查看提交历史
git log --oneline

# 查看文件变更
git diff
```
