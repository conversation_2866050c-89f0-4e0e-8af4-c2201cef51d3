# 企业混合App后端API接口规范文档

## 概述

本文档详细描述了企业混合App所需的后端API接口规范，包括接口地址、请求参数、响应格式等。所有接口均基于RESTful API设计原则，使用JSON格式进行数据交换。

## 基础信息

- **基础URL**: `http://localhost:8080/api` (可配置)
- **数据格式**: JSON
- **字符编码**: UTF-8
- **认证方式**: Bearer Token

## 通用响应格式

所有API接口均采用统一的响应格式：

```json
{
  "success": boolean,
  "message": string,
  "data": object | array | null,
  "error": string | null,
  "timestamp": number
}
```

### 响应字段说明

| 字段 | 类型 | 必填 | 说明 |
|------|------|------|------|
| success | boolean | 是 | 请求是否成功 |
| message | string | 是 | 响应消息 |
| data | object/array/null | 否 | 响应数据 |
| error | string/null | 否 | 错误信息 |
| timestamp | number | 否 | 响应时间戳 |

## 1. 认证相关接口

### 1.1 用户登录

**接口地址**: `POST /api/auth/login`

**请求参数**:
```json
{
  "username": "string",
  "password": "string"
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "登录成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "refresh_token_string",
    "expiresIn": 7200,
    "user": {
      "id": 1,
      "username": "admin",
      "name": "管理员",
      "avatar": "https://example.com/avatar.jpg",
      "department": "信息技术部",
      "email": "<EMAIL>",
      "phone": "13800138000"
    }
  }
}
```

### 1.2 刷新Token

**接口地址**: `POST /api/auth/refresh`

**请求参数**:
```json
{
  "refreshToken": "string"
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "Token刷新成功",
  "data": {
    "token": "new_access_token",
    "expiresIn": 7200
  }
}
```

### 1.3 用户登出

**接口地址**: `POST /api/auth/logout`

**请求头**: `Authorization: Bearer {token}`

**响应示例**:
```json
{
  "success": true,
  "message": "登出成功"
}
```

## 2. 系统相关接口

### 2.1 服务器连接测试

**接口地址**: `GET /api/system/ping`

**响应示例**:
```json
{
  "success": true,
  "message": "连接成功",
  "data": {
    "serverInfo": {
      "name": "企业办公移动系统",
      "version": "2.1.0",
      "logo": "https://example.com/logo.png",
      "description": "高效协作，智能办公",
      "company": "科技有限公司",
      "copyright": "© 2024 科技有限公司 版权所有"
    },
    "serverTime": 1703123456789
  }
}
```

### 2.2 获取服务器信息

**接口地址**: `GET /api/system/info`

**响应示例**:
```json
{
  "success": true,
  "message": "获取成功",
  "data": {
    "serverInfo": {
      "name": "企业办公移动系统",
      "version": "2.1.0",
      "logo": "https://example.com/logo.png",
      "description": "高效协作，智能办公",
      "company": "科技有限公司",
      "copyright": "© 2024 科技有限公司 版权所有",
      "features": ["移动办公", "协同工作", "数据安全"],
      "supportContact": "<EMAIL>"
    }
  }
}
```

## 3. 首页相关接口

### 3.1 获取首页信息

**接口地址**: `GET /api/home/<USER>

**请求头**: `Authorization: Bearer {token}`

**查询参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| forceRefresh | boolean | 否 | 是否强制刷新，默认false |

**响应示例**:
```json
{
  "success": true,
  "message": "获取成功",
  "data": {
    "banners": [
      {
        "id": 1,
        "title": "企业数字化转型",
        "image": "https://example.com/banner1.jpg",
        "url": "https://example.com/news/1",
        "sort": 1,
        "status": 1
      }
    ],
    "news": [
      {
        "id": 1,
        "title": "公司Q4季度总结会议通知",
        "summary": "定于本月底召开Q4季度总结会议，请各部门做好准备...",
        "publishTime": "2024-12-20 10:00:00",
        "category": "公司公告",
        "author": "人事部",
        "readCount": 156,
        "url": "https://example.com/news/detail/1"
      }
    ]
  }
}
```

## 4. 应用相关接口

### 4.1 获取应用列表

**接口地址**: `GET /api/apps/list`

**请求头**: `Authorization: Bearer {token}`

**查询参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| forceRefresh | boolean | 否 | 是否强制刷新，默认false |
| category | string | 否 | 应用分类筛选 |

**响应示例**:
```json
{
  "success": true,
  "message": "获取成功",
  "data": {
    "categories": [
      {
        "id": 1,
        "title": "办公协作",
        "sort": 1,
        "apps": [
          {
            "id": 1,
            "name": "日程管理",
            "icon": "calendar_today",
            "color": "0xFF3366CC",
            "url": "https://calendar.example.com",
            "description": "管理个人和团队日程安排",
            "category": "办公协作",
            "sort": 1,
            "status": 1,
            "isFavorite": false
          }
        ]
      }
    ]
  }
}
```

### 4.2 保存常用应用设置

**接口地址**: `POST /api/apps/favorites`

**请求头**: `Authorization: Bearer {token}`

**请求参数**:
```json
{
  "appIds": [1, 2, 3]
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "保存成功"
}
```

### 4.3 保存应用排序

**接口地址**: `POST /api/apps/sort`

**请求头**: `Authorization: Bearer {token}`

**请求参数**:
```json
{
  "apps": [
    {
      "id": 1,
      "sort": 1
    },
    {
      "id": 2,
      "sort": 2
    }
  ]
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "排序保存成功"
}
```

## 5. 工作台相关接口

### 5.1 获取工作台应用

**接口地址**: `GET /api/workspace/apps`

**请求头**: `Authorization: Bearer {token}`

**响应示例**:
```json
{
  "success": true,
  "message": "获取成功",
  "data": {
    "favoriteApps": [
      {
        "id": 1,
        "name": "日程管理",
        "icon": "calendar_today",
        "color": "0xFF3366CC",
        "url": "https://calendar.example.com",
        "sort": 1
      }
    ]
  }
}
```

## 6. 消息相关接口

### 6.1 获取消息列表

**接口地址**: `GET /api/messages/list`

**请求头**: `Authorization: Bearer {token}`

**查询参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| forceRefresh | boolean | 否 | 是否强制刷新，默认false |
| type | string | 否 | 消息类型：all/unread/system/work |
| page | number | 否 | 页码，默认1 |
| pageSize | number | 否 | 每页数量，默认20 |

**响应示例**:
```json
{
  "success": true,
  "message": "获取成功",
  "data": {
    "messages": [
      {
        "id": 1,
        "title": "系统维护通知",
        "content": "系统将于今晚22:00-24:00进行维护升级...",
        "time": "10:30",
        "icon": "notifications",
        "color": "0xFF3366CC",
        "unreadCount": 1,
        "type": "system",
        "isRead": false,
        "createTime": "2024-12-20 10:30:00",
        "sender": "系统管理员"
      }
    ],
    "pagination": {
      "page": 1,
      "pageSize": 20,
      "total": 50,
      "totalPages": 3
    }
  }
}
```

### 6.2 标记消息已读

**接口地址**: `POST /api/messages/read`

**请求头**: `Authorization: Bearer {token}`

**请求参数**:
```json
{
  "messageIds": [1, 2, 3]
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "标记成功"
}
```

## 7. 用户相关接口

### 7.1 获取用户信息

**接口地址**: `GET /api/user/profile`

**请求头**: `Authorization: Bearer {token}`

**响应示例**:
```json
{
  "success": true,
  "message": "获取成功",
  "data": {
    "id": 1,
    "username": "admin",
    "name": "管理员",
    "avatar": "https://example.com/avatar.jpg",
    "department": "信息技术部",
    "email": "<EMAIL>",
    "phone": "13800138000",
    "position": "系统管理员",
    "joinDate": "2023-01-01",
    "lastLoginTime": "2024-12-20 09:30:00"
  }
}
```

### 7.2 更新用户信息

**接口地址**: `PUT /api/user/profile`

**请求头**: `Authorization: Bearer {token}`

**请求参数**:
```json
{
  "name": "string",
  "email": "string",
  "phone": "string",
  "avatar": "string"
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "更新成功"
}
```

## 8. 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 400 | 请求参数错误 |
| 401 | 未授权或Token过期 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |
| 1001 | 用户名或密码错误 |
| 1002 | 用户已被禁用 |
| 1003 | Token无效 |
| 1004 | Token已过期 |
| 2001 | 应用不存在 |
| 2002 | 应用已被禁用 |
| 3001 | 消息不存在 |

## 9. 安全说明

1. **Token认证**: 除登录和系统信息接口外，所有接口都需要在请求头中携带有效的Bearer Token
2. **Token过期**: Token默认有效期为2小时，过期后需要使用refreshToken刷新
3. **HTTPS**: 生产环境建议使用HTTPS协议
4. **参数验证**: 所有输入参数都应进行严格的验证和过滤
5. **权限控制**: 根据用户角色控制接口访问权限

## 10. 开发说明

1. **环境配置**: 开发环境可使用HTTP，生产环境必须使用HTTPS
2. **跨域处理**: 需要配置CORS允许移动端访问
3. **日志记录**: 建议记录所有API请求日志用于调试和监控
4. **缓存策略**: 对于频繁访问的数据建议使用Redis等缓存
5. **数据库设计**: 建议使用MySQL或PostgreSQL作为主数据库

## 11. 测试说明

### 测试账号
- 用户名: `admin`
- 密码: `123456`

### 测试环境
- 服务器地址: `http://localhost:8080`
- 数据库: 使用测试数据库，包含完整的测试数据

### 接口测试工具
推荐使用Postman或类似工具进行接口测试，可导入本文档中的接口定义进行自动化测试。

## 12. 文件上传接口

### 12.1 上传头像

**接口地址**: `POST /api/upload/avatar`

**请求头**:
- `Authorization: Bearer {token}`
- `Content-Type: multipart/form-data`

**请求参数**:
- `file`: 图片文件 (支持jpg, png, gif格式，最大2MB)

**响应示例**:
```json
{
  "success": true,
  "message": "上传成功",
  "data": {
    "url": "https://example.com/uploads/avatars/user_1_20241220.jpg",
    "filename": "user_1_20241220.jpg",
    "size": 1024000
  }
}
```

### 12.2 上传文件

**接口地址**: `POST /api/upload/file`

**请求头**:
- `Authorization: Bearer {token}`
- `Content-Type: multipart/form-data`

**请求参数**:
- `file`: 文件 (最大10MB)
- `category`: 文件分类 (可选)

**响应示例**:
```json
{
  "success": true,
  "message": "上传成功",
  "data": {
    "url": "https://example.com/uploads/files/document_20241220.pdf",
    "filename": "document_20241220.pdf",
    "originalName": "重要文档.pdf",
    "size": 2048000,
    "mimeType": "application/pdf"
  }
}
```

## 13. 设置相关接口

### 13.1 获取用户设置

**接口地址**: `GET /api/settings/user`

**请求头**: `Authorization: Bearer {token}`

**响应示例**:
```json
{
  "success": true,
  "message": "获取成功",
  "data": {
    "theme": "light",
    "language": "zh",
    "notifications": {
      "system": true,
      "work": true,
      "email": false,
      "push": true
    },
    "privacy": {
      "showOnlineStatus": true,
      "allowSearch": true
    }
  }
}
```

### 13.2 更新用户设置

**接口地址**: `PUT /api/settings/user`

**请求头**: `Authorization: Bearer {token}`

**请求参数**:
```json
{
  "theme": "light|dark",
  "language": "zh|en",
  "notifications": {
    "system": boolean,
    "work": boolean,
    "email": boolean,
    "push": boolean
  },
  "privacy": {
    "showOnlineStatus": boolean,
    "allowSearch": boolean
  }
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "设置更新成功"
}
```

## 14. 统计相关接口

### 14.1 获取用户统计信息

**接口地址**: `GET /api/stats/user`

**请求头**: `Authorization: Bearer {token}`

**查询参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| period | string | 否 | 统计周期：day/week/month，默认week |

**响应示例**:
```json
{
  "success": true,
  "message": "获取成功",
  "data": {
    "loginCount": 15,
    "appUsageCount": 45,
    "messageCount": 8,
    "activeHours": 32.5,
    "mostUsedApps": [
      {
        "appId": 1,
        "appName": "日程管理",
        "usageCount": 12
      }
    ]
  }
}
```

### 14.2 获取应用使用统计

**接口地址**: `GET /api/stats/apps`

**请求头**: `Authorization: Bearer {token}`

**响应示例**:
```json
{
  "success": true,
  "message": "获取成功",
  "data": {
    "totalApps": 15,
    "activeApps": 8,
    "topApps": [
      {
        "id": 1,
        "name": "日程管理",
        "usageCount": 156,
        "userCount": 45
      }
    ]
  }
}
```

## 15. 通知相关接口

### 15.1 获取通知设置

**接口地址**: `GET /api/notifications/settings`

**请求头**: `Authorization: Bearer {token}`

**响应示例**:
```json
{
  "success": true,
  "message": "获取成功",
  "data": {
    "pushEnabled": true,
    "emailEnabled": false,
    "systemNotifications": true,
    "workNotifications": true,
    "quietHours": {
      "enabled": true,
      "startTime": "22:00",
      "endTime": "08:00"
    }
  }
}
```

### 15.2 更新通知设置

**接口地址**: `PUT /api/notifications/settings`

**请求头**: `Authorization: Bearer {token}`

**请求参数**:
```json
{
  "pushEnabled": boolean,
  "emailEnabled": boolean,
  "systemNotifications": boolean,
  "workNotifications": boolean,
  "quietHours": {
    "enabled": boolean,
    "startTime": "string",
    "endTime": "string"
  }
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "通知设置更新成功"
}
```

## 16. 日志相关接口

### 16.1 上报错误日志

**接口地址**: `POST /api/logs/error`

**请求头**: `Authorization: Bearer {token}`

**请求参数**:
```json
{
  "type": "string",
  "message": "string",
  "stackTrace": "string",
  "deviceInfo": {
    "platform": "string",
    "version": "string",
    "model": "string"
  },
  "appVersion": "string",
  "timestamp": number
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "日志上报成功",
  "data": {
    "logId": "log_20241220_001"
  }
}
```

### 16.2 获取用户操作日志

**接口地址**: `GET /api/logs/user`

**请求头**: `Authorization: Bearer {token}`

**查询参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| page | number | 否 | 页码，默认1 |
| pageSize | number | 否 | 每页数量，默认20 |
| startDate | string | 否 | 开始日期 YYYY-MM-DD |
| endDate | string | 否 | 结束日期 YYYY-MM-DD |

**响应示例**:
```json
{
  "success": true,
  "message": "获取成功",
  "data": {
    "logs": [
      {
        "id": 1,
        "action": "login",
        "description": "用户登录",
        "ip": "*************",
        "userAgent": "Mozilla/5.0...",
        "timestamp": "2024-12-20 09:30:00"
      }
    ],
    "pagination": {
      "page": 1,
      "pageSize": 20,
      "total": 100,
      "totalPages": 5
    }
  }
}
```

## 17. 搜索相关接口

### 17.1 全局搜索

**接口地址**: `GET /api/search`

**请求头**: `Authorization: Bearer {token}`

**查询参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| keyword | string | 是 | 搜索关键词 |
| type | string | 否 | 搜索类型：all/apps/news/messages |
| page | number | 否 | 页码，默认1 |
| pageSize | number | 否 | 每页数量，默认10 |

**响应示例**:
```json
{
  "success": true,
  "message": "搜索成功",
  "data": {
    "results": [
      {
        "type": "app",
        "id": 1,
        "title": "日程管理",
        "description": "管理个人和团队日程安排",
        "url": "https://calendar.example.com",
        "highlight": "日程<em>管理</em>"
      }
    ],
    "pagination": {
      "page": 1,
      "pageSize": 10,
      "total": 25,
      "totalPages": 3
    },
    "suggestions": ["日程", "管理", "任务"]
  }
}
```

## 18. 版本相关接口

### 18.1 检查应用更新

**接口地址**: `GET /api/version/check`

**查询参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| platform | string | 是 | 平台：android/ios |
| currentVersion | string | 是 | 当前版本号 |

**响应示例**:
```json
{
  "success": true,
  "message": "检查完成",
  "data": {
    "hasUpdate": true,
    "latestVersion": "2.1.0",
    "updateInfo": {
      "title": "版本更新",
      "description": "修复已知问题，优化用户体验",
      "features": [
        "新增消息推送功能",
        "优化界面显示效果",
        "修复登录异常问题"
      ],
      "downloadUrl": "https://example.com/app-v2.1.0.apk",
      "fileSize": 25600000,
      "isForceUpdate": false,
      "releaseDate": "2024-12-20"
    }
  }
}
```

## 19. 数据库设计建议

### 19.1 用户表 (users)
```sql
CREATE TABLE users (
  id INT PRIMARY KEY AUTO_INCREMENT,
  username VARCHAR(50) UNIQUE NOT NULL,
  password VARCHAR(255) NOT NULL,
  name VARCHAR(100) NOT NULL,
  email VARCHAR(100),
  phone VARCHAR(20),
  avatar VARCHAR(255),
  department VARCHAR(100),
  position VARCHAR(100),
  status TINYINT DEFAULT 1,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 19.2 应用表 (applications)
```sql
CREATE TABLE applications (
  id INT PRIMARY KEY AUTO_INCREMENT,
  name VARCHAR(100) NOT NULL,
  icon VARCHAR(100),
  color VARCHAR(20),
  url VARCHAR(255),
  description TEXT,
  category_id INT,
  sort INT DEFAULT 0,
  status TINYINT DEFAULT 1,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 19.3 消息表 (messages)
```sql
CREATE TABLE messages (
  id INT PRIMARY KEY AUTO_INCREMENT,
  title VARCHAR(200) NOT NULL,
  content TEXT,
  type VARCHAR(20) DEFAULT 'system',
  icon VARCHAR(50),
  color VARCHAR(20),
  sender_id INT,
  status TINYINT DEFAULT 1,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

## 20. 部署建议

### 20.1 服务器要求
- **操作系统**: Linux (推荐Ubuntu 20.04+)
- **内存**: 最低4GB，推荐8GB+
- **存储**: 最低50GB，推荐100GB+
- **网络**: 稳定的网络连接

### 20.2 技术栈建议
- **后端框架**: Spring Boot (Java) / Express.js (Node.js) / Django (Python)
- **数据库**: MySQL 8.0+ / PostgreSQL 12+
- **缓存**: Redis 6.0+
- **文件存储**: 本地存储 / 阿里云OSS / AWS S3
- **反向代理**: Nginx
- **容器化**: Docker + Docker Compose

### 20.3 安全配置
- 启用HTTPS (SSL/TLS证书)
- 配置防火墙规则
- 定期备份数据库
- 监控系统资源使用情况
- 配置日志轮转和清理策略
