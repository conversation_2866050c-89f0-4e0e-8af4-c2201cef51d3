<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工作台详情</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px 20px;
            text-align: center;
            position: relative;
        }

        .app-icon {
            width: 80px;
            height: 80px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            margin: 0 auto 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 36px;
            backdrop-filter: blur(10px);
        }

        .app-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 8px;
        }

        .app-category {
            font-size: 14px;
            opacity: 0.9;
            background: rgba(255, 255, 255, 0.2);
            padding: 4px 12px;
            border-radius: 12px;
            display: inline-block;
        }

        .content {
            padding: 20px;
        }

        .section {
            margin-bottom: 30px;
        }

        .section-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
            border-bottom: 2px solid #667eea;
            padding-bottom: 5px;
        }

        .description {
            font-size: 16px;
            line-height: 1.8;
            color: #666;
            margin-bottom: 20px;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .feature-card {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            border-left: 4px solid #667eea;
            transition: transform 0.2s ease;
        }

        .feature-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .feature-icon {
            font-size: 24px;
            margin-bottom: 10px;
        }

        .feature-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 8px;
            color: #333;
        }

        .feature-desc {
            font-size: 14px;
            color: #666;
            line-height: 1.5;
        }

        .stats-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .stat-item {
            text-align: center;
            background: #f8f9fa;
            padding: 20px;
            border-radius: 12px;
        }

        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 12px;
            color: #666;
            text-transform: uppercase;
        }

        .action-buttons {
            display: flex;
            gap: 15px;
            margin-top: 30px;
            flex-wrap: wrap;
        }

        .btn {
            flex: 1;
            min-width: 120px;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            text-align: center;
            display: inline-block;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }

        .btn-secondary {
            background: #f8f9fa;
            color: #333;
            border: 2px solid #e9ecef;
        }

        .btn-secondary:hover {
            background: #e9ecef;
            border-color: #667eea;
        }

        .workspace-tools {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            margin-top: 20px;
        }

        .tools-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
        }

        .tool-item {
            background: white;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.2s ease;
            border: 2px solid transparent;
        }

        .tool-item:hover {
            border-color: #667eea;
            transform: translateY(-2px);
        }

        .tool-icon {
            font-size: 32px;
            margin-bottom: 10px;
        }

        .tool-name {
            font-size: 14px;
            font-weight: bold;
            color: #333;
        }

        @media (max-width: 768px) {
            .container {
                margin: 0;
                box-shadow: none;
            }
            
            .header {
                padding: 20px 15px;
            }
            
            .content {
                padding: 15px;
            }
            
            .features-grid {
                grid-template-columns: 1fr;
            }
            
            .action-buttons {
                flex-direction: column;
            }
            
            .btn {
                flex: none;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="app-icon" id="appIcon">📊</div>
            <div class="app-title" id="appTitle">工作台应用</div>
            <div class="app-category" id="appCategory">办公协作</div>
        </div>

        <div class="content">
            <div class="section">
                <div class="section-title">应用简介</div>
                <div class="description" id="appDescription">
                    专业的工作台管理工具，为您提供高效的办公环境和协作平台。
                </div>
            </div>

            <div class="section">
                <div class="section-title">核心功能</div>
                <div class="features-grid" id="featuresGrid">
                    <!-- 功能特性将通过JavaScript动态加载 -->
                </div>
            </div>

            <div class="section">
                <div class="section-title">使用统计</div>
                <div class="stats-container" id="statsContainer">
                    <!-- 统计数据将通过JavaScript动态加载 -->
                </div>
            </div>

            <div class="section">
                <div class="section-title">工作台工具</div>
                <div class="workspace-tools">
                    <div class="tools-grid" id="toolsGrid">
                        <!-- 工具列表将通过JavaScript动态加载 -->
                    </div>
                </div>
            </div>

            <div class="action-buttons">
                <button class="btn btn-primary" onclick="openApp()">立即使用</button>
                <button class="btn btn-secondary" onclick="addToFavorites()">添加收藏</button>
                <button class="btn btn-secondary" onclick="shareApp()">分享应用</button>
            </div>
        </div>
    </div>

    <script>
        // 工作台应用数据
        const workspaceData = {
            '1': {
                name: '日程管理',
                icon: '📅',
                category: '时间管理',
                description: '专业的日程管理工具，帮助您高效安排工作和生活。支持多种视图模式，智能提醒功能，让您的时间管理更加轻松便捷。',
                features: [
                    { icon: '📊', title: '多视图模式', desc: '支持日、周、月视图，灵活查看日程安排' },
                    { icon: '⏰', title: '智能提醒', desc: '自定义提醒时间，重要事项不再遗漏' },
                    { icon: '👥', title: '团队协作', desc: '共享日程，团队协作更高效' },
                    { icon: '📱', title: '多端同步', desc: '手机、电脑实时同步，随时随地管理日程' }
                ],
                stats: { users: '1,234', satisfaction: '98%', lastUpdate: '2024.12' },
                tools: [
                    { icon: '📝', name: '快速记录' },
                    { icon: '🔔', name: '提醒设置' },
                    { icon: '📊', name: '统计报表' },
                    { icon: '🔄', name: '同步设置' }
                ]
            },
            '2': {
                name: '任务协作',
                icon: '✅',
                category: '项目管理',
                description: '强大的任务协作平台，支持团队项目管理、任务分配、进度跟踪等功能，提升团队协作效率。',
                features: [
                    { icon: '📋', title: '任务管理', desc: '创建、分配、跟踪任务进度' },
                    { icon: '👥', title: '团队协作', desc: '多人协作，实时沟通交流' },
                    { icon: '📈', title: '进度跟踪', desc: '可视化项目进度，掌控全局' },
                    { icon: '📊', title: '数据分析', desc: '详细的数据报表和分析' }
                ],
                stats: { users: '2,156', satisfaction: '96%', lastUpdate: '2024.12' },
                tools: [
                    { icon: '➕', name: '新建任务' },
                    { icon: '👥', name: '团队管理' },
                    { icon: '📊', name: '进度报表' },
                    { icon: '💬', name: '团队沟通' }
                ]
            },
            '3': {
                name: '文档中心',
                icon: '📄',
                category: '知识管理',
                description: '企业文档管理中心，支持文档创建、编辑、分享、版本控制等功能，构建企业知识库。',
                features: [
                    { icon: '📝', title: '在线编辑', desc: '支持多种格式文档在线编辑' },
                    { icon: '🔄', title: '版本控制', desc: '完整的版本历史记录和回滚' },
                    { icon: '🔗', title: '文档分享', desc: '灵活的权限控制和分享机制' },
                    { icon: '🔍', title: '全文搜索', desc: '强大的搜索功能，快速定位内容' }
                ],
                stats: { users: '3,421', satisfaction: '94%', lastUpdate: '2024.12' },
                tools: [
                    { icon: '📝', name: '新建文档' },
                    { icon: '📁', name: '文件管理' },
                    { icon: '🔍', name: '搜索文档' },
                    { icon: '📤', name: '文档分享' }
                ]
            }
        };

        // 初始化页面
        function initializeWithParams(params) {
            const appId = params.id || '1';
            const app = workspaceData[appId];
            
            if (app) {
                updatePageContent(app);
            }
        }

        function updatePageContent(app) {
            // 更新基本信息
            document.getElementById('appIcon').textContent = app.icon;
            document.getElementById('appTitle').textContent = app.name;
            document.getElementById('appCategory').textContent = app.category;
            document.getElementById('appDescription').textContent = app.description;

            // 更新功能特性
            const featuresGrid = document.getElementById('featuresGrid');
            featuresGrid.innerHTML = app.features.map(feature => `
                <div class="feature-card">
                    <div class="feature-icon">${feature.icon}</div>
                    <div class="feature-title">${feature.title}</div>
                    <div class="feature-desc">${feature.desc}</div>
                </div>
            `).join('');

            // 更新统计数据
            const statsContainer = document.getElementById('statsContainer');
            statsContainer.innerHTML = `
                <div class="stat-item">
                    <div class="stat-number">${app.stats.users}</div>
                    <div class="stat-label">活跃用户</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">${app.stats.satisfaction}</div>
                    <div class="stat-label">满意度</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">${app.stats.lastUpdate}</div>
                    <div class="stat-label">最后更新</div>
                </div>
            `;

            // 更新工具列表
            const toolsGrid = document.getElementById('toolsGrid');
            toolsGrid.innerHTML = app.tools.map(tool => `
                <div class="tool-item" onclick="openTool('${tool.name}')">
                    <div class="tool-icon">${tool.icon}</div>
                    <div class="tool-name">${tool.name}</div>
                </div>
            `).join('');
        }

        // 按钮事件处理
        function openApp() {
            alert('正在打开应用...');
        }

        function addToFavorites() {
            alert('已添加到收藏夹');
        }

        function shareApp() {
            alert('分享功能开发中...');
        }

        function openTool(toolName) {
            alert(`正在打开工具: ${toolName}`);
        }

        // 如果没有URL参数，使用默认数据
        if (!window.urlParams) {
            updatePageContent(workspaceData['1']);
        }
    </script>
</body>
</html>
