<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>消息详情</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .message-icon {
            width: 60px;
            height: 60px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            margin: 0 auto 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            backdrop-filter: blur(10px);
        }

        .message-title {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 8px;
        }

        .message-meta {
            font-size: 14px;
            opacity: 0.9;
            display: flex;
            justify-content: center;
            gap: 20px;
            flex-wrap: wrap;
        }

        .meta-item {
            background: rgba(255, 255, 255, 0.2);
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
        }

        .content {
            padding: 20px;
        }

        .message-content {
            font-size: 16px;
            line-height: 1.8;
            color: #333;
            margin-bottom: 30px;
        }

        .message-content h3 {
            color: #667eea;
            margin: 20px 0 10px 0;
            font-size: 18px;
        }

        .message-content p {
            margin-bottom: 15px;
        }

        .message-content ul {
            margin: 15px 0;
            padding-left: 20px;
        }

        .message-content li {
            margin-bottom: 8px;
        }

        .priority-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
            margin-bottom: 15px;
        }

        .priority-high {
            background: #ff4757;
            color: white;
        }

        .priority-medium {
            background: #ffa502;
            color: white;
        }

        .priority-low {
            background: #2ed573;
            color: white;
        }

        .attachments {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
        }

        .attachments-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }

        .attachment-item {
            display: flex;
            align-items: center;
            padding: 10px;
            background: white;
            border-radius: 8px;
            margin-bottom: 10px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .attachment-item:hover {
            background: #e9ecef;
        }

        .attachment-icon {
            width: 40px;
            height: 40px;
            background: #667eea;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            margin-right: 15px;
            font-size: 16px;
        }

        .attachment-info {
            flex: 1;
        }

        .attachment-name {
            font-weight: bold;
            margin-bottom: 2px;
        }

        .attachment-size {
            font-size: 12px;
            color: #666;
        }

        .action-buttons {
            display: flex;
            gap: 15px;
            margin-top: 30px;
            flex-wrap: wrap;
        }

        .btn {
            flex: 1;
            min-width: 120px;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            text-align: center;
            display: inline-block;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }

        .btn-secondary {
            background: #f8f9fa;
            color: #333;
            border: 2px solid #e9ecef;
        }

        .btn-secondary:hover {
            background: #e9ecef;
            border-color: #667eea;
        }

        .related-messages {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            margin-top: 20px;
        }

        .related-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }

        .related-item {
            display: flex;
            align-items: center;
            padding: 10px;
            background: white;
            border-radius: 8px;
            margin-bottom: 10px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .related-item:hover {
            background: #e9ecef;
        }

        .related-icon {
            width: 30px;
            height: 30px;
            background: #667eea;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            margin-right: 15px;
            font-size: 14px;
        }

        .related-info {
            flex: 1;
        }

        .related-title-text {
            font-weight: bold;
            margin-bottom: 2px;
            font-size: 14px;
        }

        .related-time {
            font-size: 12px;
            color: #666;
        }

        @media (max-width: 768px) {
            .container {
                margin: 0;
                box-shadow: none;
            }
            
            .header {
                padding: 15px;
            }
            
            .content {
                padding: 15px;
            }
            
            .message-meta {
                flex-direction: column;
                gap: 10px;
            }
            
            .action-buttons {
                flex-direction: column;
            }
            
            .btn {
                flex: none;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="message-icon" id="messageIcon">📧</div>
            <div class="message-title" id="messageTitle">消息标题</div>
            <div class="message-meta">
                <div class="meta-item" id="messageSender">发送人: 系统</div>
                <div class="meta-item" id="messageTime">时间: 2024-12-28</div>
                <div class="meta-item" id="messageType">类型: 系统通知</div>
            </div>
        </div>

        <div class="content">
            <div class="priority-badge" id="priorityBadge">高优先级</div>
            
            <div class="message-content" id="messageContent">
                <!-- 消息内容将通过JavaScript动态加载 -->
            </div>

            <div class="attachments" id="attachmentsSection" style="display: none;">
                <div class="attachments-title">附件</div>
                <div id="attachmentsList">
                    <!-- 附件列表将通过JavaScript动态加载 -->
                </div>
            </div>

            <div class="action-buttons">
                <button class="btn btn-primary" onclick="markAsRead()">标记已读</button>
                <button class="btn btn-secondary" onclick="replyMessage()">回复</button>
                <button class="btn btn-secondary" onclick="forwardMessage()">转发</button>
            </div>

            <div class="related-messages">
                <div class="related-title">相关消息</div>
                <div id="relatedMessagesList">
                    <!-- 相关消息将通过JavaScript动态加载 -->
                </div>
            </div>
        </div>
    </div>

    <script>
        // 消息数据
        const messageData = {
            '1': {
                title: '系统维护通知',
                icon: '🔧',
                sender: '系统管理员',
                time: '2024-12-28 10:00',
                type: '系统通知',
                priority: 'high',
                content: `
                    <h3>系统维护通知</h3>
                    <p>尊敬的用户，</p>
                    <p>为了提供更好的服务体验，我们将于 <strong>2024年12月30日 02:00-06:00</strong> 进行系统维护升级。</p>
                    
                    <h3>维护内容</h3>
                    <ul>
                        <li>系统性能优化</li>
                        <li>安全补丁更新</li>
                        <li>新功能部署</li>
                        <li>数据库优化</li>
                    </ul>
                    
                    <h3>影响范围</h3>
                    <p>维护期间，系统将暂时无法访问，请您提前做好相关准备工作。</p>
                    
                    <p>如有紧急情况，请联系技术支持：400-123-4567</p>
                    
                    <p>感谢您的理解与支持！</p>
                `,
                attachments: [
                    { name: '维护计划详情.pdf', size: '2.3 MB', icon: '📄' },
                    { name: '系统升级说明.docx', size: '1.8 MB', icon: '📝' }
                ],
                related: [
                    { title: '上次维护总结', time: '2024-12-15', icon: '📋' },
                    { title: '系统使用指南', time: '2024-12-20', icon: '📖' }
                ]
            },
            '2': {
                title: '新版本发布',
                icon: '🚀',
                sender: '产品团队',
                time: '2024-12-27 14:30',
                type: '产品更新',
                priority: 'medium',
                content: `
                    <h3>新版本 v2.1.0 正式发布</h3>
                    <p>我们很高兴地宣布，企业移动办公系统 v2.1.0 版本正式发布！</p>
                    
                    <h3>新增功能</h3>
                    <ul>
                        <li>全新的工作台界面设计</li>
                        <li>增强的消息推送功能</li>
                        <li>支持多语言切换</li>
                        <li>优化的文件管理系统</li>
                    </ul>
                    
                    <h3>性能优化</h3>
                    <ul>
                        <li>应用启动速度提升 30%</li>
                        <li>内存使用优化</li>
                        <li>网络请求性能改进</li>
                    </ul>
                    
                    <p>更新将在未来几天内自动推送，请注意查收。</p>
                `,
                attachments: [
                    { name: '更新日志.pdf', size: '1.5 MB', icon: '📄' },
                    { name: '功能演示视频.mp4', size: '15.2 MB', icon: '🎥' }
                ],
                related: [
                    { title: 'v2.0.0 发布说明', time: '2024-11-15', icon: '📋' },
                    { title: '功能使用教程', time: '2024-12-25', icon: '📖' }
                ]
            },
            '3': {
                title: '会议提醒',
                icon: '📅',
                sender: '张经理',
                time: '2024-12-28 09:15',
                type: '会议通知',
                priority: 'high',
                content: `
                    <h3>月度工作总结会议</h3>
                    <p><strong>会议时间：</strong>2024年12月30日 14:00-16:00</p>
                    <p><strong>会议地点：</strong>会议室A（3楼）</p>
                    <p><strong>会议主题：</strong>12月工作总结及1月工作计划</p>
                    
                    <h3>会议议程</h3>
                    <ul>
                        <li>14:00-14:30 各部门工作总结汇报</li>
                        <li>14:30-15:00 项目进展讨论</li>
                        <li>15:00-15:30 下月工作计划制定</li>
                        <li>15:30-16:00 其他事项讨论</li>
                    </ul>
                    
                    <h3>参会人员</h3>
                    <p>全体项目组成员，请准时参加。如有特殊情况无法参会，请提前请假。</p>
                    
                    <p>请各位同事提前准备相关材料。</p>
                `,
                attachments: [
                    { name: '会议议程.docx', size: '0.8 MB', icon: '📝' },
                    { name: '工作总结模板.xlsx', size: '1.2 MB', icon: '📊' }
                ],
                related: [
                    { title: '上次会议纪要', time: '2024-11-30', icon: '📋' },
                    { title: '会议室预订确认', time: '2024-12-27', icon: '🏢' }
                ]
            }
        };

        // 初始化页面
        function initializeWithParams(params) {
            const messageId = params.id || '1';
            const message = messageData[messageId];
            
            if (message) {
                updatePageContent(message);
            }
        }

        function updatePageContent(message) {
            // 更新基本信息
            document.getElementById('messageIcon').textContent = message.icon;
            document.getElementById('messageTitle').textContent = message.title;
            document.getElementById('messageSender').textContent = `发送人: ${message.sender}`;
            document.getElementById('messageTime').textContent = `时间: ${message.time}`;
            document.getElementById('messageType').textContent = `类型: ${message.type}`;
            
            // 更新优先级标签
            const priorityBadge = document.getElementById('priorityBadge');
            priorityBadge.className = `priority-badge priority-${message.priority}`;
            priorityBadge.textContent = message.priority === 'high' ? '高优先级' : 
                                       message.priority === 'medium' ? '中优先级' : '低优先级';
            
            // 更新消息内容
            document.getElementById('messageContent').innerHTML = message.content;
            
            // 更新附件
            if (message.attachments && message.attachments.length > 0) {
                document.getElementById('attachmentsSection').style.display = 'block';
                const attachmentsList = document.getElementById('attachmentsList');
                attachmentsList.innerHTML = message.attachments.map(attachment => `
                    <div class="attachment-item" onclick="downloadAttachment('${attachment.name}')">
                        <div class="attachment-icon">${attachment.icon}</div>
                        <div class="attachment-info">
                            <div class="attachment-name">${attachment.name}</div>
                            <div class="attachment-size">${attachment.size}</div>
                        </div>
                    </div>
                `).join('');
            }
            
            // 更新相关消息
            const relatedMessagesList = document.getElementById('relatedMessagesList');
            relatedMessagesList.innerHTML = message.related.map(related => `
                <div class="related-item" onclick="openRelatedMessage('${related.title}')">
                    <div class="related-icon">${related.icon}</div>
                    <div class="related-info">
                        <div class="related-title-text">${related.title}</div>
                        <div class="related-time">${related.time}</div>
                    </div>
                </div>
            `).join('');
        }

        // 按钮事件处理
        function markAsRead() {
            alert('消息已标记为已读');
        }

        function replyMessage() {
            alert('回复功能开发中...');
        }

        function forwardMessage() {
            alert('转发功能开发中...');
        }

        function downloadAttachment(fileName) {
            alert(`正在下载附件: ${fileName}`);
        }

        function openRelatedMessage(title) {
            alert(`正在打开相关消息: ${title}`);
        }

        // 如果没有URL参数，使用默认数据
        if (!window.urlParams) {
            updatePageContent(messageData['1']);
        }
    </script>
</body>
</html>
