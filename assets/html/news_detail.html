<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新闻详情</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .header h1 {
            font-size: 24px;
            margin-bottom: 10px;
        }

        .meta {
            font-size: 14px;
            opacity: 0.9;
        }

        .content {
            padding: 20px;
        }

        .banner-image {
            width: 100%;
            height: 200px;
            object-fit: cover;
            border-radius: 8px;
            margin-bottom: 20px;
            transition: opacity 0.3s ease;
        }

        /* 加载动画 */
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 内容淡入动画 */
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .article-content {
            font-size: 16px;
            line-height: 1.8;
            margin-bottom: 20px;
        }

        .article-content p {
            margin-bottom: 15px;
        }

        .article-content h2 {
            color: #667eea;
            margin: 25px 0 15px 0;
            font-size: 20px;
        }

        .article-content h3 {
            color: #555;
            margin: 20px 0 10px 0;
            font-size: 18px;
        }

        .highlight {
            background-color: #e8f2ff;
            padding: 15px;
            border-left: 4px solid #667eea;
            margin: 20px 0;
            border-radius: 4px;
        }

        .tags {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
        }

        .tag {
            display: inline-block;
            background: #667eea;
            color: white;
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 12px;
            margin-right: 8px;
            margin-bottom: 8px;
        }

        .footer {
            background: #f8f9fa;
            padding: 20px;
            text-align: center;
            color: #666;
            font-size: 14px;
            border-top: 1px solid #eee;
        }

        .back-button {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.5);
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
            z-index: 1000;
        }

        .back-button:hover {
            background: rgba(0, 0, 0, 0.7);
        }

        @media (max-width: 768px) {
            .container {
                margin: 0;
            }
            
            .header {
                padding: 15px;
            }
            
            .header h1 {
                font-size: 20px;
            }
            
            .content {
                padding: 15px;
            }
        }
    </style>
</head>
<body>
    <button class="back-button" onclick="history.back()">← 返回</button>
    
    <div class="container">
        <div class="header">
            <h1 id="article-title">加载中...</h1>
            <div class="meta">
                <span id="publish-time">2024-12-20 10:00:00</span>
                <span style="margin: 0 10px;">|</span>
                <span id="category">企业新闻</span>
            </div>
        </div>

        <div class="content">
            <img id="banner-image" class="banner-image" src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjBmMGYwIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxOCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuWKoOi9veS4rS4uLjwvdGV4dD48L3N2Zz4=" alt="新闻配图" loading="lazy">

            <div class="article-content" id="article-content">
                <p>正在加载内容...</p>
            </div>

            <div class="tags" id="tags-container">
                <span class="tag">企业动态</span>
                <span class="tag">重要通知</span>
                <span class="tag">最新资讯</span>
            </div>
        </div>

        <div class="footer">
            <p>© 2024 企业移动办公系统 | 内部资讯平台</p>
        </div>
    </div>

    <script>
        // 获取URL参数
        function getUrlParameter(name) {
            // 优先使用注入的参数
            if (window.urlParams && window.urlParams[name]) {
                return window.urlParams[name];
            }
            // 回退到URL参数
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get(name);
        }

        // 初始化函数，支持参数注入
        function initializeWithParams(params) {
            if (params && (params.id || params.type)) {
                loadContentData(params.id, params.type);
            }
        }

        // 模拟新闻数据
        const newsData = {
            '1': {
                title: '公司Q4季度总结会议通知',
                content: `
                    <h2>会议概述</h2>
                    <p>为了总结本年度第四季度的工作成果，分析当前市场形势，制定下一年度的发展战略，公司决定召开Q4季度总结会议。</p>
                    
                    <h3>会议时间</h3>
                    <p>2024年12月28日（周六）上午9:00-12:00</p>
                    
                    <h3>会议地点</h3>
                    <p>公司总部大会议室（A座15楼）</p>
                    
                    <div class="highlight">
                        <strong>重要提醒：</strong>请各部门负责人务必准时参加，并提前准备好部门工作总结报告。
                    </div>
                    
                    <h3>会议议程</h3>
                    <p>1. 各部门Q4工作总结汇报（9:00-10:30）</p>
                    <p>2. 财务部门年度财务报告（10:30-11:00）</p>
                    <p>3. 2025年度发展规划讨论（11:00-11:45）</p>
                    <p>4. 总经理总结发言（11:45-12:00）</p>
                    
                    <h3>参会人员</h3>
                    <p>• 公司高级管理层</p>
                    <p>• 各部门负责人</p>
                    <p>• 项目经理及以上级别员工</p>
                    
                    <p>请各位参会人员做好充分准备，确保会议顺利进行。如有特殊情况无法参加，请提前向人事部门请假。</p>
                `,
                image: 'https://picsum.photos/800/200?random=1',
                category: '公司公告',
                publishTime: '2024-12-20 10:00:00',
                tags: ['重要通知', '季度总结', '会议安排']
            },
            '2': {
                title: '新员工入职培训安排',
                content: `
                    <h2>培训目标</h2>
                    <p>为帮助新员工快速融入团队，了解公司文化和业务流程，特安排为期一周的入职培训。</p>
                    
                    <h3>培训时间</h3>
                    <p>2024年12月23日-27日，每日上午9:00-12:00，下午14:00-17:00</p>
                    
                    <h3>培训内容</h3>
                    <p><strong>第一天：公司介绍与企业文化</strong></p>
                    <p>• 公司发展历程与愿景使命</p>
                    <p>• 组织架构与部门职能</p>
                    <p>• 企业文化与价值观</p>
                    
                    <p><strong>第二天：规章制度与流程</strong></p>
                    <p>• 员工手册学习</p>
                    <p>• 考勤制度与请假流程</p>
                    <p>• 办公系统使用培训</p>
                    
                    <div class="highlight">
                        <strong>注意事项：</strong>培训期间请准时参加，如有疑问及时向HR部门咨询。
                    </div>
                    
                    <p><strong>第三天：业务知识培训</strong></p>
                    <p>• 公司主营业务介绍</p>
                    <p>• 产品知识学习</p>
                    <p>• 客户服务标准</p>
                    
                    <p><strong>第四天：技能培训</strong></p>
                    <p>• 办公软件使用</p>
                    <p>• 沟通技巧培训</p>
                    <p>• 团队协作方法</p>
                    
                    <p><strong>第五天：实践与考核</strong></p>
                    <p>• 模拟工作场景</p>
                    <p>• 培训效果测试</p>
                    <p>• 反馈与总结</p>
                `,
                image: 'https://picsum.photos/800/200?random=2',
                category: '人事通知',
                publishTime: '2024-12-19 14:30:00',
                tags: ['新员工', '培训', '入职指导']
            },
            '3': {
                title: '系统维护通知',
                content: `
                    <h2>维护说明</h2>
                    <p>为提升系统性能，优化用户体验，我们将于本周末进行系统维护升级。</p>
                    
                    <h3>维护时间</h3>
                    <p>2024年12月21日（周六）22:00 - 12月22日（周日）06:00</p>
                    
                    <div class="highlight">
                        <strong>重要提醒：</strong>维护期间系统将暂停服务，请提前做好工作安排。
                    </div>
                    
                    <h3>维护内容</h3>
                    <p>1. <strong>服务器硬件升级</strong></p>
                    <p>   • 增加服务器内存容量</p>
                    <p>   • 优化数据库性能</p>
                    <p>   • 更新安全补丁</p>
                    
                    <p>2. <strong>系统功能优化</strong></p>
                    <p>   • 提升页面加载速度</p>
                    <p>   • 优化移动端体验</p>
                    <p>   • 修复已知问题</p>
                    
                    <p>3. <strong>新功能上线</strong></p>
                    <p>   • 增加消息推送功能</p>
                    <p>   • 优化文件上传体验</p>
                    <p>   • 新增数据导出功能</p>
                    
                    <h3>注意事项</h3>
                    <p>• 请在维护开始前保存好所有工作内容</p>
                    <p>• 维护期间无法访问系统，请合理安排工作时间</p>
                    <p>• 如遇紧急情况，请联系技术支持热线：400-123-4567</p>
                    
                    <p>感谢大家的理解与配合！</p>
                `,
                image: 'https://picsum.photos/800/200?random=3',
                category: '技术公告',
                publishTime: '2024-12-18 16:00:00',
                tags: ['系统维护', '升级', '技术通知']
            }
        };

        // 轮播图数据
        const bannerData = {
            '1': {
                title: '企业数字化转型',
                content: `
                    <h2>数字化转型的重要性</h2>
                    <p>在当今快速发展的数字时代，企业数字化转型已成为保持竞争优势的关键因素。</p>
                    
                    <h3>转型目标</h3>
                    <p>我们的数字化转型旨在：</p>
                    <p>• 提高运营效率</p>
                    <p>• 改善客户体验</p>
                    <p>• 创新业务模式</p>
                    <p>• 增强数据驱动决策能力</p>
                    
                    <div class="highlight">
                        <strong>核心理念：</strong>以客户为中心，以数据为驱动，以技术为支撑。
                    </div>
                    
                    <h3>实施计划</h3>
                    <p>第一阶段：基础设施建设（2024年Q1-Q2）</p>
                    <p>第二阶段：业务流程数字化（2024年Q3-Q4）</p>
                    <p>第三阶段：数据分析与智能化（2025年Q1-Q2）</p>
                    
                    <p>我们相信，通过全员的共同努力，一定能够成功实现数字化转型目标。</p>
                `,
                image: 'https://picsum.photos/800/200?random=4',
                category: '企业战略',
                publishTime: '2024-12-15 09:00:00',
                tags: ['数字化', '转型', '战略规划']
            },
            '2': {
                title: '新产品发布会',
                content: `
                    <h2>产品发布会盛大举行</h2>
                    <p>我们很高兴地宣布，公司最新产品发布会将于下月隆重举行。</p>
                    
                    <h3>产品亮点</h3>
                    <p>本次发布的新产品具有以下特色：</p>
                    <p>• 创新的用户界面设计</p>
                    <p>• 强大的数据处理能力</p>
                    <p>• 完善的安全保障机制</p>
                    <p>• 灵活的定制化选项</p>
                    
                    <div class="highlight">
                        <strong>发布会时间：</strong>2025年1月15日 14:00-17:00
                    </div>
                    
                    <h3>参会嘉宾</h3>
                    <p>• 行业专家学者</p>
                    <p>• 合作伙伴代表</p>
                    <p>• 媒体记者朋友</p>
                    <p>• 公司全体员工</p>
                    
                    <p>期待与大家共同见证这一重要时刻！</p>
                `,
                image: 'https://picsum.photos/800/200?random=5',
                category: '产品发布',
                publishTime: '2024-12-16 11:00:00',
                tags: ['新产品', '发布会', '创新']
            },
            '3': {
                title: '团队建设活动',
                content: `
                    <h2>年度团队建设活动</h2>
                    <p>为增强团队凝聚力，促进员工之间的交流与合作，公司组织年度团队建设活动。</p>
                    
                    <h3>活动安排</h3>
                    <p><strong>时间：</strong>2024年12月30日-31日</p>
                    <p><strong>地点：</strong>某某度假村</p>
                    <p><strong>参与人员：</strong>全体员工</p>
                    
                    <h3>活动内容</h3>
                    <p>第一天：</p>
                    <p>• 团队拓展训练</p>
                    <p>• 户外运动比赛</p>
                    <p>• 篝火晚会</p>
                    
                    <div class="highlight">
                        <strong>活动目标：</strong>增进友谊，提升团队协作能力，放松身心。
                    </div>
                    
                    <p>第二天：</p>
                    <p>• 年度总结分享</p>
                    <p>• 优秀员工表彰</p>
                    <p>• 新年展望讨论</p>
                    
                    <h3>报名方式</h3>
                    <p>请于12月25日前向人事部门报名，名额有限，先到先得。</p>
                    
                    <p>让我们一起度过一个难忘的团建时光！</p>
                `,
                image: 'https://picsum.photos/800/200?random=6',
                category: '员工活动',
                publishTime: '2024-12-17 15:30:00',
                tags: ['团建', '活动', '员工福利']
            },
            '4': {
                title: '年度优秀员工评选结果公布',
                content: `
                    <h2>2024年度优秀员工评选结果</h2>
                    <p>经过严格的评选程序，2024年度优秀员工评选结果正式公布。</p>

                    <h3>评选标准</h3>
                    <p>• 工作业绩突出</p>
                    <p>• 团队协作精神</p>
                    <p>• 创新能力强</p>
                    <p>• 职业道德高尚</p>

                    <div class="highlight">
                        <strong>表彰大会：</strong>将于12月30日举行颁奖典礼
                    </div>

                    <p>恭喜所有获奖员工，感谢大家的辛勤付出！</p>
                `,
                image: 'https://picsum.photos/800/200?random=7',
                category: '人事通知',
                publishTime: '2024-12-17 09:00:00',
                tags: ['优秀员工', '表彰', '年度评选']
            },
            '5': {
                title: '办公设备升级通知',
                content: `
                    <h2>办公设备升级计划</h2>
                    <p>为提高工作效率，公司决定对部分办公设备进行升级。</p>

                    <h3>升级内容</h3>
                    <p>• 更换高性能电脑</p>
                    <p>• 升级网络设备</p>
                    <p>• 安装新版办公软件</p>

                    <p>升级期间可能会影响正常办公，请大家提前做好准备。</p>
                `,
                image: 'https://picsum.photos/800/200?random=8',
                category: '行政通知',
                publishTime: '2024-12-16 15:20:00',
                tags: ['设备升级', '办公效率', '通知']
            },
            '6': {
                title: '春节放假安排通知',
                content: `
                    <h2>2025年春节放假安排</h2>
                    <p>根据国家法定节假日安排，现将春节放假时间通知如下：</p>

                    <h3>放假时间</h3>
                    <p>2025年2月10日至2月17日，共8天</p>

                    <div class="highlight">
                        <strong>注意事项：</strong>放假期间请注意安全，保持通讯畅通
                    </div>

                    <p>祝大家春节快乐，阖家幸福！</p>
                `,
                image: 'https://picsum.photos/800/200?random=9',
                category: '公司公告',
                publishTime: '2024-12-15 11:30:00',
                tags: ['春节', '放假安排', '节日祝福']
            },
            '7': {
                title: '安全生产培训会议',
                content: `
                    <h2>安全生产培训重要性</h2>
                    <p>为加强安全生产意识，提高安全防范能力，特举办安全培训会议。</p>

                    <h3>培训内容</h3>
                    <p>• 安全生产法律法规</p>
                    <p>• 应急处理程序</p>
                    <p>• 安全防护措施</p>

                    <p>安全无小事，请大家务必重视！</p>
                `,
                image: 'https://picsum.photos/800/200?random=10',
                category: '安全培训',
                publishTime: '2024-12-14 13:45:00',
                tags: ['安全培训', '生产安全', '培训会议']
            },
            '8': {
                title: '客户满意度调研报告',
                content: `
                    <h2>客户满意度调研成果</h2>
                    <p>本季度客户满意度调研已完成，整体满意度达到95%。</p>

                    <h3>调研结果</h3>
                    <p>• 产品质量满意度：96%</p>
                    <p>• 服务质量满意度：94%</p>
                    <p>• 响应速度满意度：93%</p>

                    <p>感谢客户的信任与支持！</p>
                `,
                image: 'https://picsum.photos/800/200?random=11',
                category: '业务报告',
                publishTime: '2024-12-13 16:10:00',
                tags: ['客户满意度', '调研报告', '业务成果']
            },
            '9': {
                title: '新产品研发进展更新',
                content: `
                    <h2>智能办公系统2.0研发进展</h2>
                    <p>智能办公系统2.0版本研发进展顺利，预计明年Q1发布。</p>

                    <h3>新功能亮点</h3>
                    <p>• AI智能助手</p>
                    <p>• 语音识别功能</p>
                    <p>• 自动化工作流</p>

                    <p>敬请期待新版本的发布！</p>
                `,
                image: 'https://picsum.photos/800/200?random=12',
                category: '产品动态',
                publishTime: '2024-12-12 10:25:00',
                tags: ['产品研发', '智能办公', '新功能']
            },
            '10': {
                title: '员工健康体检安排',
                content: `
                    <h2>年度健康体检通知</h2>
                    <p>为关爱员工健康，公司安排年度健康体检，请按时参加。</p>

                    <h3>体检时间</h3>
                    <p>2024年12月25日-29日</p>

                    <h3>体检项目</h3>
                    <p>• 常规体检</p>
                    <p>• 专科检查</p>
                    <p>• 健康咨询</p>

                    <p>健康是最大的财富，请大家重视体检！</p>
                `,
                image: 'https://picsum.photos/800/200?random=13',
                category: '员工福利',
                publishTime: '2024-12-11 14:00:00',
                tags: ['健康体检', '员工福利', '健康管理']
            },
            '11': {
                title: '技术分享会成功举办',
                content: `
                    <h2>技术分享会圆满结束</h2>
                    <p>本月技术分享会圆满结束，多位技术专家分享了前沿技术。</p>

                    <h3>分享主题</h3>
                    <p>• 人工智能应用</p>
                    <p>• 云计算技术</p>
                    <p>• 数据安全</p>

                    <p>感谢所有参与分享的同事！</p>
                `,
                image: 'https://picsum.photos/800/200?random=14',
                category: '技术交流',
                publishTime: '2024-12-10 17:30:00',
                tags: ['技术分享', '前沿技术', '学习交流']
            },
            '12': {
                title: '环保倡议书',
                content: `
                    <h2>绿色办公倡议</h2>
                    <p>为响应绿色办公理念，公司发起环保倡议，号召全员参与。</p>

                    <h3>倡议内容</h3>
                    <p>• 节约用纸</p>
                    <p>• 节能减排</p>
                    <p>• 垃圾分类</p>

                    <p>让我们共同为环保事业贡献力量！</p>
                `,
                image: 'https://picsum.photos/800/200?random=15',
                category: '企业文化',
                publishTime: '2024-12-09 09:15:00',
                tags: ['环保倡议', '绿色办公', '企业责任']
            },
            '13': {
                title: '财务报销流程优化通知',
                content: `
                    <h2>报销流程优化说明</h2>
                    <p>为简化报销流程，提高效率，财务部门对报销流程进行优化。</p>

                    <h3>优化内容</h3>
                    <p>• 在线提交申请</p>
                    <p>• 自动审批流程</p>
                    <p>• 快速到账服务</p>

                    <p>新流程将大大提高报销效率！</p>
                `,
                image: 'https://picsum.photos/800/200?random=16',
                category: '流程优化',
                publishTime: '2024-12-08 11:40:00',
                tags: ['流程优化', '财务报销', '效率提升']
            },
            '14': {
                title: '团建活动报名开始',
                content: `
                    <h2>年底团建活动邀请</h2>
                    <p>年底团建活动即将开始，精彩活动等你参与，快来报名吧！</p>

                    <h3>活动安排</h3>
                    <p>• 户外拓展</p>
                    <p>• 聚餐联欢</p>
                    <p>• 游戏竞赛</p>

                    <p>期待大家的积极参与！</p>
                `,
                image: 'https://picsum.photos/800/200?random=17',
                category: '员工活动',
                publishTime: '2024-12-07 08:50:00',
                tags: ['团建活动', '员工活动', '团队建设']
            },
            '15': {
                title: '知识产权保护培训',
                content: `
                    <h2>知识产权保护重要性</h2>
                    <p>为提高全员知识产权保护意识，特举办专题培训讲座。</p>

                    <h3>培训内容</h3>
                    <p>• 知识产权法律法规</p>
                    <p>• 专利申请流程</p>
                    <p>• 商标保护策略</p>

                    <p>保护知识产权，就是保护创新成果！</p>
                `,
                image: 'https://picsum.photos/800/200?random=18',
                category: '法律培训',
                publishTime: '2024-12-06 15:00:00',
                tags: ['知识产权', '法律培训', '创新保护']
            }
        };

        // 加载内容数据
        function loadContentData(id, type) {
            // 立即显示加载状态
            const titleElement = document.getElementById('article-title');
            const contentElement = document.getElementById('article-content');

            titleElement.textContent = '加载中...';
            contentElement.innerHTML = '<div style="text-align: center; padding: 20px;"><div style="display: inline-block; width: 20px; height: 20px; border: 2px solid #f3f3f3; border-top: 2px solid #3498db; border-radius: 50%; animation: spin 1s linear infinite;"></div><p style="margin-top: 10px;">正在加载内容...</p></div>';

            // 使用 requestAnimationFrame 优化渲染
            requestAnimationFrame(() => {
                let data;
                if (type === 'banner') {
                    data = bannerData[id];
                } else {
                    data = newsData[id];
                }

                if (data) {
                    // 批量更新DOM以减少重排
                    titleElement.textContent = data.title;
                    contentElement.innerHTML = data.content;
                    contentElement.classList.add('fade-in');
                    document.getElementById('publish-time').textContent = data.publishTime;
                    document.getElementById('category').textContent = data.category;

                    // 延迟加载图片以提高初始加载速度
                    const bannerImg = document.getElementById('banner-image');
                    const img = new Image();
                    img.onload = () => {
                        bannerImg.src = data.image;
                    };
                    img.onerror = () => {
                        bannerImg.style.display = 'none';
                    };
                    img.src = data.image;

                    // 更新标签
                    const tagsContainer = document.getElementById('tags-container');
                    const fragment = document.createDocumentFragment();
                    data.tags.forEach(tag => {
                        const tagElement = document.createElement('span');
                        tagElement.className = 'tag';
                        tagElement.textContent = tag;
                        fragment.appendChild(tagElement);
                    });
                    tagsContainer.innerHTML = '';
                    tagsContainer.appendChild(fragment);

                    // 更新页面标题
                    document.title = data.title;
                } else {
                    titleElement.textContent = '内容未找到';
                    contentElement.innerHTML = '<p>抱歉，请求的内容不存在。</p>';
                }
            });
        }

        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            const id = getUrlParameter('id');
            const type = getUrlParameter('type');
            if (id) {
                loadContentData(id, type);
            }
        });
    </script>
</body>
</html>
