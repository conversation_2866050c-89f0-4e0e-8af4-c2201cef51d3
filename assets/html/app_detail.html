<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>应用详情</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px 20px;
            text-align: center;
            position: relative;
        }

        .app-icon {
            width: 80px;
            height: 80px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            margin: 0 auto 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 36px;
            backdrop-filter: blur(10px);
        }

        .app-title {
            font-size: 28px;
            margin-bottom: 8px;
            font-weight: 600;
        }

        .app-category {
            font-size: 16px;
            opacity: 0.9;
            margin-bottom: 20px;
        }

        .quick-actions {
            display: flex;
            gap: 15px;
            justify-content: center;
        }

        .action-btn {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            font-size: 14px;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .action-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .content {
            padding: 30px 20px;
        }

        .section {
            margin-bottom: 30px;
        }

        .section-title {
            font-size: 20px;
            color: #667eea;
            margin-bottom: 15px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .section-title::before {
            content: '';
            width: 4px;
            height: 20px;
            background: #667eea;
            border-radius: 2px;
        }

        .description {
            font-size: 16px;
            line-height: 1.8;
            color: #555;
            margin-bottom: 20px;
        }

        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .feature-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 12px;
            border: 1px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        .feature-icon {
            width: 40px;
            height: 40px;
            background: #667eea;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
            margin-bottom: 12px;
        }

        .feature-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 8px;
            color: #333;
        }

        .feature-desc {
            font-size: 14px;
            color: #666;
            line-height: 1.5;
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .stat-item {
            text-align: center;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
        }

        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #667eea;
            display: block;
        }

        .stat-label {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }

        .back-button {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.5);
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
            z-index: 1000;
            backdrop-filter: blur(10px);
        }

        .back-button:hover {
            background: rgba(0, 0, 0, 0.7);
        }

        .launch-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            width: 100%;
            margin-top: 20px;
            transition: all 0.3s ease;
        }

        .launch-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }

        @media (max-width: 768px) {
            .container {
                margin: 0;
            }
            
            .header {
                padding: 20px 15px;
            }
            
            .app-title {
                font-size: 24px;
            }
            
            .content {
                padding: 20px 15px;
            }
            
            .features {
                grid-template-columns: 1fr;
            }
            
            .quick-actions {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <button class="back-button" onclick="history.back()">← 返回</button>
    
    <div class="container">
        <div class="header">
            <div class="app-icon" id="app-icon">📅</div>
            <h1 class="app-title" id="app-title">日程管理</h1>
            <p class="app-category" id="app-category">办公协作</p>
            <div class="quick-actions">
                <a href="#" class="action-btn" onclick="launchApp()">立即使用</a>
                <a href="#" class="action-btn" onclick="addToFavorites()">添加收藏</a>
            </div>
        </div>

        <div class="content">
            <div class="section">
                <h2 class="section-title">应用介绍</h2>
                <p class="description" id="app-description">
                    专业的日程管理工具，帮助您高效安排工作和生活。支持多种视图模式，智能提醒功能，让您的时间管理更加轻松便捷。
                </p>
            </div>

            <div class="section">
                <h2 class="section-title">核心功能</h2>
                <div class="features" id="features-container">
                    <!-- 功能卡片将通过JavaScript动态生成 -->
                </div>
            </div>

            <div class="section">
                <h2 class="section-title">使用统计</h2>
                <div class="stats">
                    <div class="stat-item">
                        <span class="stat-number" id="user-count">1,234</span>
                        <span class="stat-label">活跃用户</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number" id="usage-rate">98%</span>
                        <span class="stat-label">满意度</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number" id="update-time">2024.12</span>
                        <span class="stat-label">最新更新</span>
                    </div>
                </div>
            </div>

            <button class="launch-button" onclick="launchApp()">
                🚀 启动应用
            </button>
        </div>
    </div>

    <script>
        // 应用数据
        const appData = {
            '1': {
                name: '日程管理',
                icon: '📅',
                category: '办公协作',
                description: '专业的日程管理工具，帮助您高效安排工作和生活。支持多种视图模式，智能提醒功能，让您的时间管理更加轻松便捷。',
                features: [
                    { icon: '📊', title: '多视图模式', desc: '支持日、周、月视图，灵活查看日程安排' },
                    { icon: '⏰', title: '智能提醒', desc: '自定义提醒时间，重要事项不再遗漏' },
                    { icon: '👥', title: '团队协作', desc: '共享日程，团队协作更高效' },
                    { icon: '📱', title: '多端同步', desc: '手机、电脑实时同步，随时随地管理日程' }
                ],
                stats: { users: '1,234', satisfaction: '98%', lastUpdate: '2024.12' },
                url: 'https://calendar.example.com'
            },
            '2': {
                name: '任务协作',
                icon: '📋',
                category: '办公协作',
                description: '强大的任务管理和团队协作平台，支持项目管理、任务分配、进度跟踪等功能，让团队协作更加高效。',
                features: [
                    { icon: '📈', title: '项目管理', desc: '完整的项目生命周期管理，从规划到交付' },
                    { icon: '👨‍💼', title: '任务分配', desc: '灵活的任务分配机制，明确责任人' },
                    { icon: '📊', title: '进度跟踪', desc: '实时跟踪项目进度，可视化展示' },
                    { icon: '💬', title: '团队沟通', desc: '内置沟通工具，讨论更便捷' }
                ],
                stats: { users: '2,156', satisfaction: '96%', lastUpdate: '2024.12' },
                url: 'https://tasks.example.com'
            },
            '3': {
                name: '文档中心',
                icon: '📄',
                category: '办公协作',
                description: '企业级文档管理系统，支持在线编辑、版本控制、权限管理等功能，让文档管理更加规范化。',
                features: [
                    { icon: '✏️', title: '在线编辑', desc: '支持多人同时编辑，实时保存' },
                    { icon: '🔄', title: '版本控制', desc: '完整的版本历史，随时回滚' },
                    { icon: '🔒', title: '权限管理', desc: '细粒度权限控制，保障文档安全' },
                    { icon: '🔍', title: '全文搜索', desc: '强大的搜索功能，快速定位内容' }
                ],
                stats: { users: '3,421', satisfaction: '97%', lastUpdate: '2024.12' },
                url: 'https://docs.example.com'
            },
            '4': {
                name: '客户管理',
                icon: '👥',
                category: '业务管理',
                description: '专业的客户关系管理系统，帮助企业更好地管理客户信息、跟进销售机会、提升客户满意度。',
                features: [
                    { icon: '📇', title: '客户档案', desc: '完整的客户信息管理，360度客户视图' },
                    { icon: '💰', title: '销售跟进', desc: '销售机会管理，提升成交率' },
                    { icon: '📞', title: '沟通记录', desc: '完整的客户沟通历史记录' },
                    { icon: '📊', title: '数据分析', desc: '客户数据分析，洞察业务趋势' }
                ],
                stats: { users: '856', satisfaction: '95%', lastUpdate: '2024.11' },
                url: 'https://crm.example.com'
            }
        };

        // 获取URL参数
        function getUrlParameter(name) {
            // 优先使用注入的参数
            if (window.urlParams && window.urlParams[name]) {
                return window.urlParams[name];
            }
            // 回退到URL参数
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get(name);
        }

        // 初始化函数，支持参数注入
        function initializeWithParams(params) {
            if (params && params.id) {
                loadAppData(params.id);
            }
        }

        // 启动应用
        function launchApp() {
            const appId = getUrlParameter('id');
            const app = appData[appId];
            if (app) {
                // 在实际应用中，这里会打开对应的应用URL
                alert(`正在启动 ${app.name}...`);
                // window.open(app.url, '_blank');
            }
        }

        // 添加到收藏
        function addToFavorites() {
            const appId = getUrlParameter('id');
            const app = appData[appId];
            if (app) {
                alert(`${app.name} 已添加到收藏夹！`);
            }
        }

        // 加载应用数据
        function loadAppData(appId) {
            const app = appData[appId];

            if (app) {
                // 更新页面内容
                document.getElementById('app-icon').textContent = app.icon;
                document.getElementById('app-title').textContent = app.name;
                document.getElementById('app-category').textContent = app.category;
                document.getElementById('app-description').textContent = app.description;

                // 更新统计数据
                document.getElementById('user-count').textContent = app.stats.users;
                document.getElementById('usage-rate').textContent = app.stats.satisfaction;
                document.getElementById('update-time').textContent = app.stats.lastUpdate;

                // 生成功能卡片
                const featuresContainer = document.getElementById('features-container');
                featuresContainer.innerHTML = '';
                app.features.forEach(feature => {
                    const featureCard = document.createElement('div');
                    featureCard.className = 'feature-card';
                    featureCard.innerHTML = `
                        <div class="feature-icon">${feature.icon}</div>
                        <div class="feature-title">${feature.title}</div>
                        <div class="feature-desc">${feature.desc}</div>
                    `;
                    featuresContainer.appendChild(featureCard);
                });

                // 更新页面标题
                document.title = `${app.name} - 应用详情`;
            } else {
                document.getElementById('app-title').textContent = '应用未找到';
                document.getElementById('app-description').textContent = '抱歉，请求的应用不存在。';
            }
        }

        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            const appId = getUrlParameter('id');
            if (appId) {
                loadAppData(appId);
            }
        });
    </script>
</body>
</html>
